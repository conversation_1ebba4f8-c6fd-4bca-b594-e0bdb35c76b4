# Running batch endpoints

1. Create data asset with your dataset. The dataset should be made of one file per request to the endpoint, and each file should be a `.json` with the keys of the request.
Example request:

```bash
$ cat pr_data/example_000.json
{
  "model": "",
  "messages": [
    {
      "role": "user",
      "content": "<SYSTEM>\nYou are an AI programming assistant that is specialized in applying code..."
    }
  ],
  "speculation": "// Copyright 2013 Square, Inc.\npackage retrofit;\n\nimport java.io.IOException;\n...",
  "temperature": 0,
  "top_p": 1,
  "max_tokens": 10000
}
```

See [convert_pr_data_to_dataframe.py](./test/convert_pr_data_to_dataframe.py) for help on generating this format.

2. Trigger a job in the AML portal.
  a. Go to batch endpoint, e.g. [fastapply](https://ml.azure.com/endpoints/batch/fastapply/detail?wsid=/subscriptions/7b4a614f-3f5e-4896-af31-d1d20477a79c/resourcegroups/zijianjin-rg/providers/Microsoft.MachineLearningServices/workspaces/Instant_apply_training_new&tid=72f988bf-86f1-41af-91ab-2d7cd011db47).
  b. Click "Create job".
  c. Select a deployment, e.g. "llama-full-staticsd".
  d. Select a dataset, e.g. "pr_data version 1".
  e. Click "Create", or customize the output location if desired.
  f. The job will be shown. Wait for it to finish, and after it's done, right-click the output node named "score" to get the blob path.

3. Collect the results. See [EvaluateFastApplyOutputs.ipynb](https://ml.azure.com/fileexplorerAzNB?wsid=/subscriptions/7b4a614f-3f5e-4896-af31-d1d20477a79c/resourcegroups/zijianjin-rg/providers/Microsoft.MachineLearningServices/workspaces/Instant_apply_training_new&tid=72f988bf-86f1-41af-91ab-2d7cd011db47&activeFilePath=Users/bensteenhoek/EvaluateFastApplyOutputs.ipynb) for an example.

```python
import mltable
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential
import pandas as pd
import json

# Instantiate client
ml_client = MLClient.from_config(credential=DefaultAzureCredential())

# Connect to datastore
datastore_name = "workspaceblobstore"
datastore = ml_client.datastores.get(name="workspaceblobstore")
local_path = "predictions.csv"
datastore_path = "azureml/1f2a398f-1c6c-4d10-b632-dc36c9381898/score/predictions.csv"  # Got this path from the output node of the finished job.
ml_client.datastores.mount("workspaceblobstore", "./workspaceblobstore")

# Load file
fpath = "workspaceblobstore/azureml/cf62e79d-1a85-451b-b132-073b9b64b183/score/predictions.csv"
df = pd.read_csv(fpath, names=["input", "output"], sep=" ")
df["input"] = df["input"].apply(json.loads)
df["output"] = df["output"].apply(json.loads)
df
```
