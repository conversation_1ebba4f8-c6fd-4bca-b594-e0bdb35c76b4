"""
RepoLocEval: A framework for evaluating repository localization methods.

This package provides tools for:
- Loading and preprocessing various datasets (SWE-bench, etc.)
- Running different localization methods (direct prediction, agent-based, etc.)
- Evaluating predictions against ground truth
"""

__version__ = "0.1.0"
__author__ = "Your Name"

from .datasets import *
from .methods import *
from .metrics import *
