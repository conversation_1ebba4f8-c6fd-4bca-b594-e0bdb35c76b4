"""SWE-bench dataset implementation."""

from typing import List, Dict, Any, Optional
from datasets import load_dataset
from .base_dataset import BaseDataset


class SWEBenchDataset(BaseDataset):
    """SWE-bench dataset for repository localization evaluation."""
    
    AVAILABLE_CONFIGS = {
        "lite": "princeton-nlp/SWE-bench_Lite_bm25_13K",
        "full": "princeton-nlp/SWE-bench",
        "verified": "princeton-nlp/SWE-bench_Verified"
    }
    
    def __init__(self, variant: str = "lite", config: Optional[Dict[str, Any]] = None):
        """Initialize SWE-bench dataset.
        
        Args:
            variant: Dataset variant ('lite', 'full', 'verified')
            config: Optional configuration dictionary
        """
        super().__init__(f"swebench_{variant}", config)
        self.variant = variant
        self.dataset_name = self.AVAILABLE_CONFIGS.get(variant, variant)
        self._stats = None
    
    def load(self, split: str = "test", limit: Optional[int] = None, **kwargs) -> List[Dict[str, Any]]:
        """Load SWE-bench dataset.
        
        Args:
            split: Dataset split to load
            limit: Optional limit on number of instances
            **kwargs: Additional arguments for load_dataset
            
        Returns:
            List of dataset instances
        """
        print(f"Loading {self.dataset_name} ({split} split)...")
        dataset = load_dataset(self.dataset_name, split=split, **kwargs)
        
        # Convert to list of dictionaries
        data = []
        items = dataset if limit is None else dataset.select(range(min(limit, len(dataset))))
        
        for item in items:
            data.append(dict(item))
        
        self._data = data
        return data
    
    def _extract_locations_from_patch(self, patch: str) -> Dict[str, List[str]]:
        """Extract file, module, and function locations from a patch.
        
        Args:
            patch: Git patch string
            
        Returns:
            Dict with 'files', 'modules', 'functions' lists
        """
        import re
        
        locations = {
            'files': [],
            'modules': [],
            'functions': []
        }
        
        lines = patch.split('\n')
        current_file = None
        current_class = None  # Track the current class context
        
        # Pattern to detect function context from @@ lines
        hunk_pattern = r'@@ -\d+,?\d* \+\d+,?\d* @@ (.+)'
        
        for i, line in enumerate(lines):
            # Track current file from diff header
            if line.startswith('diff --git'):
                match = re.search(r'b/(.*?)(?:\s|$)', line)
                if match:
                    current_file = match.group(1)
                    if current_file not in locations['files']:
                        locations['files'].append(current_file)
                    current_class = None  # Reset class context for new file
    
            # Extract function/class context from hunk headers
            elif line.startswith('@@') and current_file:
                match = re.match(hunk_pattern, line)
                if match:
                    context = match.group(1).strip()
                    
                    # Look for class context first
                    class_match = re.search(r'class\s+(\w+)', context)
                    if class_match:
                        current_class = class_match.group(1)
                        module_path = f"{current_file}:{current_class}"
                        if module_path not in locations['modules']:
                            locations['modules'].append(module_path)
                
                    # Look for function definitions in context
                    func_match = re.search(r'def\s+(\w+)', context)
                    if func_match:
                        func_name = func_match.group(1)
                        
                        # Check if this function is inside a class
                        # The context might be like "class RST(FixedWidth):" followed by methods
                        # Or it might have the class name before the def
                        
                        # Try to find class context in the hunk header
                        # Pattern: "class ClassName" anywhere in the context
                        class_in_context = re.search(r'class\s+(\w+)', context)
                        if class_in_context:
                            current_class = class_in_context.group(1)
                        
                        # Build the function path
                        if current_class and func_name in ['__init__', '__new__', '__call__'] or func_name.startswith('_'):
                            # This is likely a method
                            func_path = f"{current_file}:{current_class}.{func_name}"
                        else:
                            # Could be a standalone function or we need to check the context better
                            # Look for indentation or other clues in nearby lines
                            func_path = f"{current_file}:{func_name}"
                        
                        if func_path not in locations['functions']:
                            locations['functions'].append(func_path)
        
            # Also look for new function/class definitions in the patch content
            elif current_file and (line.startswith('+') or line.startswith('-')) and not line.startswith(('+++', '---')):
                code_line = line[1:]
                
                # Track class definitions to maintain context
                class_match = re.match(r'^class\s+(\w+)', code_line)
                if class_match:
                    current_class = class_match.group(1)
                    module_path = f"{current_file}:{current_class}"
                    if module_path not in locations['modules']:
                        locations['modules'].append(module_path)
                
                # For methods, check indentation to see if we're inside a class
                func_match = re.match(r'^(\s*)def\s+(\w+)', code_line)
                if func_match:
                    indent = func_match.group(1)
                    func_name = func_match.group(2)
                    
                    # If indented and we have a current class, it's a method
                    if indent and current_class:
                        func_path = f"{current_file}:{current_class}.{func_name}"
                    else:
                        # Top-level function or we lost class context
                        func_path = f"{current_file}:{func_name}"
                    
                    if func_path not in locations['functions']:
                        locations['functions'].append(func_path)
    
        return locations

    def preprocess(self, data: List[Dict[str, Any]], 
                   include_patch: bool = True,
                   **kwargs) -> List[Dict[str, Any]]:
        """Preprocess SWE-bench dataset.
        
        Args:
            data: Raw dataset instances
            include_patch: Whether to include patch information
            **kwargs: Additional preprocessing arguments
            
        Returns:
            Preprocessed dataset instances
        """
        processed = []
        
        for item in data:
            processed_item = {
                "instance_id": item.get("instance_id"),
                "repo": item.get("repo", ""),
                "base_commit": item.get("base_commit", ""),
                "problem_statement": item.get("problem_statement", ""),
                "hints_text": item.get("hints_text", ""),
                "created_at": item.get("created_at", ""),
            }
            
            # Extract ground truth from patch
            if "patch" in item:
                locations = self._extract_locations_from_patch(item["patch"])
                processed_item["gt_files"] = locations['files']
                processed_item["gt_modules"] = locations['modules']
                processed_item["gt_functions"] = locations['functions']
                
                if include_patch:
                    processed_item["patch"] = item["patch"]
            else:
                processed_item["gt_files"] = []
                processed_item["gt_modules"] = []
                processed_item["gt_functions"] = []
            
            processed.append(processed_item)
        
        return processed
    
    def get_stats(self) -> Dict[str, Any]:
        """Get dataset statistics.
        
        Returns:
            Dictionary containing dataset statistics
        """
        if not self._data:
            raise ValueError("Dataset not loaded. Call load() first.")
        
        if self._stats is None:
            self._stats = {
                "total_instances": len(self._data),
                "variant": self.variant,
                "dataset_name": self.dataset_name,
                "unique_repos": len(set(item.get("repo", "") for item in self._data)),
                "languages": ["python"],  # SWE-bench is Python-only
                "domains": ["bug_fixing"],
                "avg_problem_length": sum(len(item.get("problem_statement", "")) 
                                         for item in self._data) / len(self._data) if self._data else 0
            }
            
            # Count instances by repo
            repo_counts = {}
            for item in self._data:
                repo = item.get("repo", "unknown")
                repo_counts[repo] = repo_counts.get(repo, 0) + 1
            
            self._stats["repo_distribution"] = repo_counts
        
        return self._stats
    
    def filter_by_repo(self, data: List[Dict[str, Any]], repos: List[str]) -> List[Dict[str, Any]]:
        """Filter dataset by repository names.
        
        Args:
            data: Dataset instances
            repos: List of repository names to include
            
        Returns:
            Filtered dataset instances
        """
        return [item for item in data if item.get("repo") in repos]
    
    def filter_by_date_range(self, data: List[Dict[str, Any]], 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Filter dataset by creation date range.
        
        Args:
            data: Dataset instances
            start_date: Start date (ISO format)
            end_date: End date (ISO format)
            
        Returns:
            Filtered dataset instances
        """
        filtered = []
        
        for item in data:
            created_at = item.get("created_at", "")
            if not created_at:
                continue
                
            if start_date and created_at < start_date:
                continue
            if end_date and created_at > end_date:
                continue
                
            filtered.append(item)
        
        return filtered
