{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import azure.ai.ml._artifacts._artifact_utilities as artifact_utils\n", "import pandas as pd\n", "import json\n", "\n", "def convert(df):\n", "    df[\"input\"] = df[\"input\"].apply(json.loads)\n", "    df[\"output\"] = df[\"output\"].apply(json.loads)\n", "    df[\"elapsed\"] = df[\"output\"].apply(lambda d: d.get(\"elapsed\"))\n", "    df[\"output_text\"] = df[\"output\"].apply(lambda d: d[\"choices\"][0][\"message\"][\"content\"] if \"choices\" in d else None)\n", "    df[\"char_len\"] = df[\"output_text\"].apply(lambda s: len(s) if s is not None else None)\n", "    df = df[df[\"output_text\"].notna()]\n", "    return df\n", "def load(name):\n", "    artifact_utils.download_artifact_from_aml_uri(uri = ml_client.data.get(name, version=\"1\").path, destination = f\"/tmp/{name}/\", datastore_operation=ml_client.datastores)\n", "    df = pd.read_csv(f\"/tmp/{name}/predictions.csv\", sep=\" \")\n", "    df = convert(df)\n", "    return df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Found the config file in: /config.json\n"]}], "source": ["# Attach to mlclient\n", "import mltable\n", "from azure.ai.ml import MLClient\n", "from azure.identity import DefaultAzureCredential\n", "\n", "ml_client = MLClient.from_config(credential=DefaultAzureCredential())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mean(Baseline/SD): 37.40666365640148\n", "sum(Baseline)/sum(SD): 6.464800437365588\n", "Mean SD elapsed: 3.854070457617442 seconds\n"]}], "source": ["non_sd_df = load(\"llama-full-non_sd-8xa100-output\")\n", "sd_df = pd.read_csv(\"/home/<USER>/cloudfiles/code/Users/<USER>/FastApply/sd_predictions.csv\", sep=\" \")\n", "sd_df = convert(sd_df)\n", "\n", "\n", "orig_sd_df = sd_df\n", "orig_non_sd_df = non_sd_df\n", "non_sd_df = non_sd_df[non_sd_df[\"char_len\"]<100*1000]\n", "sd_df = sd_df[sd_df[\"char_len\"]<100*1000]\n", "\n", "# print(\"Exact same between SD and non-SD:\", (sd_df[\"output_text\"] == non_sd_df[\"output_text\"]).mean())\n", "print(\"mean(Baseline/SD):\", (non_sd_df[\"elapsed\"]/ sd_df[\"elapsed\"]).dropna().mean())\n", "print(\"sum(Baseline)/sum(SD):\", non_sd_df[\"elapsed\"].sum()/ sd_df[\"elapsed\"].sum())\n", "print(\"Mean SD elapsed:\", sd_df[\"elapsed\"].mean(), \"seconds\")"]}], "metadata": {"kernelspec": {"display_name": "zijian", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}