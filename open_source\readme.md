# Open Source Project Overview

Welcome to the Open Source repository! This document provides an overview of the datasets, experiments, code, math, and job submission processes.

---

## Table of Contents

- [xTAB](#xtab)
  - [Dataset Stats](#xtab-dataset-stats)
  - [Experiment Results](#xtab-experiment-results)
- [Code](#code)
  - [Dataset Stats](#code-dataset-stats)
  - [Experiment Results](#code-experiment-results)
- [Math](#math)
  - [Dataset Stats](#math-dataset-stats)
  - [Experiment Results](#math-experiment-results)
- [Job Submission](#job-submission)
- [Getting Started](#getting-started)


---

## xTAB

### Dataset Stats

- **Description:**  
  _Add a brief description of the xTAB dataset and its key statistics here._

- **Key Metrics:**  
  - Number of samples: 
  - Features: 
  - Source: 

### Experiment Results

- **Summary:**  
  _Summarize the main findings or results from experiments using the xTAB dataset._

- **Results Table:**  
  | Experiment | Metric 1 | Metric 2 | Notes |
  |------------|----------|----------|-------|
  |            |          |          |       |

---

## Code

### Dataset Stats

- **Description:**  
  _Describe the code-related datasets and their statistics._

### Experiment Results

- **Summary:**  
  _Summarize experiment results related to code datasets._

---

## Math

### Dataset Stats





- **Description:**  
  _Describe the math-related datasets and their statistics._



### Experiment Results

- **Summary:**  
- excel link: https://microsoft-my.sharepoint.com/:x:/p/v-chenglong/ETpZRsAyz2FJr-7onBMMmcgBpVHCivqA4fd-hSBexFKloA?wdOrigin=TEAMS-MAGLEV.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1750799895362&web=1
- prompting with qwen30BA3B best is 93.10%.
  - inference dataset:1319
- fine-tuning: (the best is about 80%, which is bad)
  - trainiing: ~8k
- prompting (vanlina) is doing a lot of reasoning, while fine-tuning (both fft and LoRA) is doing short reasoning. 
  - due to the fine-tuning dataset is not including the reasoning.
- error analysis: https://github.com/ai-platform-microsoft/aims-tscience-nl2code/issues/3028
```
```

---

## Job Submission

### Azure ML Singularity Setup

1. **Install dependencies:**
   ```bash
   pip install azure-ai-ml azure-identity
   ```

2. **Configure authentication:**
   - Set up Azure credentials
   - Place HuggingFace token in `./tokens/.hf.txt`

3. **Submit training job:**
   ```bash
   python yuhu_singularity.py --cluster h100centralusvc
   ```

### Key Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `LORA` | `True` | Enable LoRA fine-tuning |
| `MODEL_NAME` | `Qwen/Qwen3-30B-A3B` | Base model |
| `CUTOFF_LEN` | `10000` | Max token length |
| `BATCH_SIZE` | `2` | Training batch size |
| `INSTANCE_COUNT` | `2` | Number of compute instances |
---

## Getting Started

1. **Clone the repository:**  
   ```zsh
   git clone <repo-url>
   cd <repo-directory>
   ```

2. **Install dependencies:**  
   ```zsh
   pip install -r requirements.txt
   ```

3. **Run an example:**  
   _Add example commands or scripts here._

---

## Next Steps

### Immediate Priorities

- [ ] **Dataset Validation** - Find open dataset to validate fine-tuning effectiveness *(Owner: Cheng)*
- [ ] **Documentation** - Update README with detailed job submission instructions
- [ ] **xTAB Focus** - Prioritize xTAB dataset experiments
- [ ] **Evaluation Pipeline**:
  - [ ] Batch evaluation implementation
  - [ ] Endpoint evaluation setup

## FAQ

### ❓ Common Issues

**Q: "Job submitted with SLA Standard cannot be run in interactive mode"**

**A:** Interactive services require Premium SLA tier. Choose one of:

1. **Enable Premium SLA** (Recommended for development):
   ```python
  "interactive": True,
   "slaTier": "Premium"  # Enables JupyterLab, VSCode, SSH
   ```

2. **Disable Interactive Mode** (testing):
   ```python
   "interactive": False,
   "slaTier": "Standard"  # Remove services section
   ```

**Q: How do I switch between LoRA and full fine-tuning?**

**A:** Modify the configuration in your script:
```python
LORA = True   # For LoRA fine-tuning
LORA = False  # For full fine-tuning
```

**Q: How do I adjust token length for memory constraints?**

**A:** Reduce the `CUTOFF_LEN` parameter:
```python
CUTOFF_LEN = 8000  # Reduce from default 10000
```
