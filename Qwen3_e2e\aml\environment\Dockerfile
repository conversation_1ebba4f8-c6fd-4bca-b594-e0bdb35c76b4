ARG BASE_IMAGE=mcr.microsoft.com/azureml/openmpi5.0-cuda12.4-ubuntu22.04
FROM ${BASE_IMAGE}

# Define environments
# Requires 16 cores and 128GB memory
ENV MAX_JOBS=16
ENV FLASH_ATTENTION_FORCE_BUILD=TRUE
ENV VLLM_WORKER_MULTIPROC_METHOD=spawn

# Define installation arguments
ARG INSTALL_BNB=false
ARG INSTALL_VLLM=false
ARG INSTALL_DEEPSPEED=false
ARG INSTALL_FLASHATTN=false
ARG INSTALL_LIGER_KERNEL=false
ARG INSTALL_HQQ=false
ARG INSTALL_EETQ=false
ARG PIP_INDEX=https://pypi.org/simple

# Set the working directory
WORKDIR /app

# Install the requirements
COPY requirements.txt /app
RUN pip config set global.index-url "$PIP_INDEX" && \
    pip config set global.extra-index-url "$PIP_INDEX" && \
    python -m pip install --upgrade pip && \
    pip install -r requirements.txt --no-cache-dir

RUN pip uninstall -y transformer-engine flash-attn && \
    pip uninstall -y ninja && pip install ninja && ninja --version \
    pip install --no-cache-dir flash-attn --no-build-isolation

RUN pip install deepspeed liger-kernel bitsandbytes>=0.45.0 autoawq

COPY ./vendor/vllm-private /vllm
RUN pip install '/vllm'

# Copy the rest of the application into the image
COPY . /app

# Install the LLaMA Factory
RUN pip install -e ".[metrics]"

# Inference requirements
ENV SVDIR=/var/runit
ENV WORKER_TIMEOUT=400
EXPOSE 5001 8883 8888

# support Deepspeed launcher requirement of passwordless ssh login
RUN apt-get update
RUN apt-get install -y openssh-server openssh-client
