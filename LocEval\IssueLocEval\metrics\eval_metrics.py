"""Comprehensive evaluation metrics for repository localization."""

import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import torch
from torch import Tensor
from datasets import load_dataset
import collections
import re
import logging
import pdb


# Filtered instances that should be excluded from evaluation
FILTERED_INSTANCES = [
]


class MetricCalculator:
    """Calculate various metrics for localization evaluation."""
    
    @staticmethod
    def _dcg(target: Tensor) -> Tensor:
        """Discounted Cumulative Gain."""
        batch_size, k = target.shape
        rank_positions = torch.arange(1, k + 1, dtype=torch.float32, device=target.device).tile((batch_size, 1))
        return (target / torch.log2(rank_positions + 1)).sum(dim=-1)
    
    @staticmethod
    def div_no_nan(a: Tensor, b: Tensor, na_value: float = 0.) -> Tensor:
        """Division with NaN handling."""
        return (a / b).nan_to_num_(nan=na_value, posinf=na_value, neginf=na_value)
    
    def normalized_dcg(self, pred_target: Tensor, ideal_target: Tensor, k: Optional[int] = None) -> Tensor:
        """Normalized Discounted Cumulative Gain."""
        pred_target = pred_target[:, :k]
        ideal_target = ideal_target[:, :k]
        return self.div_no_nan(self._dcg(pred_target), self._dcg(ideal_target)).mean(0)
    
    def recall_at_k(self, pred_target: Tensor, ideal_target: Tensor, k: Optional[int] = None) -> Tensor:
        """Recall@k metric."""
        pred_target = pred_target[:, :k]
        relevant = (pred_target == 1).sum(dim=-1)
        total_relevant = (ideal_target == 1).sum(dim=-1)
        recall = self.div_no_nan(relevant, total_relevant, na_value=0.)
        return recall.mean(0)
    
    def acc_at_k(self, pred_target: Tensor, ideal_target: Tensor, k: Optional[int] = None) -> Tensor:
        """Accuracy@k metric - percentage of instances with at least one hit in top-k."""
        pred_target = pred_target[:, :k]  # 只考虑前 k 个预测结果
        ideal_target = ideal_target[:, :k]
        
        relevant = (pred_target == 1).sum(dim=-1)  # 计算预测中相关文档的个数
        total_relevant = (ideal_target == 1).sum(dim=-1)  # 计算所有相关文档的个数

        comparison = relevant == total_relevant
        return comparison.sum()/relevant.shape[0]
    
    def precision_at_k(self, pred_target: Tensor, ideal_target: Tensor, k: Optional[int] = None) -> Tensor:
        """Precision@k metric."""
        pred_target = pred_target[:, :k]
        relevant = (pred_target == 1).sum(dim=-1)
        precision = relevant / k
        return precision.mean(0)
    
    def average_precision_at_k(self, pred_target: Tensor, ideal_target: Tensor, k: Optional[int] = None) -> Tensor:
        """Mean Average Precision@k metric."""
        batch_size, _ = pred_target.shape
        pred_target = pred_target[:, :k]
        
        precisions = []
        for i in range(batch_size):
            ap = 0.0
            relevant_count = 0
            for j in range(k):
                if pred_target[i, j] == 1:
                    relevant_count += 1
                    ap += relevant_count / (j + 1)
            ap = ap / k
            precisions.append(ap)
        
        return torch.tensor(precisions).mean()


class LocalizationEvaluator:
    """Main evaluator for localization predictions."""
    
    def __init__(self):
        self.calculator = MetricCalculator()
        self.metric_funcs = {
            'ndcg': self.calculator.normalized_dcg,
            'recall': self.calculator.recall_at_k,
            'acc': self.calculator.acc_at_k,
            'precision': self.calculator.precision_at_k,
            'map': self.calculator.average_precision_at_k
        }
        self.metric_names = {
            'ndcg': 'NDCG',
            'recall': 'Recall',
            'acc': 'Acc',
            'precision': 'P',
            'map': 'MAP'
        }
    
    def is_path_match(self, pred: str, gt: str) -> bool:
        """
        Check if prediction matches ground truth path using substring matching.
        
        Args:
            pred: Predicted path
            gt: Ground truth path
            
        Returns:
            True if pred matches gt via substring matching
        """
        # Case 1: pred is a substring of gt path
        # e.g., pred="messages/storage/cookie.py", gt="django/contrib/messages/storage/cookie.py"
        if pred in gt:
            return True
        
        # Case 2: gt path is a substring of pred
        # e.g., pred="django/contrib/messages/storage/cookie.py", gt="messages/storage/cookie.py"
        if gt in pred:
            return True
        
        # Case 3: Both end with the same filename (for very short GT paths)
        # e.g., pred="path/to/file.py", gt="file.py"
        pred_filename = pred.split('/')[-1]
        gt_filename = gt.split('/')[-1]
        if pred_filename == gt_filename and len(gt_filename) > 3:  # Avoid matching very short names
            return True
        
        return False
    
    def load_predictions(self, file_path: str, key: str = 'found_files') -> Dict[str, List[str]]:
        """Load predictions from JSONL file."""
        predictions = {}
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line)
                predictions[data['instance_id']] = data.get(key, [])
        return predictions
    
    def load_ground_truth_from_data(self, data: List[Dict[str, Any]], level: str) -> Dict[str, List[str]]:
        """Load ground truth from preprocessed data.
        
        Args:
            data: Preprocessed dataset with gt_files, gt_modules, gt_functions
            level: Evaluation level ('file', 'module', 'function')
            
        Returns:
            Dictionary mapping instance_id to ground truth locations
        """
        gt_dict = {}
        
        level_to_key = {
            'file': 'gt_files',
            'module': 'gt_modules', 
            'function': 'gt_functions'
        }
        
        key = level_to_key.get(level)
        if not key:
            raise ValueError(f"Invalid level: {level}")
        
        for instance in data:
            instance_id = instance['instance_id']
            gt_dict[instance_id] = instance.get(key, [])
        
        return gt_dict
    
    def calculate_metrics(
        self,
        predictions: Dict[str, List[str]],
        ground_truth: Dict[str, List[str]],
        k_values: List[int],
        metrics: List[str] = ['acc', 'ndcg', 'precision', 'recall', 'map'],
        filter_list: Optional[List[str]] = None,
        selected_list: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """Calculate various metrics."""
        calculator = MetricCalculator()
        results = {}
        
        # Get intersection - only evaluate instances that have both predictions and ground truth
        instance_ids = list(set(predictions.keys()) & set(ground_truth.keys()))

        if filter_list:
            instance_ids = [id for id in instance_ids if id not in filter_list]
        if selected_list:
            instance_ids = [id for id in instance_ids if id in selected_list]
        
        # Skip if no instances to evaluate
        if not instance_ids:
            return {f"{metric}@{k}": 0.0 for metric in metrics for k in k_values}
        
        # Prepare binary matrices for evaluation
        max_k = max(k_values) if k_values else 10
        pred_matrix = []
        gt_matrix = []
        
        for instance_id in instance_ids:
            preds = predictions.get(instance_id, [])
            gts = ground_truth.get(instance_id, [])
            
            # Skip instances with no ground truth
            if not gts:
                continue
            
            # Create binary vector for predictions using flexible path matching
            pred_row = []
            for pred in preds[:max_k]:  # Limit to max_k predictions
                # Check if pred matches any gt using the instance method
                found = any(self.is_path_match(pred, gt) for gt in gts)
                pred_row.append(1.0 if found else 0.0)
            
            # Pad to max_k if needed
            while len(pred_row) < max_k:
                pred_row.append(0.0)
            
            pred_matrix.append(pred_row[:max_k])  # Ensure exactly max_k elements
            
            # Ground truth is always 1 for existing items, padded with 0s
            gt_row = [1.0] * min(len(gts), max_k) + [0.0] * (max_k - min(len(gts), max_k))
            gt_matrix.append(gt_row)
        
        # Skip if no valid instances
        if not pred_matrix:
            return {f"{metric}@{k}": 0.0 for metric in metrics for k in k_values}
        
        # Convert to tensors
        pred_tensor = torch.tensor(pred_matrix)
        gt_tensor = torch.tensor(gt_matrix)
        
        # Calculate metrics
        metric_funcs = {
            'acc': calculator.acc_at_k,
            'ndcg': calculator.normalized_dcg,
            'precision': calculator.precision_at_k,
            'recall': calculator.recall_at_k,
            'map': calculator.average_precision_at_k
        }
        
        for metric in metrics:
            if metric not in metric_funcs:
                continue
            
            metric_func = metric_funcs[metric]
            
            for k in k_values:
                if metric == 'map':
                    # MAP doesn't use k parameter
                    value = metric_func(pred_tensor, gt_tensor, k=max_k)
                    results[f"{metric}"] = float(value)
                    break
                else:
                    value = metric_func(pred_tensor, gt_tensor, k=k)
                    results[f"{metric}@{k}"] = float(value)
        
        return results
    
    def evaluate(
        self,
        prediction_file: str,
        data: Optional[List[Dict[str, Any]]] = None,
        level2key: Optional[Dict[str, str]] = None,
        k_values_list: Optional[List[List[int]]] = None,
        metrics: List[str] = ['acc', 'ndcg', 'precision', 'recall', 'map'],
        selected_list: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Main evaluation function.
        
        Args:
            prediction_file: Path to predictions JSONL file
            dataset: HuggingFace dataset name (if loading fresh)
            data: Preprocessed data with ground truth (if already loaded)
            split: Dataset split
            level2key: Mapping of levels to prediction keys
            k_values_list: K values for each level
            metrics: Metrics to calculate
            selected_list: Specific instances to evaluate
            
        Returns:
            DataFrame with evaluation results
        """
        if level2key is None:
            level2key = {
                'file': 'found_files',
                'module': 'found_modules',
                'function': 'found_entities'
            }
        
        if k_values_list is None:
            k_values_list = [
                [1, 3, 5, 10],  # file level
                [1, 3, 5, 10],    # module level
                [1, 3, 5, 10]     # function level
                # [5],
                # [5, 10],
                # [5]
            ]
        
        results = []
        levels = ['file', 'module', 'function']
        
        for i, level in enumerate(levels):
            # Load predictions
            predictions = self.load_predictions(prediction_file, level2key[level])
            
            # Load ground truth
            ground_truth = self.load_ground_truth_from_data(data, level) if data is not None else {}
           
            # Calculate metrics
            level_results = self.calculate_metrics(
                predictions,
                ground_truth,
                k_values_list[i],
                metrics,
                filter_list=FILTERED_INSTANCES,
                selected_list=selected_list
            )
            
            results.append(pd.DataFrame(level_results, index=[0]))
        
        # Combine results
        return pd.concat(results, axis=1, keys=levels)
    
    def label_results(
        self,
        prediction_file: str,
        data: List[Dict[str, Any]],
        k: int = 5,
        level2key: Optional[Dict[str, str]] = None
    ) -> Dict[str, bool]:
        """Label each instance as success/failure based on predictions vs ground truth.
        
        Success requires ALL ground truth items (files, modules, and functions) to be found
        in the top-k predictions at their respective levels.
        
        Args:
            prediction_file: Path to predictions JSONL file
            data: Preprocessed dataset with ground truth
            k: Top-k predictions to consider
            level2key: Mapping of levels to prediction keys
            
        Returns:
            Dictionary mapping instance_id to success (True) or failure (False)
        """
        if level2key is None:
            level2key = {
                'file': 'found_files',
                'module': 'found_modules',
                'function': 'found_entities'
            }
        
        # Load all predictions
        predictions_by_level = {}
        for level, key in level2key.items():
            predictions_by_level[level] = self.load_predictions(prediction_file, key)
        
        # Load ground truth for all levels
        ground_truth_by_level = {}
        for level in ['file', 'module', 'function']:
            ground_truth_by_level[level] = self.load_ground_truth_from_data(data, level)
        
        # Get all instance IDs from data
        instance_ids = [item['instance_id'] for item in data]
        
        # Label each instance
        labels = {}
        
        for instance_id in instance_ids:
            # Start with assumption of success
            success = True
            
            # Check each level
            for level in ['file', 'module', 'function']:
                # Get predictions and ground truth
                preds = predictions_by_level[level].get(instance_id, [])
                gts = ground_truth_by_level[level].get(instance_id, [])
                
                # If there are ground truth items, check if all are found
                if gts:
                    if not self._check_all_found(preds, gts, k):
                        success = False
                        break  # No need to check other levels
            
            labels[instance_id] = success
        
        return labels
    
    def _check_all_found(self, pred_list: List[str], gt_list: List[str], k: int) -> bool:
        """Check if predictions match ground truth based on k and |GT| relationship.
        
        Args:
            pred_list: List of predictions
            gt_list: List of ground truth items
            k: Top-k predictions to consider
            
        Returns:
            True if success based on the logic:
            - If k >= |GT|: All GT items must be found in top-k predictions
            - If k < |GT|: All top-k predictions must be in GT
        """
        # Ensure pred_list contains only strings (flatten if needed)
        flattened_preds = []
        for pred in pred_list[:k]:
            if isinstance(pred, list):
                flattened_preds.extend(pred)
            else:
                flattened_preds.append(pred)
        
        pred_list = flattened_preds[:k]
        
        if k >= len(gt_list):
            # Check if all GT items are found in predictions
            for gt in gt_list:
                found = any(self.is_path_match(pred, gt) for pred in pred_list)
                if not found:
                    return False
            return True
        else:
            # k < |GT|: Check if all k predictions are in GT
            if len(pred_list) < k:
                return False  # Not enough predictions
            
            for pred in pred_list:
                found = any(self.is_path_match(pred, gt) for gt in gt_list)
                if not found:
                    return False
            return True
