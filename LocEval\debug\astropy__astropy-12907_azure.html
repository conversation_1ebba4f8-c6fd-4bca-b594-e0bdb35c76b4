
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Trajectory Visualization - Unknown</title>
            </head>
            <body>
                
        <style>
            .traj-container {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                background: #f5f5f5;
                padding: 20px;
            }
            .message-block {
                margin: 15px 0;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .user-message {
                background: #e3f2fd;
                border-left: 4px solid #2196F3;
            }
            .assistant-message {
                background: #f3e5f5;
                border-left: 4px solid #9C27B0;
            }
            .tool-call {
                background: #fff3e0;
                border-left: 4px solid #FF9800;
            }
            .tool-response {
                background: #e8f5e9;
                border-left: 4px solid #4CAF50;
            }
            .message-header {
                padding: 12px 16px;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
            }
            .message-content {
                padding: 0 16px 16px 16px;
                white-space: pre-wrap;
                font-size: 14px;
                line-height: 1.5;
            }
            .collapsible {
                background: #f0f0f0;
                padding: 8px 12px;
                margin: 8px 0;
                border-radius: 4px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .collapsible:hover {
                background: #e0e0e0;
            }
            .collapsed-content {
                display: none;
                padding: 12px;
                background: white;
                border-radius: 4px;
                margin-top: 8px;
                border: 1px solid #ddd;
            }
            .tool-args {
                background: #f5f5f5;
                padding: 8px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                overflow-x: auto;
            }
            .metadata {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .step-number {
                background: #666;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
            }
            .toggle-btn {
                background: #2196F3;
                color: white;
                border: none;
                padding: 4px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            }
            .toggle-btn:hover {
                background: #1976D2;
            }
            code {
                background: #f5f5f5;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: monospace;
            }
            .observation-preview {
                background: #fafafa;
                padding: 8px;
                border-radius: 4px;
                margin-top: 8px;
                font-size: 13px;
                color: #666;
            }
            .debug-info {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .label-success {
                background: #4CAF50;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            .label-failure {
                background: #f44336;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            .predictions-list, .ground-truth-list {
                margin: 10px 0;
                padding-left: 20px;
            }
            .raw-output-section {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-top: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .tools-section {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-top: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .tool-definition {
                background: #f9f9f9;
                padding: 12px;
                margin: 8px 0;
                border-radius: 6px;
                border-left: 3px solid #2196F3;
            }
        </style>
        
        <script>
        function toggleContent(id) {
            var content = document.getElementById(id);
            var button = document.getElementById('btn-' + id);
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                button.textContent = 'Hide';
            } else {
                content.style.display = 'none';
                button.textContent = 'Show';
            }
        }
        
        function toggleAll(show) {
            var contents = document.getElementsByClassName('collapsed-content');
            var buttons = document.getElementsByClassName('toggle-btn');
            for (var i = 0; i < contents.length; i++) {
                contents[i].style.display = show ? 'block' : 'none';
                buttons[i].textContent = show ? 'Hide' : 'Show';
            }
        }
        
        // Initialize all collapsed content to be hidden
        window.onload = function() {
            var contents = document.getElementsByClassName('collapsed-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].style.display = 'none';
            }
        }
        </script>
        <div class="traj-container">
            <div class="metadata">
                <h2>Trajectory Metadata</h2>
                <p><strong>Total Tokens:</strong> 476,945 
                   (Prompt: 475,731, Completion: 1,214)</p>
                <p><strong>Execution Time:</strong> 46.50s</p>
                <p><strong>Total Messages:</strong> 30</p>
                <button class="toggle-btn" onclick="toggleAll(true)">Expand All</button>
                <button class="toggle-btn" onclick="toggleAll(false)">Collapse All</button>
            </div>
            
            <div class="debug-info">
                <h2>Debug Information</h2>
                <p><strong>Instance ID:</strong> astropy__astropy-12907</p>
                <p><strong>Label:</strong> <span class="label-failure">FAILURE</span></p>
                
                <h3>Predictions</h3>
                <div class="predictions-list">
            <p><strong>found_files:</strong></p><ul><li><code>[&#39;astropy/modeling/separable.py&#39;, &#39;astropy/modeling/core.py&#39;]</code></li></ul><p><strong>found_modules:</strong></p><ul><li><code>[&#39;astropy/modeling/separable.py:separability_matrix&#39;, &#39;astropy/modeling/separable.py:_separable&#39;, &#39;astropy/cosmology/flrw.py:FLRW&#39;, &#39;astropy/extern/ply/yacc.py:ParserReflect&#39;, &#39;astropy/modeling/core.py:CompoundModel&#39;, &#39;astropy/timeseries/periodograms/bls/core.py:BoxLeastSquaresResults&#39;, &#39;astropy/extern/ply/yacc.py:Grammar&#39;]</code></li></ul><p><strong>found_entities:</strong></p><ul><li><code>[&#39;astropy/modeling/separable.py:separability_matrix&#39;, &#39;astropy/modeling/separable.py:_separable&#39;, &#39;astropy/cosmology/flrw.py:FLRW.critical_density0&#39;, &#39;astropy/extern/ply/yacc.py:ParserReflect.validate_tokens&#39;, &#39;astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists&#39;, &#39;astropy/timeseries/periodograms/bls/core.py:BoxLeastSquaresResults.__getattr__&#39;, &#39;astropy/extern/ply/yacc.py:Grammar.set_precedence&#39;]</code></li></ul>
                </div>
                
                <h3>Ground Truth</h3>
                <div class="ground-truth-list">
            <p><strong>gt_files:</strong></p><ul><li><code>astropy/modeling/separable.py</code></li></ul><p><strong>gt_modules:</strong></p><ul><li><code>astropy/modeling/separable.py:_cstack</code></li></ul><p><strong>gt_functions:</strong></p><ul><li><code>astropy/modeling/separable.py:_cstack</code></li></ul>
                </div>
            </div>
            <div class="message-block user-message"><div class="message-header">👤 User Input <span class="step-number">Step 1</span></div><div class="message-content">
Given the following GitHub problem description, your objective is to localize the specific files, classes or functions, and lines of code that need modification or contain key information to resolve the issue.

Follow these steps to localize the issue:
## Step 1: Categorize and Extract Key Problem Information
 - Classify the problem statement into the following categories:
    Problem description, error trace, code to reproduce the bug, and additional context.
 - Identify modules in the &#39;astrop...</div><div class="collapsible" onclick="toggleContent('user-1')"><span>Show full content (4475 chars)</span><button id="btn-user-1" class="toggle-btn">Show</button></div><div id="user-1" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: inherit; font-size: 14px; margin: 0;">
Given the following GitHub problem description, your objective is to localize the specific files, classes or functions, and lines of code that need modification or contain key information to resolve the issue.

Follow these steps to localize the issue:
## Step 1: Categorize and Extract Key Problem Information
 - Classify the problem statement into the following categories:
    Problem description, error trace, code to reproduce the bug, and additional context.
 - Identify modules in the &#39;astropy&#39; package mentioned in each category.
 - Use extracted keywords and line numbers to search for relevant code references for additional context.

## Step 2: Locate Referenced Modules
- Accurately determine specific modules
    - Explore the repo to familiarize yourself with its structure.
    - Analyze the described execution flow to identify specific modules or components being referenced.
- Pay special attention to distinguishing between modules with similar names using context and described execution flow.
- Output Format for collected relevant modules:
    - Use the format: &#39;file_path:QualifiedName&#39;
    - E.g., for a function `calculate_sum` in the `MathUtils` class located in `src/helpers/math_helpers.py`, represent it as: &#39;src/helpers/math_helpers.py:MathUtils.calculate_sum&#39;.

## Step 3: Analyze and Reproducing the Problem
- Clarify the Purpose of the Issue
    - If expanding capabilities: Identify where and how to incorporate new behavior, fields, or modules.
    - If addressing unexpected behavior: Focus on localizing modules containing potential bugs.
- Reconstruct the execution flow
    - Identify main entry points triggering the issue.
    - Trace function calls, class interactions, and sequences of events.
    - Identify potential breakpoints causing the issue.
    Important: Keep the reconstructed flow focused on the problem, avoiding irrelevant details.

## Step 4: Locate Areas for Modification
- Locate specific files, functions, or lines of code requiring changes or containing critical information for resolving the issue.
- Consider upstream and downstream dependencies that may affect or be affected by the issue.
- If applicable, identify where to introduce new fields, functions, or variables.
- Think Thoroughly: List multiple potential solutions and consider edge cases that could impact the resolution.

## Output Format for Final Results:
Your final output should list the locations requiring modification, wrapped with triple backticks ```
Each location should include the file path, class name (if applicable), function name, or line numbers, ordered by importance.
Your answer would better include about 5 files.

### Examples:
```
full_path1/file1.py
line: 10
class: MyClass1
function: my_function1

full_path2/file2.py
line: 76
function: MyClass2.my_function2

full_path3/file3.py
line: 24
line: 156
function: my_function3
```

Return just the location(s)

Note: Your thinking should be thorough and so it&#39;s fine if it&#39;s very long.

--- BEGIN PROBLEM STATEMENT ---
Title: Modeling&#39;s `separability_matrix` does not compute separability correctly for nested CompoundModels

Consider the following model:



```python

from astropy.modeling import models as m

from astropy.modeling.separable import separability_matrix



cm = m.Linear1D(10) &amp; m.Linear1D(5)

```



It&#39;s separability matrix as you might expect is a diagonal:



```python

&gt;&gt;&gt; separability_matrix(cm)

array([[ True, False],

       [False,  True]])

```



If I make the model more complex:

```python

&gt;&gt;&gt; separability_matrix(m.Pix2Sky_TAN() &amp; m.Linear1D(10) &amp; m.Linear1D(5))

array([[ True,  True, False, False],

       [ True,  True, False, False],

       [False, False,  True, False],

       [False, False, False,  True]])

```



The output matrix is again, as expected, the outputs and inputs to the linear models are separable and independent of each other.



If however, I nest these compound models:

```python

&gt;&gt;&gt; separability_matrix(m.Pix2Sky_TAN() &amp; cm)

array([[ True,  True, False, False],

       [ True,  True, False, False],

       [False, False,  True,  True],

       [False, False,  True,  True]])

```

Suddenly the inputs and outputs are no longer separable?



This feels like a bug to me, but I might be missing something?
--- END PROBLEM STATEMENT ---

IMPORTANT: You should ONLY interact with the environment provided to you AND NEVER ASK FOR HUMAN HELP.
Don&#39;t include any lambda functions!
You should NOT modify any files!
</pre></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (2 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-2-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-2-0" class="toggle-btn">Show</button></div><div id="call-2-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_vj92NCXaPjUPUl2g1mkWEsAv</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "separability_matrix"
  ]
}</div></div><div class="collapsible" onclick="toggleContent('call-2-1')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-2-1" class="toggle-btn">Show</button></div><div id="call-2-1" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_vXtYtlVTArcXRtgyoDIZvdvW</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "CompoundModel"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;separability_matrix&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-3')"><span>View full output (1930 chars)</span><button id="btn-tool-3" class="toggle-btn">Show</button></div><div id="tool-3" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;separability_matrix&quot;...
### Search Result:
Found function `astropy/modeling/separable.py:separability_matrix`.
Source: Match found for entity name `separability_matrix`.
```
 66 | def separability_matrix(transform):
 67 |     &quot;&quot;&quot;
 68 |     Compute the correlation between outputs and inputs.
 69 | 
 70 |     Parameters
 71 |     ----------
 72 |     transform : `~astropy.modeling.core.Model`
 73 |         A (compound) model.
 74 | 
 75 |     Returns
 76 |     -------
 77 |     separable_matrix : ndarray
 78 |         A boolean correlation matrix of shape (n_outputs, n_inputs).
 79 |         Indicates the dependence of outputs on inputs. For completely
 80 |         independent outputs, the diagonal elements are True and
 81 |         off-diagonal elements are False.
 82 | 
 83 |     Examples
 84 |     --------
 85 |     &gt;&gt;&gt; from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
 86 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Scale(1) &amp; Scale(2))
 87 |         array([[ True, False], [False,  True]]...)
 88 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Rotation2D(2))
 89 |         array([[ True,  True], [ True,  True]]...)
 90 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]) | \
 91 |         Polynomial2D(1) &amp; Polynomial2D(2))
 92 |         array([[ True,  True], [ True,  True]]...)
 93 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]))
 94 |         array([[ True, False], [False,  True], [ True, False], [False,  True]]...)
 95 | 
 96 |     &quot;&quot;&quot;
 97 |     if transform.n_inputs == 1 and transform.n_outputs &gt; 1:
 98 |         return np.ones((transform.n_outputs, transform.n_inputs),
 99 |                        dtype=np.bool_)
100 |     separable_matrix = _separable(transform)
101 |     separable_matrix = np.where(separable_matrix != 0, True, False)
102 |     return separable_matrix
```
</pre></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;CompoundModel&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-4')"><span>View full output (3215 chars)</span><button id="btn-tool-4" class="toggle-btn">Show</button></div><div id="tool-4" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;CompoundModel&quot;...
### Search Result:
Found class `astropy/modeling/core.py:CompoundModel`.
Source: Match found for entity name `CompoundModel`.
Just show the structure of this class due to response length limitations:
```
class CompoundModel(Model):

    def __init__(self, op, left, right, name=None):
...
    def _get_left_inputs_from_args(self, args):
...
    def _get_right_inputs_from_args(self, args):
...
    def _get_left_params_from_args(self, args):
...
    def _get_right_params_from_args(self, args):
...
    def _get_kwarg_model_parameters_as_positional(self, args, kwargs):
...
    def _apply_operators_to_value_lists(self, leftval, rightval, **kw):
...
    def evaluate(self, *args, **kw):
...
    @property
    def n_submodels(self):
...
    @property
    def submodel_names(self):
...
    def both_inverses_exist(self):
...
    def _pre_evaluate(self, *args, **kwargs):
...
    @property
    def _argnames(self):
...
    def _post_evaluate(self, inputs, outputs, broadcasted_shapes, with_bbox, **kwargs):
...
    def _evaluate(self, *args, **kw):
...
    @property
    def param_names(self):
...
    def _make_leaflist(self):
...
    def __getattr__(self, name):
...
    def __getitem__(self, index):
...
    def _str_index_to_int(self, str_index):
...
    @property
    def n_inputs(self):
...
    @n_inputs.setter
    def n_inputs(self, value):
...
    @property
    def n_outputs(self):
...
    @n_outputs.setter
    def n_outputs(self, value):
...
    @property
    def eqcons(self):
...
    @eqcons.setter
    def eqcons(self, value):
...
    @property
    def ineqcons(self):
...
    @ineqcons.setter
    def ineqcons(self, value):
...
    def traverse_postorder(self, include_operator=False):
...
    def _format_expression(self, format_leaf=None):
...
    def _format_components(self):
...
    def __str__(self):
...
    def rename(self, name):
...
    @property
    def isleaf(self):
...
    @property
    def inverse(self):
...
    @property
    def fittable(self):
...
    __add__ = _model_oper(&#39;+&#39;)
    __sub__ = _model_oper(&#39;-&#39;)
    __mul__ = _model_oper(&#39;*&#39;)
    __truediv__ = _model_oper(&#39;/&#39;)
    __pow__ = _model_oper(&#39;**&#39;)
    __or__ = _model_oper(&#39;|&#39;)
    __and__ = _model_oper(&#39;&amp;&#39;)

    def _map_parameters(self):
...
    def _initialize_slices(self):
...
    @staticmethod
    def _recursive_lookup(branch, adict, key):
...
    def inputs_map(self):
...
    def _parameter_units_for_data_units(self, input_units, output_units):
...
    @property
    def input_units(self):
...
    @property
    def input_units_equivalencies(self):
...
    @property
    def input_units_allow_dimensionless(self):
...
    @property
    def input_units_strict(self):
...
    @property
    def return_units(self):
...
    def outputs_map(self):
...
    @property
    def has_user_bounding_box(self):
...
    def render(self, out=None, coords=None):
...
    def replace_submodel(self, name, model):
...
    def _set_sub_models_and_parameter_units(self, left, right):
...
    def without_units_for_data(self, **kwargs):
...
    def with_units_from_data(self, **kwargs):

```
Hint: Search `astropy/modeling/core.py:CompoundModel` to get the full content if needed.
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-5-0')"><span>📞 <code>get_entity_contents</code></span><button id="btn-call-5-0" class="toggle-btn">Show</button></div><div id="call-5-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_PCk2pOugnoSgnZYf2KU60ELG</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "entity_names": [
    "astropy/modeling/separable.py:separability_matrix",
    "astropy/modeling/core.py:CompoundModel"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: get_entity_contents</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py:separability_matrix`...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-6')"><span>View full output (53435 chars)</span><button id="btn-tool-6" class="toggle-btn">Show</button></div><div id="tool-6" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py:separability_matrix`...
### Search Result:
Found function `astropy/modeling/separable.py:separability_matrix`.
Source: Exact match found for entity name `astropy/modeling/separable.py:separability_matrix`.
```
 66 | def separability_matrix(transform):
 67 |     &quot;&quot;&quot;
 68 |     Compute the correlation between outputs and inputs.
 69 | 
 70 |     Parameters
 71 |     ----------
 72 |     transform : `~astropy.modeling.core.Model`
 73 |         A (compound) model.
 74 | 
 75 |     Returns
 76 |     -------
 77 |     separable_matrix : ndarray
 78 |         A boolean correlation matrix of shape (n_outputs, n_inputs).
 79 |         Indicates the dependence of outputs on inputs. For completely
 80 |         independent outputs, the diagonal elements are True and
 81 |         off-diagonal elements are False.
 82 | 
 83 |     Examples
 84 |     --------
 85 |     &gt;&gt;&gt; from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
 86 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Scale(1) &amp; Scale(2))
 87 |         array([[ True, False], [False,  True]]...)
 88 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Rotation2D(2))
 89 |         array([[ True,  True], [ True,  True]]...)
 90 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]) | \
 91 |         Polynomial2D(1) &amp; Polynomial2D(2))
 92 |         array([[ True,  True], [ True,  True]]...)
 93 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]))
 94 |         array([[ True, False], [False,  True], [ True, False], [False,  True]]...)
 95 | 
 96 |     &quot;&quot;&quot;
 97 |     if transform.n_inputs == 1 and transform.n_outputs &gt; 1:
 98 |         return np.ones((transform.n_outputs, transform.n_inputs),
 99 |                        dtype=np.bool_)
100 |     separable_matrix = _separable(transform)
101 |     separable_matrix = np.where(separable_matrix != 0, True, False)
102 |     return separable_matrix
```


##Searching for entity `astropy/modeling/core.py:CompoundModel`...
### Search Result:
Found class `astropy/modeling/core.py:CompoundModel`.
Source: Exact match found for entity name `astropy/modeling/core.py:CompoundModel`.
```
2894 | class CompoundModel(Model):
2895 |     &#39;&#39;&#39;
2896 |     Base class for compound models.
2897 | 
2898 |     While it can be used directly, the recommended way
2899 |     to combine models is through the model operators.
2900 |     &#39;&#39;&#39;
2901 | 
2902 |     def __init__(self, op, left, right, name=None):
2903 |         self.__dict__[&#39;_param_names&#39;] = None
2904 |         self._n_submodels = None
2905 |         self.op = op
2906 |         self.left = left
2907 |         self.right = right
2908 |         self._bounding_box = None
2909 |         self._user_bounding_box = None
2910 |         self._leaflist = None
2911 |         self._tdict = None
2912 |         self._parameters = None
2913 |         self._parameters_ = None
2914 |         self._param_metrics = None
2915 | 
2916 |         if op != &#39;fix_inputs&#39; and len(left) != len(right):
2917 |             raise ValueError(
2918 |                 &#39;Both operands must have equal values for n_models&#39;)
2919 |         self._n_models = len(left)
2920 | 
2921 |         if op != &#39;fix_inputs&#39; and ((left.model_set_axis != right.model_set_axis)
2922 |                                    or left.model_set_axis):  # not False and not 0
2923 |             raise ValueError(&quot;model_set_axis must be False or 0 and consistent for operands&quot;)
2924 |         self._model_set_axis = left.model_set_axis
2925 | 
2926 |         if op in [&#39;+&#39;, &#39;-&#39;, &#39;*&#39;, &#39;/&#39;, &#39;**&#39;] or op in SPECIAL_OPERATORS:
2927 |             if (left.n_inputs != right.n_inputs) or \
2928 |                (left.n_outputs != right.n_outputs):
2929 |                 raise ModelDefinitionError(
2930 |                     &#39;Both operands must match numbers of inputs and outputs&#39;)
2931 |             self.n_inputs = left.n_inputs
2932 |             self.n_outputs = left.n_outputs
2933 |             self.inputs = left.inputs
2934 |             self.outputs = left.outputs
2935 |         elif op == &#39;&amp;&#39;:
2936 |             self.n_inputs = left.n_inputs + right.n_inputs
2937 |             self.n_outputs = left.n_outputs + right.n_outputs
2938 |             self.inputs = combine_labels(left.inputs, right.inputs)
2939 |             self.outputs = combine_labels(left.outputs, right.outputs)
2940 |         elif op == &#39;|&#39;:
2941 |             if left.n_outputs != right.n_inputs:
2942 |                 raise ModelDefinitionError(
2943 |                     &quot;Unsupported operands for |: {0} (n_inputs={1}, &quot;
2944 |                     &quot;n_outputs={2}) and {3} (n_inputs={4}, n_outputs={5}); &quot;
2945 |                     &quot;n_outputs for the left-hand model must match n_inputs &quot;
2946 |                     &quot;for the right-hand model.&quot;.format(
2947 |                         left.name, left.n_inputs, left.n_outputs, right.name,
2948 |                         right.n_inputs, right.n_outputs))
2949 | 
2950 |             self.n_inputs = left.n_inputs
2951 |             self.n_outputs = right.n_outputs
2952 |             self.inputs = left.inputs
2953 |             self.outputs = right.outputs
2954 |         elif op == &#39;fix_inputs&#39;:
2955 |             if not isinstance(left, Model):
2956 |                 raise ValueError(&#39;First argument to &quot;fix_inputs&quot; must be an instance of an astropy Model.&#39;)
2957 |             if not isinstance(right, dict):
2958 |                 raise ValueError(&#39;Expected a dictionary for second argument of &quot;fix_inputs&quot;.&#39;)
2959 | 
2960 |             # Dict keys must match either possible indices
2961 |             # for model on left side, or names for inputs.
2962 |             self.n_inputs = left.n_inputs - len(right)
2963 |             # Assign directly to the private attribute (instead of using the setter)
2964 |             # to avoid asserting the new number of outputs matches the old one.
2965 |             self._outputs = left.outputs
2966 |             self.n_outputs = left.n_outputs
2967 |             newinputs = list(left.inputs)
2968 |             keys = right.keys()
2969 |             input_ind = []
2970 |             for key in keys:
2971 |                 if np.issubdtype(type(key), np.integer):
2972 |                     if key &gt;= left.n_inputs or key &lt; 0:
2973 |                         raise ValueError(
2974 |                             &#39;Substitution key integer value &#39;
2975 |                             &#39;not among possible input choices.&#39;)
2976 |                     if key in input_ind:
2977 |                         raise ValueError(&quot;Duplicate specification of &quot;
2978 |                                          &quot;same input (index/name).&quot;)
2979 |                     input_ind.append(key)
2980 |                 elif isinstance(key, str):
2981 |                     if key not in left.inputs:
2982 |                         raise ValueError(
2983 |                             &#39;Substitution key string not among possible &#39;
2984 |                             &#39;input choices.&#39;)
2985 |                     # Check to see it doesn&#39;t match positional
2986 |                     # specification.
2987 |                     ind = left.inputs.index(key)
2988 |                     if ind in input_ind:
2989 |                         raise ValueError(&quot;Duplicate specification of &quot;
2990 |                                          &quot;same input (index/name).&quot;)
2991 |                     input_ind.append(ind)
2992 |             # Remove substituted inputs
2993 |             input_ind.sort()
2994 |             input_ind.reverse()
2995 |             for ind in input_ind:
2996 |                 del newinputs[ind]
2997 |             self.inputs = tuple(newinputs)
2998 |             # Now check to see if the input model has bounding_box defined.
2999 |             # If so, remove the appropriate dimensions and set it for this
3000 |             # instance.
3001 |             try:
3002 |                 self.bounding_box = \
3003 |                     self.left.bounding_box.fix_inputs(self, right)
3004 |             except NotImplementedError:
3005 |                 pass
3006 | 
3007 |         else:
3008 |             raise ModelDefinitionError(&#39;Illegal operator: &#39;, self.op)
3009 |         self.name = name
3010 |         self._fittable = None
3011 |         self.fit_deriv = None
3012 |         self.col_fit_deriv = None
3013 |         if op in (&#39;|&#39;, &#39;+&#39;, &#39;-&#39;):
3014 |             self.linear = left.linear and right.linear
3015 |         else:
3016 |             self.linear = False
3017 |         self.eqcons = []
3018 |         self.ineqcons = []
3019 |         self.n_left_params = len(self.left.parameters)
3020 |         self._map_parameters()
3021 | 
3022 |     def _get_left_inputs_from_args(self, args):
3023 |         return args[:self.left.n_inputs]
3024 | 
3025 |     def _get_right_inputs_from_args(self, args):
3026 |         op = self.op
3027 |         if op == &#39;&amp;&#39;:
3028 |             # Args expected to look like (*left inputs, *right inputs, *left params, *right params)
3029 |             return args[self.left.n_inputs: self.left.n_inputs + self.right.n_inputs]
3030 |         elif op == &#39;|&#39; or  op == &#39;fix_inputs&#39;:
3031 |             return None
3032 |         else:
3033 |             return args[:self.left.n_inputs]
3034 | 
3035 |     def _get_left_params_from_args(self, args):
3036 |         op = self.op
3037 |         if op == &#39;&amp;&#39;:
3038 |             # Args expected to look like (*left inputs, *right inputs, *left params, *right params)
3039 |             n_inputs = self.left.n_inputs + self.right.n_inputs
3040 |             return args[n_inputs: n_inputs + self.n_left_params]
3041 |         else:
3042 |             return args[self.left.n_inputs: self.left.n_inputs + self.n_left_params]
3043 | 
3044 |     def _get_right_params_from_args(self, args):
3045 |         op = self.op
3046 |         if op == &#39;fix_inputs&#39;:
3047 |             return None
3048 |         if op == &#39;&amp;&#39;:
3049 |             # Args expected to look like (*left inputs, *right inputs, *left params, *right params)
3050 |             return args[self.left.n_inputs + self.right.n_inputs + self.n_left_params:]
3051 |         else:
3052 |             return args[self.left.n_inputs + self.n_left_params:]
3053 | 
3054 |     def _get_kwarg_model_parameters_as_positional(self, args, kwargs):
3055 |         # could do it with inserts but rebuilding seems like simpilist way
3056 | 
3057 |         #TODO: Check if any param names are in kwargs maybe as an intersection of sets?
3058 |         if self.op == &quot;&amp;&quot;:
3059 |             new_args = list(args[:self.left.n_inputs + self.right.n_inputs])
3060 |             args_pos = self.left.n_inputs + self.right.n_inputs
3061 |         else:
3062 |             new_args = list(args[:self.left.n_inputs])
3063 |             args_pos = self.left.n_inputs
3064 | 
3065 |         for param_name in self.param_names:
3066 |             kw_value = kwargs.pop(param_name, None)
3067 |             if kw_value is not None:
3068 |                 value = kw_value
3069 |             else:
3070 |                 try:
3071 |                     value = args[args_pos]
3072 |                 except IndexError:
3073 |                     raise IndexError(&quot;Missing parameter or input&quot;)
3074 | 
3075 |                 args_pos += 1
3076 |             new_args.append(value)
3077 | 
3078 |         return new_args, kwargs
3079 | 
3080 |     def _apply_operators_to_value_lists(self, leftval, rightval, **kw):
3081 |         op = self.op
3082 |         if op == &#39;+&#39;:
3083 |             return binary_operation(operator.add, leftval, rightval)
3084 |         elif op == &#39;-&#39;:
3085 |             return binary_operation(operator.sub, leftval, rightval)
3086 |         elif op == &#39;*&#39;:
3087 |             return binary_operation(operator.mul, leftval, rightval)
3088 |         elif op == &#39;/&#39;:
3089 |             return binary_operation(operator.truediv, leftval, rightval)
3090 |         elif op == &#39;**&#39;:
3091 |             return binary_operation(operator.pow, leftval, rightval)
3092 |         elif op == &#39;&amp;&#39;:
3093 |             if not isinstance(leftval, tuple):
3094 |                 leftval = (leftval,)
3095 |             if not isinstance(rightval, tuple):
3096 |                 rightval = (rightval,)
3097 |             return leftval + rightval
3098 |         elif op in SPECIAL_OPERATORS:
3099 |             return binary_operation(SPECIAL_OPERATORS[op], leftval, rightval)
3100 |         else:
3101 |             raise ModelDefinitionError(&#39;Unrecognized operator {op}&#39;)
3102 | 
3103 |     def evaluate(self, *args, **kw):
3104 |         op = self.op
3105 |         args, kw = self._get_kwarg_model_parameters_as_positional(args, kw)
3106 |         left_inputs = self._get_left_inputs_from_args(args)
3107 |         left_params = self._get_left_params_from_args(args)
3108 | 
3109 |         if op == &#39;fix_inputs&#39;:
3110 |             pos_index = dict(zip(self.left.inputs, range(self.left.n_inputs)))
3111 |             fixed_inputs = {
3112 |                 key if np.issubdtype(type(key), np.integer) else pos_index[key]: value
3113 |                 for key, value in self.right.items()
3114 |             }
3115 |             left_inputs = [
3116 |                 fixed_inputs[ind] if ind in fixed_inputs.keys() else inp
3117 |                 for ind, inp in enumerate(left_inputs)
3118 |             ]
3119 | 
3120 |         leftval = self.left.evaluate(*itertools.chain(left_inputs, left_params))
3121 | 
3122 |         if op == &#39;fix_inputs&#39;:
3123 |             return leftval
3124 | 
3125 |         right_inputs = self._get_right_inputs_from_args(args)
3126 |         right_params = self._get_right_params_from_args(args)
3127 | 
3128 |         if op == &quot;|&quot;:
3129 |             if isinstance(leftval, tuple):
3130 |                 return self.right.evaluate(*itertools.chain(leftval, right_params))
3131 |             else:
3132 |                 return self.right.evaluate(leftval, *right_params)
3133 |         else:
3134 |             rightval = self.right.evaluate(*itertools.chain(right_inputs, right_params))
3135 | 
3136 |         return self._apply_operators_to_value_lists(leftval, rightval, **kw)
3137 | 
3138 |     @property
3139 |     def n_submodels(self):
3140 |         if self._leaflist is None:
3141 |             self._make_leaflist()
3142 |         return len(self._leaflist)
3143 | 
3144 |     @property
3145 |     def submodel_names(self):
3146 |         &quot;&quot;&quot; Return the names of submodels in a ``CompoundModel``.&quot;&quot;&quot;
3147 |         if self._leaflist is None:
3148 |             self._make_leaflist()
3149 |         names = [item.name for item in self._leaflist]
3150 |         nonecount = 0
3151 |         newnames = []
3152 |         for item in names:
3153 |             if item is None:
3154 |                 newnames.append(f&#39;None_{nonecount}&#39;)
3155 |                 nonecount += 1
3156 |             else:
3157 |                 newnames.append(item)
3158 |         return tuple(newnames)
3159 | 
3160 |     def both_inverses_exist(self):
3161 |         &#39;&#39;&#39;
3162 |         if both members of this compound model have inverses return True
3163 |         &#39;&#39;&#39;
3164 |         warnings.warn(
3165 |             &quot;CompoundModel.both_inverses_exist is deprecated. &quot;
3166 |             &quot;Use has_inverse instead.&quot;,
3167 |             AstropyDeprecationWarning
3168 |         )
3169 | 
3170 |         try:
3171 |             linv = self.left.inverse
3172 |             rinv = self.right.inverse
3173 |         except NotImplementedError:
3174 |             return False
3175 | 
3176 |         return True
3177 | 
3178 |     def _pre_evaluate(self, *args, **kwargs):
3179 |         &quot;&quot;&quot;
3180 |         CompoundModel specific input setup that needs to occur prior to
3181 |             model evaluation.
3182 | 
3183 |         Note
3184 |         ----
3185 |             All of the _pre_evaluate for each component model will be
3186 |             performed at the time that the individual model is evaluated.
3187 |         &quot;&quot;&quot;
3188 | 
3189 |         # If equivalencies are provided, necessary to map parameters and pass
3190 |         # the leaflist as a keyword input for use by model evaluation so that
3191 |         # the compound model input names can be matched to the model input
3192 |         # names.
3193 |         if &#39;equivalencies&#39; in kwargs:
3194 |             # Restructure to be useful for the individual model lookup
3195 |             kwargs[&#39;inputs_map&#39;] = [(value[0], (value[1], key)) for
3196 |                                     key, value in self.inputs_map().items()]
3197 | 
3198 |         # Setup actual model evaluation method
3199 |         def evaluate(_inputs):
3200 |             return self._evaluate(*_inputs, **kwargs)
3201 | 
3202 |         return evaluate, args, None, kwargs
3203 | 
3204 |     @property
3205 |     def _argnames(self):
3206 |         &quot;&quot;&quot;No inputs should be used to determine input_shape when handling compound models&quot;&quot;&quot;
3207 |         return ()
3208 | 
3209 |     def _post_evaluate(self, inputs, outputs, broadcasted_shapes, with_bbox, **kwargs):
3210 |         &quot;&quot;&quot;
3211 |         CompoundModel specific post evaluation processing of outputs
3212 | 
3213 |         Note
3214 |         ----
3215 |             All of the _post_evaluate for each component model will be
3216 |             performed at the time that the individual model is evaluated.
3217 |         &quot;&quot;&quot;
3218 |         if self.get_bounding_box(with_bbox) is not None and self.n_outputs == 1:
3219 |             return outputs[0]
3220 |         return outputs
3221 | 
3222 |     def _evaluate(self, *args, **kw):
3223 |         op = self.op
3224 |         if op != &#39;fix_inputs&#39;:
3225 |             if op != &#39;&amp;&#39;:
3226 |                 leftval = self.left(*args, **kw)
3227 |                 if op != &#39;|&#39;:
3228 |                     rightval = self.right(*args, **kw)
3229 |                 else:
3230 |                     rightval = None
3231 | 
3232 |             else:
3233 |                 leftval = self.left(*(args[:self.left.n_inputs]), **kw)
3234 |                 rightval = self.right(*(args[self.left.n_inputs:]), **kw)
3235 | 
3236 |             if op != &quot;|&quot;:
3237 |                 return self._apply_operators_to_value_lists(leftval, rightval, **kw)
3238 | 
3239 |             elif op == &#39;|&#39;:
3240 |                 if isinstance(leftval, tuple):
3241 |                     return self.right(*leftval, **kw)
3242 |                 else:
3243 |                     return self.right(leftval, **kw)
3244 | 
3245 |         else:
3246 |             subs = self.right
3247 |             newargs = list(args)
3248 |             subinds = []
3249 |             subvals = []
3250 |             for key in subs.keys():
3251 |                 if np.issubdtype(type(key), np.integer):
3252 |                     subinds.append(key)
3253 |                 elif isinstance(key, str):
3254 |                     ind = self.left.inputs.index(key)
3255 |                     subinds.append(ind)
3256 |                 subvals.append(subs[key])
3257 |             # Turn inputs specified in kw into positional indices.
3258 |             # Names for compound inputs do not propagate to sub models.
3259 |             kwind = []
3260 |             kwval = []
3261 |             for kwkey in list(kw.keys()):
3262 |                 if kwkey in self.inputs:
3263 |                     ind = self.inputs.index(kwkey)
3264 |                     if ind &lt; len(args):
3265 |                         raise ValueError(&quot;Keyword argument duplicates &quot;
3266 |                                          &quot;positional value supplied.&quot;)
3267 |                     kwind.append(ind)
3268 |                     kwval.append(kw[kwkey])
3269 |                     del kw[kwkey]
3270 |             # Build new argument list
3271 |             # Append keyword specified args first
3272 |             if kwind:
3273 |                 kwargs = list(zip(kwind, kwval))
3274 |                 kwargs.sort()
3275 |                 kwindsorted, kwvalsorted = list(zip(*kwargs))
3276 |                 newargs = newargs + list(kwvalsorted)
3277 |             if subinds:
3278 |                 subargs = list(zip(subinds, subvals))
3279 |                 subargs.sort()
3280 |                 # subindsorted, subvalsorted = list(zip(*subargs))
3281 |                 # The substitutions must be inserted in order
3282 |                 for ind, val in subargs:
3283 |                     newargs.insert(ind, val)
3284 |             return self.left(*newargs, **kw)
3285 | 
3286 |     @property
3287 |     def param_names(self):
3288 |         &quot;&quot;&quot; An ordered list of parameter names.&quot;&quot;&quot;
3289 |         return self._param_names
3290 | 
3291 |     def _make_leaflist(self):
3292 |         tdict = {}
3293 |         leaflist = []
3294 |         make_subtree_dict(self, &#39;&#39;, tdict, leaflist)
3295 |         self._leaflist = leaflist
3296 |         self._tdict = tdict
3297 | 
3298 |     def __getattr__(self, name):
3299 |         &quot;&quot;&quot;
3300 |         If someone accesses an attribute not already defined, map the
3301 |         parameters, and then see if the requested attribute is one of
3302 |         the parameters
3303 |         &quot;&quot;&quot;
3304 |         # The following test is needed to avoid infinite recursion
3305 |         # caused by deepcopy. There may be other such cases discovered.
3306 |         if name == &#39;__setstate__&#39;:
3307 |             raise AttributeError
3308 |         if name in self._param_names:
3309 |             return self.__dict__[name]
3310 |         else:
3311 |             raise AttributeError(f&#39;Attribute &quot;{name}&quot; not found&#39;)
3312 | 
3313 |     def __getitem__(self, index):
3314 |         if self._leaflist is None:
3315 |             self._make_leaflist()
3316 |         leaflist = self._leaflist
3317 |         tdict = self._tdict
3318 |         if isinstance(index, slice):
3319 |             if index.step:
3320 |                 raise ValueError(&#39;Steps in slices not supported &#39;
3321 |                                  &#39;for compound models&#39;)
3322 |             if index.start is not None:
3323 |                 if isinstance(index.start, str):
3324 |                     start = self._str_index_to_int(index.start)
3325 |                 else:
3326 |                     start = index.start
3327 |             else:
3328 |                 start = 0
3329 |             if index.stop is not None:
3330 |                 if isinstance(index.stop, str):
3331 |                     stop = self._str_index_to_int(index.stop)
3332 |                 else:
3333 |                     stop = index.stop - 1
3334 |             else:
3335 |                 stop = len(leaflist) - 1
3336 |             if index.stop == 0:
3337 |                 raise ValueError(&quot;Slice endpoint cannot be 0&quot;)
3338 |             if start &lt; 0:
3339 |                 start = len(leaflist) + start
3340 |             if stop &lt; 0:
3341 |                 stop = len(leaflist) + stop
3342 |             # now search for matching node:
3343 |             if stop == start:  # only single value, get leaf instead in code below
3344 |                 index = start
3345 |             else:
3346 |                 for key in tdict:
3347 |                     node, leftind, rightind = tdict[key]
3348 |                     if leftind == start and rightind == stop:
3349 |                         return node
3350 |                 raise IndexError(&quot;No appropriate subtree matches slice&quot;)
3351 |         if isinstance(index, type(0)):
3352 |             return leaflist[index]
3353 |         elif isinstance(index, type(&#39;&#39;)):
3354 |             return leaflist[self._str_index_to_int(index)]
3355 |         else:
3356 |             raise TypeError(&#39;index must be integer, slice, or model name string&#39;)
3357 | 
3358 |     def _str_index_to_int(self, str_index):
3359 |         # Search through leaflist for item with that name
3360 |         found = []
3361 |         for nleaf, leaf in enumerate(self._leaflist):
3362 |             if getattr(leaf, &#39;name&#39;, None) == str_index:
3363 |                 found.append(nleaf)
3364 |         if len(found) == 0:
3365 |             raise IndexError(f&quot;No component with name &#39;{str_index}&#39; found&quot;)
3366 |         if len(found) &gt; 1:
3367 |             raise IndexError(&quot;Multiple components found using &#39;{}&#39; as name\n&quot;
3368 |                              &quot;at indices {}&quot;.format(str_index, found))
3369 |         return found[0]
3370 | 
3371 |     @property
3372 |     def n_inputs(self):
3373 |         &quot;&quot;&quot; The number of inputs of a model.&quot;&quot;&quot;
3374 |         return self._n_inputs
3375 | 
3376 |     @n_inputs.setter
3377 |     def n_inputs(self, value):
3378 |         self._n_inputs = value
3379 | 
3380 |     @property
3381 |     def n_outputs(self):
3382 |         &quot;&quot;&quot; The number of outputs of a model.&quot;&quot;&quot;
3383 |         return self._n_outputs
3384 | 
3385 |     @n_outputs.setter
3386 |     def n_outputs(self, value):
3387 |         self._n_outputs = value
3388 | 
3389 |     @property
3390 |     def eqcons(self):
3391 |         return self._eqcons
3392 | 
3393 |     @eqcons.setter
3394 |     def eqcons(self, value):
3395 |         self._eqcons = value
3396 | 
3397 |     @property
3398 |     def ineqcons(self):
3399 |         return self._eqcons
3400 | 
3401 |     @ineqcons.setter
3402 |     def ineqcons(self, value):
3403 |         self._eqcons = value
3404 | 
3405 |     def traverse_postorder(self, include_operator=False):
3406 |         &quot;&quot;&quot; Postorder traversal of the CompoundModel tree.&quot;&quot;&quot;
3407 |         res = []
3408 |         if isinstance(self.left, CompoundModel):
3409 |             res = res + self.left.traverse_postorder(include_operator)
3410 |         else:
3411 |             res = res + [self.left]
3412 |         if isinstance(self.right, CompoundModel):
3413 |             res = res + self.right.traverse_postorder(include_operator)
3414 |         else:
3415 |             res = res + [self.right]
3416 |         if include_operator:
3417 |             res.append(self.op)
3418 |         else:
3419 |             res.append(self)
3420 |         return res
3421 | 
3422 |     def _format_expression(self, format_leaf=None):
3423 |         leaf_idx = 0
3424 |         operands = deque()
3425 | 
3426 |         if format_leaf is None:
3427 |             format_leaf = lambda i, l: f&#39;[{i}]&#39;
3428 | 
3429 |         for node in self.traverse_postorder():
3430 |             if not isinstance(node, CompoundModel):
3431 |                 operands.append(format_leaf(leaf_idx, node))
3432 |                 leaf_idx += 1
3433 |                 continue
3434 | 
3435 |             right = operands.pop()
3436 |             left = operands.pop()
3437 |             if node.op in OPERATOR_PRECEDENCE:
3438 |                 oper_order = OPERATOR_PRECEDENCE[node.op]
3439 | 
3440 |                 if isinstance(node, CompoundModel):
3441 |                     if (isinstance(node.left, CompoundModel) and
3442 |                             OPERATOR_PRECEDENCE[node.left.op] &lt; oper_order):
3443 |                         left = f&#39;({left})&#39;
3444 |                     if (isinstance(node.right, CompoundModel) and
3445 |                             OPERATOR_PRECEDENCE[node.right.op] &lt; oper_order):
3446 |                         right = f&#39;({right})&#39;
3447 | 
3448 |                 operands.append(&#39; &#39;.join((left, node.op, right)))
3449 |             else:
3450 |                 left = f&#39;(({left}),&#39;
3451 |                 right = f&#39;({right}))&#39;
3452 |                 operands.append(&#39; &#39;.join((node.op[0], left, right)))
3453 | 
3454 |         return &#39;&#39;.join(operands)
3455 | 
3456 |     def _format_components(self):
3457 |         if self._parameters_ is None:
3458 |             self._map_parameters()
3459 |         return &#39;\n\n&#39;.join(&#39;[{0}]: {1!r}&#39;.format(idx, m)
3460 |                            for idx, m in enumerate(self._leaflist))
3461 | 
3462 |     def __str__(self):
3463 |         expression = self._format_expression()
3464 |         components = self._format_components()
3465 |         keywords = [
3466 |             (&#39;Expression&#39;, expression),
3467 |             (&#39;Components&#39;, &#39;\n&#39; + indent(components))
3468 |         ]
3469 |         return super()._format_str(keywords=keywords)
3470 | 
3471 |     def rename(self, name):
3472 |         self.name = name
3473 |         return self
3474 | 
3475 |     @property
3476 |     def isleaf(self):
3477 |         return False
3478 | 
3479 |     @property
3480 |     def inverse(self):
3481 |         if self.op == &#39;|&#39;:
3482 |             return self.right.inverse | self.left.inverse
3483 |         elif self.op == &#39;&amp;&#39;:
3484 |             return self.left.inverse &amp; self.right.inverse
3485 |         else:
3486 |             return NotImplemented
3487 | 
3488 |     @property
3489 |     def fittable(self):
3490 |         &quot;&quot;&quot; Set the fittable attribute on a compound model.&quot;&quot;&quot;
3491 |         if self._fittable is None:
3492 |             if self._leaflist is None:
3493 |                 self._map_parameters()
3494 |             self._fittable = all(m.fittable for m in self._leaflist)
3495 |         return self._fittable
3496 | 
3497 |     __add__ = _model_oper(&#39;+&#39;)
3498 |     __sub__ = _model_oper(&#39;-&#39;)
3499 |     __mul__ = _model_oper(&#39;*&#39;)
3500 |     __truediv__ = _model_oper(&#39;/&#39;)
3501 |     __pow__ = _model_oper(&#39;**&#39;)
3502 |     __or__ = _model_oper(&#39;|&#39;)
3503 |     __and__ = _model_oper(&#39;&amp;&#39;)
3504 | 
3505 |     def _map_parameters(self):
3506 |         &quot;&quot;&quot;
3507 |         Map all the constituent model parameters to the compound object,
3508 |         renaming as necessary by appending a suffix number.
3509 | 
3510 |         This can be an expensive operation, particularly for a complex
3511 |         expression tree.
3512 | 
3513 |         All the corresponding parameter attributes are created that one
3514 |         expects for the Model class.
3515 | 
3516 |         The parameter objects that the attributes point to are the same
3517 |         objects as in the constiutent models. Changes made to parameter
3518 |         values to either are seen by both.
3519 | 
3520 |         Prior to calling this, none of the associated attributes will
3521 |         exist. This method must be called to make the model usable by
3522 |         fitting engines.
3523 | 
3524 |         If oldnames=True, then parameters are named as in the original
3525 |         implementation of compound models.
3526 |         &quot;&quot;&quot;
3527 |         if self._parameters is not None:
3528 |             # do nothing
3529 |             return
3530 |         if self._leaflist is None:
3531 |             self._make_leaflist()
3532 |         self._parameters_ = {}
3533 |         param_map = {}
3534 |         self._param_names = []
3535 |         for lindex, leaf in enumerate(self._leaflist):
3536 |             if not isinstance(leaf, dict):
3537 |                 for param_name in leaf.param_names:
3538 |                     param = getattr(leaf, param_name)
3539 |                     new_param_name = f&quot;{param_name}_{lindex}&quot;
3540 |                     self.__dict__[new_param_name] = param
3541 |                     self._parameters_[new_param_name] = param
3542 |                     self._param_names.append(new_param_name)
3543 |                     param_map[new_param_name] = (lindex, param_name)
3544 |         self._param_metrics = {}
3545 |         self._param_map = param_map
3546 |         self._param_map_inverse = dict((v, k) for k, v in param_map.items())
3547 |         self._initialize_slices()
3548 |         self._param_names = tuple(self._param_names)
3549 | 
3550 |     def _initialize_slices(self):
3551 |         param_metrics = self._param_metrics
3552 |         total_size = 0
3553 | 
3554 |         for name in self.param_names:
3555 |             param = getattr(self, name)
3556 |             value = param.value
3557 |             param_size = np.size(value)
3558 |             param_shape = np.shape(value)
3559 |             param_slice = slice(total_size, total_size + param_size)
3560 |             param_metrics[name] = {}
3561 |             param_metrics[name][&#39;slice&#39;] = param_slice
3562 |             param_metrics[name][&#39;shape&#39;] = param_shape
3563 |             param_metrics[name][&#39;size&#39;] = param_size
3564 |             total_size += param_size
3565 |         self._parameters = np.empty(total_size, dtype=np.float64)
3566 | 
3567 |     @staticmethod
3568 |     def _recursive_lookup(branch, adict, key):
3569 |         if isinstance(branch, CompoundModel):
3570 |             return adict[key]
3571 |         return branch, key
3572 | 
3573 |     def inputs_map(self):
3574 |         &quot;&quot;&quot;
3575 |         Map the names of the inputs to this ExpressionTree to the inputs to the leaf models.
3576 |         &quot;&quot;&quot;
3577 |         inputs_map = {}
3578 |         if not isinstance(self.op, str):  # If we don&#39;t have an operator the mapping is trivial
3579 |             return {inp: (self, inp) for inp in self.inputs}
3580 | 
3581 |         elif self.op == &#39;|&#39;:
3582 |             if isinstance(self.left, CompoundModel):
3583 |                 l_inputs_map = self.left.inputs_map()
3584 |             for inp in self.inputs:
3585 |                 if isinstance(self.left, CompoundModel):
3586 |                     inputs_map[inp] = l_inputs_map[inp]
3587 |                 else:
3588 |                     inputs_map[inp] = self.left, inp
3589 |         elif self.op == &#39;&amp;&#39;:
3590 |             if isinstance(self.left, CompoundModel):
3591 |                 l_inputs_map = self.left.inputs_map()
3592 |             if isinstance(self.right, CompoundModel):
3593 |                 r_inputs_map = self.right.inputs_map()
3594 |             for i, inp in enumerate(self.inputs):
3595 |                 if i &lt; len(self.left.inputs):  # Get from left
3596 |                     if isinstance(self.left, CompoundModel):
3597 |                         inputs_map[inp] = l_inputs_map[self.left.inputs[i]]
3598 |                     else:
3599 |                         inputs_map[inp] = self.left, self.left.inputs[i]
3600 |                 else:  # Get from right
3601 |                     if isinstance(self.right, CompoundModel):
3602 |                         inputs_map[inp] = r_inputs_map[self.right.inputs[i - len(self.left.inputs)]]
3603 |                     else:
3604 |                         inputs_map[inp] = self.right, self.right.inputs[i - len(self.left.inputs)]
3605 |         elif self.op == &#39;fix_inputs&#39;:
3606 |             fixed_ind = list(self.right.keys())
3607 |             ind = [list(self.left.inputs).index(i) if isinstance(i, str) else i for i in fixed_ind]
3608 |             inp_ind = list(range(self.left.n_inputs))
3609 |             for i in ind:
3610 |                 inp_ind.remove(i)
3611 |             for i in inp_ind:
3612 |                 inputs_map[self.left.inputs[i]] = self.left, self.left.inputs[i]
3613 |         else:
3614 |             if isinstance(self.left, CompoundModel):
3615 |                 l_inputs_map = self.left.inputs_map()
3616 |             for inp in self.left.inputs:
3617 |                 if isinstance(self.left, CompoundModel):
3618 |                     inputs_map[inp] = l_inputs_map[inp]
3619 |                 else:
3620 |                     inputs_map[inp] = self.left, inp
3621 |         return inputs_map
3622 | 
3623 |     def _parameter_units_for_data_units(self, input_units, output_units):
3624 |         if self._leaflist is None:
3625 |             self._map_parameters()
3626 |         units_for_data = {}
3627 |         for imodel, model in enumerate(self._leaflist):
3628 |             units_for_data_leaf = model._parameter_units_for_data_units(input_units, output_units)
3629 |             for param_leaf in units_for_data_leaf:
3630 |                 param = self._param_map_inverse[(imodel, param_leaf)]
3631 |                 units_for_data[param] = units_for_data_leaf[param_leaf]
3632 |         return units_for_data
3633 | 
3634 |     @property
3635 |     def input_units(self):
3636 |         inputs_map = self.inputs_map()
3637 |         input_units_dict = {key: inputs_map[key][0].input_units[orig_key]
3638 |                             for key, (mod, orig_key) in inputs_map.items()
3639 |                             if inputs_map[key][0].input_units is not None}
3640 |         if input_units_dict:
3641 |             return input_units_dict
3642 |         return None
3643 | 
3644 |     @property
3645 |     def input_units_equivalencies(self):
3646 |         inputs_map = self.inputs_map()
3647 |         input_units_equivalencies_dict = {
3648 |             key: inputs_map[key][0].input_units_equivalencies[orig_key]
3649 |             for key, (mod, orig_key) in inputs_map.items()
3650 |             if inputs_map[key][0].input_units_equivalencies is not None
3651 |         }
3652 |         if not input_units_equivalencies_dict:
3653 |             return None
3654 | 
3655 |         return input_units_equivalencies_dict
3656 | 
3657 |     @property
3658 |     def input_units_allow_dimensionless(self):
3659 |         inputs_map = self.inputs_map()
3660 |         return {key: inputs_map[key][0].input_units_allow_dimensionless[orig_key]
3661 |                 for key, (mod, orig_key) in inputs_map.items()}
3662 | 
3663 |     @property
3664 |     def input_units_strict(self):
3665 |         inputs_map = self.inputs_map()
3666 |         return {key: inputs_map[key][0].input_units_strict[orig_key]
3667 |                 for key, (mod, orig_key) in inputs_map.items()}
3668 | 
3669 |     @property
3670 |     def return_units(self):
3671 |         outputs_map = self.outputs_map()
3672 |         return {key: outputs_map[key][0].return_units[orig_key]
3673 |                 for key, (mod, orig_key) in outputs_map.items()
3674 |                 if outputs_map[key][0].return_units is not None}
3675 | 
3676 |     def outputs_map(self):
3677 |         &quot;&quot;&quot;
3678 |         Map the names of the outputs to this ExpressionTree to the outputs to the leaf models.
3679 |         &quot;&quot;&quot;
3680 |         outputs_map = {}
3681 |         if not isinstance(self.op, str):  # If we don&#39;t have an operator the mapping is trivial
3682 |             return {out: (self, out) for out in self.outputs}
3683 | 
3684 |         elif self.op == &#39;|&#39;:
3685 |             if isinstance(self.right, CompoundModel):
3686 |                 r_outputs_map = self.right.outputs_map()
3687 |             for out in self.outputs:
3688 |                 if isinstance(self.right, CompoundModel):
3689 |                     outputs_map[out] = r_outputs_map[out]
3690 |                 else:
3691 |                     outputs_map[out] = self.right, out
3692 | 
3693 |         elif self.op == &#39;&amp;&#39;:
3694 |             if isinstance(self.left, CompoundModel):
3695 |                 l_outputs_map = self.left.outputs_map()
3696 |             if isinstance(self.right, CompoundModel):
3697 |                 r_outputs_map = self.right.outputs_map()
3698 |             for i, out in enumerate(self.outputs):
3699 |                 if i &lt; len(self.left.outputs):  # Get from left
3700 |                     if isinstance(self.left, CompoundModel):
3701 |                         outputs_map[out] = l_outputs_map[self.left.outputs[i]]
3702 |                     else:
3703 |                         outputs_map[out] = self.left, self.left.outputs[i]
3704 |                 else:  # Get from right
3705 |                     if isinstance(self.right, CompoundModel):
3706 |                         outputs_map[out] = r_outputs_map[self.right.outputs[i - len(self.left.outputs)]]
3707 |                     else:
3708 |                         outputs_map[out] = self.right, self.right.outputs[i - len(self.left.outputs)]
3709 |         elif self.op == &#39;fix_inputs&#39;:
3710 |             return self.left.outputs_map()
3711 |         else:
3712 |             if isinstance(self.left, CompoundModel):
3713 |                 l_outputs_map = self.left.outputs_map()
3714 |             for out in self.left.outputs:
3715 |                 if isinstance(self.left, CompoundModel):
3716 |                     outputs_map[out] = l_outputs_map()[out]
3717 |                 else:
3718 |                     outputs_map[out] = self.left, out
3719 |         return outputs_map
3720 | 
3721 |     @property
3722 |     def has_user_bounding_box(self):
3723 |         &quot;&quot;&quot;
3724 |         A flag indicating whether or not a custom bounding_box has been
3725 |         assigned to this model by a user, via assignment to
3726 |         ``model.bounding_box``.
3727 |         &quot;&quot;&quot;
3728 | 
3729 |         return self._user_bounding_box is not None
3730 | 
3731 |     def render(self, out=None, coords=None):
3732 |         &quot;&quot;&quot;
3733 |         Evaluate a model at fixed positions, respecting the ``bounding_box``.
3734 | 
3735 |         The key difference relative to evaluating the model directly is that
3736 |         this method is limited to a bounding box if the `Model.bounding_box`
3737 |         attribute is set.
3738 | 
3739 |         Parameters
3740 |         ----------
3741 |         out : `numpy.ndarray`, optional
3742 |             An array that the evaluated model will be added to.  If this is not
3743 |             given (or given as ``None``), a new array will be created.
3744 |         coords : array-like, optional
3745 |             An array to be used to translate from the model&#39;s input coordinates
3746 |             to the ``out`` array. It should have the property that
3747 |             ``self(coords)`` yields the same shape as ``out``.  If ``out`` is
3748 |             not specified, ``coords`` will be used to determine the shape of
3749 |             the returned array. If this is not provided (or None), the model
3750 |             will be evaluated on a grid determined by `Model.bounding_box`.
3751 | 
3752 |         Returns
3753 |         -------
3754 |         out : `numpy.ndarray`
3755 |             The model added to ``out`` if  ``out`` is not ``None``, or else a
3756 |             new array from evaluating the model over ``coords``.
3757 |             If ``out`` and ``coords`` are both `None`, the returned array is
3758 |             limited to the `Model.bounding_box` limits. If
3759 |             `Model.bounding_box` is `None`, ``arr`` or ``coords`` must be
3760 |             passed.
3761 | 
3762 |         Raises
3763 |         ------
3764 |         ValueError
3765 |             If ``coords`` are not given and the the `Model.bounding_box` of
3766 |             this model is not set.
3767 | 
3768 |         Examples
3769 |         --------
3770 |         :ref:`astropy:bounding-boxes`
3771 |         &quot;&quot;&quot;
3772 | 
3773 |         bbox = self.get_bounding_box()
3774 | 
3775 |         ndim = self.n_inputs
3776 | 
3777 |         if (coords is None) and (out is None) and (bbox is None):
3778 |             raise ValueError(&#39;If no bounding_box is set, &#39;
3779 |                              &#39;coords or out must be input.&#39;)
3780 | 
3781 |         # for consistent indexing
3782 |         if ndim == 1:
3783 |             if coords is not None:
3784 |                 coords = [coords]
3785 |             if bbox is not None:
3786 |                 bbox = [bbox]
3787 | 
3788 |         if coords is not None:
3789 |             coords = np.asanyarray(coords, dtype=float)
3790 |             # Check dimensions match out and model
3791 |             assert len(coords) == ndim
3792 |             if out is not None:
3793 |                 if coords[0].shape != out.shape:
3794 |                     raise ValueError(&#39;inconsistent shape of the output.&#39;)
3795 |             else:
3796 |                 out = np.zeros(coords[0].shape)
3797 | 
3798 |         if out is not None:
3799 |             out = np.asanyarray(out)
3800 |             if out.ndim != ndim:
3801 |                 raise ValueError(&#39;the array and model must have the same &#39;
3802 |                                  &#39;number of dimensions.&#39;)
3803 | 
3804 |         if bbox is not None:
3805 |             # Assures position is at center pixel, important when using
3806 |             # add_array.
3807 |             pd = np.array([(np.mean(bb), np.ceil((bb[1] - bb[0]) / 2))
3808 |                            for bb in bbox]).astype(int).T
3809 |             pos, delta = pd
3810 | 
3811 |             if coords is not None:
3812 |                 sub_shape = tuple(delta * 2 + 1)
3813 |                 sub_coords = np.array([extract_array(c, sub_shape, pos)
3814 |                                        for c in coords])
3815 |             else:
3816 |                 limits = [slice(p - d, p + d + 1, 1) for p, d in pd.T]
3817 |                 sub_coords = np.mgrid[limits]
3818 | 
3819 |             sub_coords = sub_coords[::-1]
3820 | 
3821 |             if out is None:
3822 |                 out = self(*sub_coords)
3823 |             else:
3824 |                 try:
3825 |                     out = add_array(out, self(*sub_coords), pos)
3826 |                 except ValueError:
3827 |                     raise ValueError(
3828 |                         &#39;The `bounding_box` is larger than the input out in &#39;
3829 |                         &#39;one or more dimensions. Set &#39;
3830 |                         &#39;`model.bounding_box = None`.&#39;)
3831 |         else:
3832 |             if coords is None:
3833 |                 im_shape = out.shape
3834 |                 limits = [slice(i) for i in im_shape]
3835 |                 coords = np.mgrid[limits]
3836 | 
3837 |             coords = coords[::-1]
3838 | 
3839 |             out += self(*coords)
3840 | 
3841 |         return out
3842 | 
3843 |     def replace_submodel(self, name, model):
3844 |         &quot;&quot;&quot;
3845 |         Construct a new `~astropy.modeling.CompoundModel` instance from an
3846 |         existing CompoundModel, replacing the named submodel with a new model.
3847 | 
3848 |         In order to ensure that inverses and names are kept/reconstructed, it&#39;s
3849 |         necessary to rebuild the CompoundModel from the replaced node all the
3850 |         way back to the base. The original CompoundModel is left untouched.
3851 | 
3852 |         Parameters
3853 |         ----------
3854 |         name : str
3855 |             name of submodel to be replaced
3856 |         model : `~astropy.modeling.Model`
3857 |             replacement model
3858 |         &quot;&quot;&quot;
3859 |         submodels = [m for m in self.traverse_postorder()
3860 |                      if getattr(m, &#39;name&#39;, None) == name]
3861 |         if submodels:
3862 |             if len(submodels) &gt; 1:
3863 |                 raise ValueError(f&quot;More than one submodel named {name}&quot;)
3864 | 
3865 |             old_model = submodels.pop()
3866 |             if len(old_model) != len(model):
3867 |                 raise ValueError(&quot;New and old models must have equal values &quot;
3868 |                                  &quot;for n_models&quot;)
3869 | 
3870 |             # Do this check first in order to raise a more helpful Exception,
3871 |             # although it would fail trying to construct the new CompoundModel
3872 |             if (old_model.n_inputs != model.n_inputs or
3873 |                         old_model.n_outputs != model.n_outputs):
3874 |                 raise ValueError(&quot;New model must match numbers of inputs and &quot;
3875 |                                  &quot;outputs of existing model&quot;)
3876 | 
3877 |             tree = _get_submodel_path(self, name)
3878 |             while tree:
3879 |                 branch = self.copy()
3880 |                 for node in tree[:-1]:
3881 |                     branch = getattr(branch, node)
3882 |                 setattr(branch, tree[-1], model)
3883 |                 model = CompoundModel(branch.op, branch.left, branch.right,
3884 |                                       name=branch.name)
3885 |                 tree = tree[:-1]
3886 |             return model
3887 | 
3888 |         else:
3889 |             raise ValueError(f&quot;No submodels found named {name}&quot;)
3890 | 
3891 |     def _set_sub_models_and_parameter_units(self, left, right):
3892 |         &quot;&quot;&quot;
3893 |         Provides a work-around to properly set the sub models and respective
3894 |         parameters&#39;s units/values when using ``without_units_for_data``
3895 |         or ``without_units_for_data`` methods.
3896 |         &quot;&quot;&quot;
3897 |         model = CompoundModel(self.op, left, right)
3898 | 
3899 |         self.left = left
3900 |         self.right = right
3901 | 
3902 |         for name in model.param_names:
3903 |             model_parameter = getattr(model, name)
3904 |             parameter = getattr(self, name)
3905 | 
3906 |             parameter.value = model_parameter.value
3907 |             parameter._set_unit(model_parameter.unit, force=True)
3908 | 
3909 |     def without_units_for_data(self, **kwargs):
3910 |         &quot;&quot;&quot;
3911 |         See `~astropy.modeling.Model.without_units_for_data` for overview
3912 |         of this method.
3913 | 
3914 |         Notes
3915 |         -----
3916 |         This modifies the behavior of the base method to account for the
3917 |         case where the sub-models of a compound model have different output
3918 |         units. This is only valid for compound * and / compound models as
3919 |         in that case it is reasonable to mix the output units. It does this
3920 |         by modifying the output units of each sub model by using the output
3921 |         units of the other sub model so that we can apply the original function
3922 |         and get the desired result.
3923 | 
3924 |         Additional data has to be output in the mixed output unit case
3925 |         so that the units can be properly rebuilt by
3926 |         `~astropy.modeling.CompoundModel.with_units_from_data`.
3927 | 
3928 |         Outside the mixed output units, this method is identical to the
3929 |         base method.
3930 |         &quot;&quot;&quot;
3931 |         if self.op in [&#39;*&#39;, &#39;/&#39;]:
3932 |             model = self.copy()
3933 |             inputs = {inp: kwargs[inp] for inp in self.inputs}
3934 | 
3935 |             left_units = self.left.output_units(**kwargs)
3936 |             right_units = self.right.output_units(**kwargs)
3937 | 
3938 |             if self.op == &#39;*&#39;:
3939 |                 left_kwargs = {out: kwargs[out] / right_units[out]
3940 |                                for out in self.left.outputs if kwargs[out] is not None}
3941 |                 right_kwargs = {out: kwargs[out] / left_units[out]
3942 |                                 for out in self.right.outputs if kwargs[out] is not None}
3943 |             else:
3944 |                 left_kwargs = {out: kwargs[out] * right_units[out]
3945 |                                for out in self.left.outputs if kwargs[out] is not None}
3946 |                 right_kwargs = {out: 1 / kwargs[out] * left_units[out]
3947 |                                 for out in self.right.outputs if kwargs[out] is not None}
3948 | 
3949 |             left_kwargs.update(inputs.copy())
3950 |             right_kwargs.update(inputs.copy())
3951 | 
3952 |             left = self.left.without_units_for_data(**left_kwargs)
3953 |             if isinstance(left, tuple):
3954 |                 left_kwargs[&#39;_left_kwargs&#39;] = left[1]
3955 |                 left_kwargs[&#39;_right_kwargs&#39;] = left[2]
3956 |                 left = left[0]
3957 | 
3958 |             right = self.right.without_units_for_data(**right_kwargs)
3959 |             if isinstance(right, tuple):
3960 |                 right_kwargs[&#39;_left_kwargs&#39;] = right[1]
3961 |                 right_kwargs[&#39;_right_kwargs&#39;] = right[2]
3962 |                 right = right[0]
3963 | 
3964 |             model._set_sub_models_and_parameter_units(left, right)
3965 | 
3966 |             return model, left_kwargs, right_kwargs
3967 |         else:
3968 |             return super().without_units_for_data(**kwargs)
3969 | 
3970 |     def with_units_from_data(self, **kwargs):
3971 |         &quot;&quot;&quot;
3972 |         See `~astropy.modeling.Model.with_units_from_data` for overview
3973 |         of this method.
3974 | 
3975 |         Notes
3976 |         -----
3977 |         This modifies the behavior of the base method to account for the
3978 |         case where the sub-models of a compound model have different output
3979 |         units. This is only valid for compound * and / compound models as
3980 |         in that case it is reasonable to mix the output units. In order to
3981 |         do this it requires some additional information output by
3982 |         `~astropy.modeling.CompoundModel.without_units_for_data` passed as
3983 |         keyword arguments under the keywords ``_left_kwargs`` and ``_right_kwargs``.
3984 | 
3985 |         Outside the mixed output units, this method is identical to the
3986 |         base method.
3987 |         &quot;&quot;&quot;
3988 | 
3989 |         if self.op in [&#39;*&#39;, &#39;/&#39;]:
3990 |             left_kwargs = kwargs.pop(&#39;_left_kwargs&#39;)
3991 |             right_kwargs = kwargs.pop(&#39;_right_kwargs&#39;)
3992 | 
3993 |             left = self.left.with_units_from_data(**left_kwargs)
3994 |             right = self.right.with_units_from_data(**right_kwargs)
3995 | 
3996 |             model = self.copy()
3997 |             model._set_sub_models_and_parameter_units(left, right)
3998 | 
3999 |             return model
4000 |         else:
4001 |             return super().with_units_from_data(**kwargs)
```
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-7-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-7-0" class="toggle-btn">Show</button></div><div id="call-7-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_lwWUCxxcXsahYMmFmP2DrOe4</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "_separable"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;_separable&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-8')"><span>View full output (1158 chars)</span><button id="btn-tool-8" class="toggle-btn">Show</button></div><div id="tool-8" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;_separable&quot;...
### Search Result:
Found function `astropy/modeling/separable.py:_separable`.
Source: Match found for entity name `_separable`.
```
290 | def _separable(transform):
291 |     &quot;&quot;&quot;
292 |     Calculate the separability of outputs.
293 | 
294 |     Parameters
295 |     ----------
296 |     transform : `astropy.modeling.Model`
297 |         A transform (usually a compound model).
298 | 
299 |     Returns :
300 |     is_separable : ndarray of dtype np.bool
301 |         An array of shape (transform.n_outputs,) of boolean type
302 |         Each element represents the separablity of the corresponding output.
303 |     &quot;&quot;&quot;
304 |     if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
305 |         return transform_matrix
306 |     elif isinstance(transform, CompoundModel):
307 |         sepleft = _separable(transform.left)
308 |         sepright = _separable(transform.right)
309 |         return _operators[transform.op](sepleft, sepright)
310 |     elif isinstance(transform, Model):
311 |         return _coord_matrix(transform, &#39;left&#39;, transform.n_outputs)
```
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-9-0')"><span>📞 <code>get_entity_contents</code></span><button id="btn-call-9-0" class="toggle-btn">Show</button></div><div id="call-9-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_5GfVti7QvRab0eUrWyZIzJ3u</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "entity_names": [
    "astropy/modeling/core.py:Model",
    "astropy/modeling/separable.py:_separable"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: get_entity_contents</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for entity `astropy/modeling/core.py:Model`...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-10')"><span>View full output (110216 chars)</span><button id="btn-tool-10" class="toggle-btn">Show</button></div><div id="tool-10" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for entity `astropy/modeling/core.py:Model`...
### Search Result:
Found class `astropy/modeling/core.py:Model`.
Source: Exact match found for entity name `astropy/modeling/core.py:Model`.
```
 501 | class Model(metaclass=_ModelMeta):
 502 |     &quot;&quot;&quot;
 503 |     Base class for all models.
 504 | 
 505 |     This is an abstract class and should not be instantiated directly.
 506 | 
 507 |     The following initialization arguments apply to the majority of Model
 508 |     subclasses by default (exceptions include specialized utility models
 509 |     like `~astropy.modeling.mappings.Mapping`).  Parametric models take all
 510 |     their parameters as arguments, followed by any of the following optional
 511 |     keyword arguments:
 512 | 
 513 |     Parameters
 514 |     ----------
 515 |     name : str, optional
 516 |         A human-friendly name associated with this model instance
 517 |         (particularly useful for identifying the individual components of a
 518 |         compound model).
 519 | 
 520 |     meta : dict, optional
 521 |         An optional dict of user-defined metadata to attach to this model.
 522 |         How this is used and interpreted is up to the user or individual use
 523 |         case.
 524 | 
 525 |     n_models : int, optional
 526 |         If given an integer greater than 1, a *model set* is instantiated
 527 |         instead of a single model.  This affects how the parameter arguments
 528 |         are interpreted.  In this case each parameter must be given as a list
 529 |         or array--elements of this array are taken along the first axis (or
 530 |         ``model_set_axis`` if specified), such that the Nth element is the
 531 |         value of that parameter for the Nth model in the set.
 532 | 
 533 |         See the section on model sets in the documentation for more details.
 534 | 
 535 |     model_set_axis : int, optional
 536 |         This argument only applies when creating a model set (i.e. ``n_models &gt;
 537 |         1``).  It changes how parameter values are interpreted.  Normally the
 538 |         first axis of each input parameter array (properly the 0th axis) is
 539 |         taken as the axis corresponding to the model sets.  However, any axis
 540 |         of an input array may be taken as this &quot;model set axis&quot;.  This accepts
 541 |         negative integers as well--for example use ``model_set_axis=-1`` if the
 542 |         last (most rapidly changing) axis should be associated with the model
 543 |         sets. Also, ``model_set_axis=False`` can be used to tell that a given
 544 |         input should be used to evaluate all the models in the model set.
 545 | 
 546 |     fixed : dict, optional
 547 |         Dictionary ``{parameter_name: bool}`` setting the fixed constraint
 548 |         for one or more parameters.  `True` means the parameter is held fixed
 549 |         during fitting and is prevented from updates once an instance of the
 550 |         model has been created.
 551 | 
 552 |         Alternatively the `~astropy.modeling.Parameter.fixed` property of a
 553 |         parameter may be used to lock or unlock individual parameters.
 554 | 
 555 |     tied : dict, optional
 556 |         Dictionary ``{parameter_name: callable}`` of parameters which are
 557 |         linked to some other parameter. The dictionary values are callables
 558 |         providing the linking relationship.
 559 | 
 560 |         Alternatively the `~astropy.modeling.Parameter.tied` property of a
 561 |         parameter may be used to set the ``tied`` constraint on individual
 562 |         parameters.
 563 | 
 564 |     bounds : dict, optional
 565 |         A dictionary ``{parameter_name: value}`` of lower and upper bounds of
 566 |         parameters. Keys are parameter names. Values are a list or a tuple
 567 |         of length 2 giving the desired range for the parameter.
 568 | 
 569 |         Alternatively the `~astropy.modeling.Parameter.min` and
 570 |         `~astropy.modeling.Parameter.max` or
 571 |         ~astropy.modeling.Parameter.bounds` properties of a parameter may be
 572 |         used to set bounds on individual parameters.
 573 | 
 574 |     eqcons : list, optional
 575 |         List of functions of length n such that ``eqcons[j](x0, *args) == 0.0``
 576 |         in a successfully optimized problem.
 577 | 
 578 |     ineqcons : list, optional
 579 |         List of functions of length n such that ``ieqcons[j](x0, *args) &gt;=
 580 |         0.0`` is a successfully optimized problem.
 581 | 
 582 |     Examples
 583 |     --------
 584 |     &gt;&gt;&gt; from astropy.modeling import models
 585 |     &gt;&gt;&gt; def tie_center(model):
 586 |     ...         mean = 50 * model.stddev
 587 |     ...         return mean
 588 |     &gt;&gt;&gt; tied_parameters = {&#39;mean&#39;: tie_center}
 589 | 
 590 |     Specify that ``&#39;mean&#39;`` is a tied parameter in one of two ways:
 591 | 
 592 |     &gt;&gt;&gt; g1 = models.Gaussian1D(amplitude=10, mean=5, stddev=.3,
 593 |     ...                        tied=tied_parameters)
 594 | 
 595 |     or
 596 | 
 597 |     &gt;&gt;&gt; g1 = models.Gaussian1D(amplitude=10, mean=5, stddev=.3)
 598 |     &gt;&gt;&gt; g1.mean.tied
 599 |     False
 600 |     &gt;&gt;&gt; g1.mean.tied = tie_center
 601 |     &gt;&gt;&gt; g1.mean.tied
 602 |     &lt;function tie_center at 0x...&gt;
 603 | 
 604 |     Fixed parameters:
 605 | 
 606 |     &gt;&gt;&gt; g1 = models.Gaussian1D(amplitude=10, mean=5, stddev=.3,
 607 |     ...                        fixed={&#39;stddev&#39;: True})
 608 |     &gt;&gt;&gt; g1.stddev.fixed
 609 |     True
 610 | 
 611 |     or
 612 | 
 613 |     &gt;&gt;&gt; g1 = models.Gaussian1D(amplitude=10, mean=5, stddev=.3)
 614 |     &gt;&gt;&gt; g1.stddev.fixed
 615 |     False
 616 |     &gt;&gt;&gt; g1.stddev.fixed = True
 617 |     &gt;&gt;&gt; g1.stddev.fixed
 618 |     True
 619 |     &quot;&quot;&quot;
 620 | 
 621 |     parameter_constraints = Parameter.constraints
 622 |     &quot;&quot;&quot;
 623 |     Primarily for informational purposes, these are the types of constraints
 624 |     that can be set on a model&#39;s parameters.
 625 |     &quot;&quot;&quot;
 626 | 
 627 |     model_constraints = (&#39;eqcons&#39;, &#39;ineqcons&#39;)
 628 |     &quot;&quot;&quot;
 629 |     Primarily for informational purposes, these are the types of constraints
 630 |     that constrain model evaluation.
 631 |     &quot;&quot;&quot;
 632 | 
 633 |     param_names = ()
 634 |     &quot;&quot;&quot;
 635 |     Names of the parameters that describe models of this type.
 636 | 
 637 |     The parameters in this tuple are in the same order they should be passed in
 638 |     when initializing a model of a specific type.  Some types of models, such
 639 |     as polynomial models, have a different number of parameters depending on
 640 |     some other property of the model, such as the degree.
 641 | 
 642 |     When defining a custom model class the value of this attribute is
 643 |     automatically set by the `~astropy.modeling.Parameter` attributes defined
 644 |     in the class body.
 645 |     &quot;&quot;&quot;
 646 | 
 647 |     n_inputs = 0
 648 |     &quot;&quot;&quot;The number of inputs.&quot;&quot;&quot;
 649 |     n_outputs = 0
 650 |     &quot;&quot;&quot; The number of outputs.&quot;&quot;&quot;
 651 | 
 652 |     standard_broadcasting = True
 653 |     fittable = False
 654 |     linear = True
 655 |     _separable = None
 656 |     &quot;&quot;&quot; A boolean flag to indicate whether a model is separable.&quot;&quot;&quot;
 657 |     meta = metadata.MetaData()
 658 |     &quot;&quot;&quot;A dict-like object to store optional information.&quot;&quot;&quot;
 659 | 
 660 |     # By default models either use their own inverse property or have no
 661 |     # inverse at all, but users may also assign a custom inverse to a model,
 662 |     # optionally; in that case it is of course up to the user to determine
 663 |     # whether their inverse is *actually* an inverse to the model they assign
 664 |     # it to.
 665 |     _inverse = None
 666 |     _user_inverse = None
 667 | 
 668 |     _bounding_box = None
 669 |     _user_bounding_box = None
 670 | 
 671 |     _has_inverse_bounding_box = False
 672 | 
 673 |     # Default n_models attribute, so that __len__ is still defined even when a
 674 |     # model hasn&#39;t completed initialization yet
 675 |     _n_models = 1
 676 | 
 677 |     # New classes can set this as a boolean value.
 678 |     # It is converted to a dictionary mapping input name to a boolean value.
 679 |     _input_units_strict = False
 680 | 
 681 |     # Allow dimensionless input (and corresponding output). If this is True,
 682 |     # input values to evaluate will gain the units specified in input_units. If
 683 |     # this is a dictionary then it should map input name to a bool to allow
 684 |     # dimensionless numbers for that input.
 685 |     # Only has an effect if input_units is defined.
 686 |     _input_units_allow_dimensionless = False
 687 | 
 688 |     # Default equivalencies to apply to input values. If set, this should be a
 689 |     # dictionary where each key is a string that corresponds to one of the
 690 |     # model inputs. Only has an effect if input_units is defined.
 691 |     input_units_equivalencies = None
 692 | 
 693 |     # Covariance matrix can be set by fitter if available.
 694 |     # If cov_matrix is available, then std will set as well
 695 |     _cov_matrix = None
 696 |     _stds = None
 697 | 
 698 |     def __init_subclass__(cls, **kwargs):
 699 |         super().__init_subclass__()
 700 | 
 701 |     def __init__(self, *args, meta=None, name=None, **kwargs):
 702 |         super().__init__()
 703 |         self._default_inputs_outputs()
 704 |         if meta is not None:
 705 |             self.meta = meta
 706 |         self._name = name
 707 |         # add parameters to instance level by walking MRO list
 708 |         mro = self.__class__.__mro__
 709 |         for cls in mro:
 710 |             if issubclass(cls, Model):
 711 |                 for parname, val in cls._parameters_.items():
 712 |                     newpar = copy.deepcopy(val)
 713 |                     newpar.model = self
 714 |                     if parname not in self.__dict__:
 715 |                         self.__dict__[parname] = newpar
 716 | 
 717 |         self._initialize_constraints(kwargs)
 718 |         kwargs = self._initialize_setters(kwargs)
 719 |         # Remaining keyword args are either parameter values or invalid
 720 |         # Parameter values must be passed in as keyword arguments in order to
 721 |         # distinguish them
 722 |         self._initialize_parameters(args, kwargs)
 723 |         self._initialize_slices()
 724 |         self._initialize_unit_support()
 725 | 
 726 |     def _default_inputs_outputs(self):
 727 |         if self.n_inputs == 1 and self.n_outputs == 1:
 728 |             self._inputs = (&quot;x&quot;,)
 729 |             self._outputs = (&quot;y&quot;,)
 730 |         elif self.n_inputs == 2 and self.n_outputs == 1:
 731 |             self._inputs = (&quot;x&quot;, &quot;y&quot;)
 732 |             self._outputs = (&quot;z&quot;,)
 733 |         else:
 734 |             try:
 735 |                 self._inputs = tuple(&quot;x&quot; + str(idx) for idx in range(self.n_inputs))
 736 |                 self._outputs = tuple(&quot;x&quot; + str(idx) for idx in range(self.n_outputs))
 737 |             except TypeError:
 738 |                 # self.n_inputs and self.n_outputs are properties
 739 |                 # This is the case when subclasses of Model do not define
 740 |                 # ``n_inputs``, ``n_outputs``, ``inputs`` or ``outputs``.
 741 |                 self._inputs = ()
 742 |                 self._outputs = ()
 743 | 
 744 |     def _initialize_setters(self, kwargs):
 745 |         &quot;&quot;&quot;
 746 |         This exists to inject defaults for settable properties for models
 747 |         originating from `custom_model`.
 748 |         &quot;&quot;&quot;
 749 |         if hasattr(self, &#39;_settable_properties&#39;):
 750 |             setters = {name: kwargs.pop(name, default)
 751 |                        for name, default in self._settable_properties.items()}
 752 |             for name, value in setters.items():
 753 |                 setattr(self, name, value)
 754 | 
 755 |         return kwargs
 756 | 
 757 |     @property
 758 |     def inputs(self):
 759 |         return self._inputs
 760 | 
 761 |     @inputs.setter
 762 |     def inputs(self, val):
 763 |         if len(val) != self.n_inputs:
 764 |             raise ValueError(f&quot;Expected {self.n_inputs} number of inputs, got {len(val)}.&quot;)
 765 |         self._inputs = val
 766 |         self._initialize_unit_support()
 767 | 
 768 |     @property
 769 |     def outputs(self):
 770 |         return self._outputs
 771 | 
 772 |     @outputs.setter
 773 |     def outputs(self, val):
 774 |         if len(val) != self.n_outputs:
 775 |             raise ValueError(f&quot;Expected {self.n_outputs} number of outputs, got {len(val)}.&quot;)
 776 |         self._outputs = val
 777 | 
 778 |     @property
 779 |     def n_inputs(self):
 780 |         # TODO: remove the code in the ``if`` block when support
 781 |         # for models with ``inputs`` as class variables is removed.
 782 |         if hasattr(self.__class__, &#39;n_inputs&#39;) and isinstance(self.__class__.n_inputs, property):
 783 |             try:
 784 |                 return len(self.__class__.inputs)
 785 |             except TypeError:
 786 |                 try:
 787 |                     return len(self.inputs)
 788 |                 except AttributeError:
 789 |                     return 0
 790 | 
 791 |         return self.__class__.n_inputs
 792 | 
 793 |     @property
 794 |     def n_outputs(self):
 795 |         # TODO: remove the code in the ``if`` block when support
 796 |         # for models with ``outputs`` as class variables is removed.
 797 |         if hasattr(self.__class__, &#39;n_outputs&#39;) and isinstance(self.__class__.n_outputs, property):
 798 |             try:
 799 |                 return len(self.__class__.outputs)
 800 |             except TypeError:
 801 |                 try:
 802 |                     return len(self.outputs)
 803 |                 except AttributeError:
 804 |                     return 0
 805 | 
 806 |         return self.__class__.n_outputs
 807 | 
 808 |     def _calculate_separability_matrix(self):
 809 |         &quot;&quot;&quot;
 810 |         This is a hook which customises the behavior of modeling.separable.
 811 | 
 812 |         This allows complex subclasses to customise the separability matrix.
 813 |         If it returns `NotImplemented` the default behavior is used.
 814 |         &quot;&quot;&quot;
 815 |         return NotImplemented
 816 | 
 817 |     def _initialize_unit_support(self):
 818 |         &quot;&quot;&quot;
 819 |         Convert self._input_units_strict and
 820 |         self.input_units_allow_dimensionless to dictionaries
 821 |         mapping input name to a boolean value.
 822 |         &quot;&quot;&quot;
 823 |         if isinstance(self._input_units_strict, bool):
 824 |             self._input_units_strict = {key: self._input_units_strict for
 825 |                                         key in self.inputs}
 826 | 
 827 |         if isinstance(self._input_units_allow_dimensionless, bool):
 828 |             self._input_units_allow_dimensionless = {key: self._input_units_allow_dimensionless
 829 |                                                      for key in self.inputs}
 830 | 
 831 |     @property
 832 |     def input_units_strict(self):
 833 |         &quot;&quot;&quot;
 834 |         Enforce strict units on inputs to evaluate. If this is set to True,
 835 |         input values to evaluate will be in the exact units specified by
 836 |         input_units. If the input quantities are convertible to input_units,
 837 |         they are converted. If this is a dictionary then it should map input
 838 |         name to a bool to set strict input units for that parameter.
 839 |         &quot;&quot;&quot;
 840 |         val = self._input_units_strict
 841 |         if isinstance(val, bool):
 842 |             return {key: val for key in self.inputs}
 843 |         return dict(zip(self.inputs, val.values()))
 844 | 
 845 |     @property
 846 |     def input_units_allow_dimensionless(self):
 847 |         &quot;&quot;&quot;
 848 |         Allow dimensionless input (and corresponding output). If this is True,
 849 |         input values to evaluate will gain the units specified in input_units. If
 850 |         this is a dictionary then it should map input name to a bool to allow
 851 |         dimensionless numbers for that input.
 852 |         Only has an effect if input_units is defined.
 853 |         &quot;&quot;&quot;
 854 | 
 855 |         val = self._input_units_allow_dimensionless
 856 |         if isinstance(val, bool):
 857 |             return {key: val for key in self.inputs}
 858 |         return dict(zip(self.inputs, val.values()))
 859 | 
 860 |     @property
 861 |     def uses_quantity(self):
 862 |         &quot;&quot;&quot;
 863 |         True if this model has been created with `~astropy.units.Quantity`
 864 |         objects or if there are no parameters.
 865 | 
 866 |         This can be used to determine if this model should be evaluated with
 867 |         `~astropy.units.Quantity` or regular floats.
 868 |         &quot;&quot;&quot;
 869 |         pisq = [isinstance(p, Quantity) for p in self._param_sets(units=True)]
 870 |         return (len(pisq) == 0) or any(pisq)
 871 | 
 872 |     def __repr__(self):
 873 |         return self._format_repr()
 874 | 
 875 |     def __str__(self):
 876 |         return self._format_str()
 877 | 
 878 |     def __len__(self):
 879 |         return self._n_models
 880 | 
 881 |     @staticmethod
 882 |     def _strip_ones(intup):
 883 |         return tuple(item for item in intup if item != 1)
 884 | 
 885 |     def __setattr__(self, attr, value):
 886 |         if isinstance(self, CompoundModel):
 887 |             param_names = self._param_names
 888 |         param_names = self.param_names
 889 | 
 890 |         if param_names is not None and attr in self.param_names:
 891 |             param = self.__dict__[attr]
 892 |             value = _tofloat(value)
 893 |             if param._validator is not None:
 894 |                 param._validator(self, value)
 895 |             # check consistency with previous shape and size
 896 |             eshape = self._param_metrics[attr][&#39;shape&#39;]
 897 |             if eshape == ():
 898 |                 eshape = (1,)
 899 |             vshape = np.array(value).shape
 900 |             if vshape == ():
 901 |                 vshape = (1,)
 902 |             esize = self._param_metrics[attr][&#39;size&#39;]
 903 |             if (np.size(value) != esize or
 904 |                     self._strip_ones(vshape) != self._strip_ones(eshape)):
 905 |                 raise InputParameterError(
 906 |                     &quot;Value for parameter {0} does not match shape or size\n&quot;
 907 |                     &quot;expected by model ({1}, {2}) vs ({3}, {4})&quot;.format(
 908 |                         attr, vshape, np.size(value), eshape, esize))
 909 |             if param.unit is None:
 910 |                 if isinstance(value, Quantity):
 911 |                     param._unit = value.unit
 912 |                     param.value = value.value
 913 |                 else:
 914 |                     param.value = value
 915 |             else:
 916 |                 if not isinstance(value, Quantity):
 917 |                     raise UnitsError(f&quot;The &#39;{param.name}&#39; parameter should be given as a&quot;
 918 |                                      &quot; Quantity because it was originally &quot;
 919 |                                      &quot;initialized as a Quantity&quot;)
 920 |                 param._unit = value.unit
 921 |                 param.value = value.value
 922 |         else:
 923 |             if attr in [&#39;fittable&#39;, &#39;linear&#39;]:
 924 |                 self.__dict__[attr] = value
 925 |             else:
 926 |                 super().__setattr__(attr, value)
 927 | 
 928 |     def _pre_evaluate(self, *args, **kwargs):
 929 |         &quot;&quot;&quot;
 930 |         Model specific input setup that needs to occur prior to model evaluation
 931 |         &quot;&quot;&quot;
 932 | 
 933 |         # Broadcast inputs into common size
 934 |         inputs, broadcasted_shapes = self.prepare_inputs(*args, **kwargs)
 935 | 
 936 |         # Setup actual model evaluation method
 937 |         parameters = self._param_sets(raw=True, units=True)
 938 | 
 939 |         def evaluate(_inputs):
 940 |             return self.evaluate(*chain(_inputs, parameters))
 941 | 
 942 |         return evaluate, inputs, broadcasted_shapes, kwargs
 943 | 
 944 |     def get_bounding_box(self, with_bbox=True):
 945 |         &quot;&quot;&quot;
 946 |         Return the ``bounding_box`` of a model if it exists or ``None``
 947 |         otherwise.
 948 | 
 949 |         Parameters
 950 |         ----------
 951 |         with_bbox :
 952 |             The value of the ``with_bounding_box`` keyword argument
 953 |             when calling the model. Default is `True` for usage when
 954 |             looking up the model&#39;s ``bounding_box`` without risk of error.
 955 |         &quot;&quot;&quot;
 956 |         bbox = None
 957 | 
 958 |         if not isinstance(with_bbox, bool) or with_bbox:
 959 |             try:
 960 |                 bbox = self.bounding_box
 961 |             except NotImplementedError:
 962 |                 pass
 963 | 
 964 |             if isinstance(bbox, CompoundBoundingBox) and not isinstance(with_bbox, bool):
 965 |                 bbox = bbox[with_bbox]
 966 | 
 967 |         return bbox
 968 | 
 969 |     @property
 970 |     def _argnames(self):
 971 |         &quot;&quot;&quot;The inputs used to determine input_shape for bounding_box evaluation&quot;&quot;&quot;
 972 |         return self.inputs
 973 | 
 974 |     def _validate_input_shape(self, _input, idx, argnames, model_set_axis, check_model_set_axis):
 975 |         &quot;&quot;&quot;
 976 |         Perform basic validation of a single model input&#39;s shape
 977 |             -- it has the minimum dimensions for the given model_set_axis
 978 | 
 979 |         Returns the shape of the input if validation succeeds.
 980 |         &quot;&quot;&quot;
 981 |         input_shape = np.shape(_input)
 982 |         # Ensure that the input&#39;s model_set_axis matches the model&#39;s
 983 |         # n_models
 984 |         if input_shape and check_model_set_axis:
 985 |             # Note: Scalar inputs *only* get a pass on this
 986 |             if len(input_shape) &lt; model_set_axis + 1:
 987 |                 raise ValueError(
 988 |                     f&quot;For model_set_axis={model_set_axis}, all inputs must be at &quot;
 989 |                     f&quot;least {model_set_axis + 1}-dimensional.&quot;)
 990 |             if input_shape[model_set_axis] != self._n_models:
 991 |                 try:
 992 |                     argname = argnames[idx]
 993 |                 except IndexError:
 994 |                     # the case of model.inputs = ()
 995 |                     argname = str(idx)
 996 | 
 997 |                 raise ValueError(
 998 |                     f&quot;Input argument &#39;{argname}&#39; does not have the correct &quot;
 999 |                     f&quot;dimensions in model_set_axis={model_set_axis} for a model set with &quot;
1000 |                     f&quot;n_models={self._n_models}.&quot;)
1001 | 
1002 |         return input_shape
1003 | 
1004 |     def _validate_input_shapes(self, inputs, argnames, model_set_axis):
1005 |         &quot;&quot;&quot;
1006 |         Perform basic validation of model inputs
1007 |             --that they are mutually broadcastable and that they have
1008 |             the minimum dimensions for the given model_set_axis.
1009 | 
1010 |         If validation succeeds, returns the total shape that will result from
1011 |         broadcasting the input arrays with each other.
1012 |         &quot;&quot;&quot;
1013 | 
1014 |         check_model_set_axis = self._n_models &gt; 1 and model_set_axis is not False
1015 | 
1016 |         all_shapes = []
1017 |         for idx, _input in enumerate(inputs):
1018 |             all_shapes.append(self._validate_input_shape(_input, idx, argnames,
1019 |                                                          model_set_axis, check_model_set_axis))
1020 | 
1021 |         input_shape = check_broadcast(*all_shapes)
1022 |         if input_shape is None:
1023 |             raise ValueError(
1024 |                 &quot;All inputs must have identical shapes or must be scalars.&quot;)
1025 | 
1026 |         return input_shape
1027 | 
1028 |     def input_shape(self, inputs):
1029 |         &quot;&quot;&quot;Get input shape for bounding_box evaluation&quot;&quot;&quot;
1030 |         return self._validate_input_shapes(inputs, self._argnames, self.model_set_axis)
1031 | 
1032 |     def _generic_evaluate(self, evaluate, _inputs, fill_value, with_bbox):
1033 |         &quot;&quot;&quot;
1034 |         Generic model evaluation routine
1035 |             Selects and evaluates model with or without bounding_box enforcement
1036 |         &quot;&quot;&quot;
1037 | 
1038 |         # Evaluate the model using the prepared evaluation method either
1039 |         #   enforcing the bounding_box or not.
1040 |         bbox = self.get_bounding_box(with_bbox)
1041 |         if (not isinstance(with_bbox, bool) or with_bbox) and bbox is not None:
1042 |             outputs = bbox.evaluate(evaluate, _inputs, fill_value)
1043 |         else:
1044 |             outputs = evaluate(_inputs)
1045 |         return outputs
1046 | 
1047 |     def _post_evaluate(self, inputs, outputs, broadcasted_shapes, with_bbox, **kwargs):
1048 |         &quot;&quot;&quot;
1049 |         Model specific post evaluation processing of outputs
1050 |         &quot;&quot;&quot;
1051 |         if self.get_bounding_box(with_bbox) is None and self.n_outputs == 1:
1052 |             outputs = (outputs,)
1053 | 
1054 |         outputs = self.prepare_outputs(broadcasted_shapes, *outputs, **kwargs)
1055 |         outputs = self._process_output_units(inputs, outputs)
1056 | 
1057 |         if self.n_outputs == 1:
1058 |             return outputs[0]
1059 |         return outputs
1060 | 
1061 |     @property
1062 |     def bbox_with_units(self):
1063 |         return (not isinstance(self, CompoundModel))
1064 | 
1065 |     def __call__(self, *args, **kwargs):
1066 |         &quot;&quot;&quot;
1067 |         Evaluate this model using the given input(s) and the parameter values
1068 |         that were specified when the model was instantiated.
1069 |         &quot;&quot;&quot;
1070 |         # Turn any keyword arguments into positional arguments.
1071 |         args, kwargs = self._get_renamed_inputs_as_positional(*args, **kwargs)
1072 | 
1073 |         # Read model evaluation related parameters
1074 |         with_bbox = kwargs.pop(&#39;with_bounding_box&#39;, False)
1075 |         fill_value = kwargs.pop(&#39;fill_value&#39;, np.nan)
1076 | 
1077 |         # prepare for model evaluation (overridden in CompoundModel)
1078 |         evaluate, inputs, broadcasted_shapes, kwargs = self._pre_evaluate(*args, **kwargs)
1079 | 
1080 |         outputs = self._generic_evaluate(evaluate, inputs,
1081 |                                          fill_value, with_bbox)
1082 | 
1083 |         # post-process evaluation results (overridden in CompoundModel)
1084 |         return self._post_evaluate(inputs, outputs, broadcasted_shapes, with_bbox, **kwargs)
1085 | 
1086 |     def _get_renamed_inputs_as_positional(self, *args, **kwargs):
1087 |         def _keyword2positional(kwargs):
1088 |             # Inputs were passed as keyword (not positional) arguments.
1089 |             # Because the signature of the ``__call__`` is defined at
1090 |             # the class level, the name of the inputs cannot be changed at
1091 |             # the instance level and the old names are always present in the
1092 |             # signature of the method. In order to use the new names of the
1093 |             # inputs, the old names are taken out of ``kwargs``, the input
1094 |             # values are sorted in the order of self.inputs and passed as
1095 |             # positional arguments to ``__call__``.
1096 | 
1097 |             # These are the keys that are always present as keyword arguments.
1098 |             keys = [&#39;model_set_axis&#39;, &#39;with_bounding_box&#39;, &#39;fill_value&#39;,
1099 |                     &#39;equivalencies&#39;, &#39;inputs_map&#39;]
1100 | 
1101 |             new_inputs = {}
1102 |             # kwargs contain the names of the new inputs + ``keys``
1103 |             allkeys = list(kwargs.keys())
1104 |             # Remove the names of the new inputs from kwargs and save them
1105 |             # to a dict ``new_inputs``.
1106 |             for key in allkeys:
1107 |                 if key not in keys:
1108 |                     new_inputs[key] = kwargs[key]
1109 |                     del kwargs[key]
1110 |             return new_inputs, kwargs
1111 |         n_args = len(args)
1112 | 
1113 |         new_inputs, kwargs = _keyword2positional(kwargs)
1114 |         n_all_args = n_args + len(new_inputs)
1115 | 
1116 |         if n_all_args &lt; self.n_inputs:
1117 |             raise ValueError(f&quot;Missing input arguments - expected {self.n_inputs}, got {n_all_args}&quot;)
1118 |         elif n_all_args &gt; self.n_inputs:
1119 |             raise ValueError(f&quot;Too many input arguments - expected {self.n_inputs}, got {n_all_args}&quot;)
1120 |         if n_args == 0:
1121 |             # Create positional arguments from the keyword arguments in ``new_inputs``.
1122 |             new_args = []
1123 |             for k in self.inputs:
1124 |                 new_args.append(new_inputs[k])
1125 |         elif n_args != self.n_inputs:
1126 |             # Some inputs are passed as positional, others as keyword arguments.
1127 |             args = list(args)
1128 | 
1129 |             # Create positional arguments from the keyword arguments in ``new_inputs``.
1130 |             new_args = []
1131 |             for k in self.inputs:
1132 |                 if k in new_inputs:
1133 |                     new_args.append(new_inputs[k])
1134 |                 else:
1135 |                     new_args.append(args[0])
1136 |                     del args[0]
1137 |         else:
1138 |             new_args = args
1139 |         return new_args, kwargs
1140 | 
1141 |     # *** Properties ***
1142 |     @property
1143 |     def name(self):
1144 |         &quot;&quot;&quot;User-provided name for this model instance.&quot;&quot;&quot;
1145 | 
1146 |         return self._name
1147 | 
1148 |     @name.setter
1149 |     def name(self, val):
1150 |         &quot;&quot;&quot;Assign a (new) name to this model.&quot;&quot;&quot;
1151 | 
1152 |         self._name = val
1153 | 
1154 |     @property
1155 |     def model_set_axis(self):
1156 |         &quot;&quot;&quot;
1157 |         The index of the model set axis--that is the axis of a parameter array
1158 |         that pertains to which model a parameter value pertains to--as
1159 |         specified when the model was initialized.
1160 | 
1161 |         See the documentation on :ref:`astropy:modeling-model-sets`
1162 |         for more details.
1163 |         &quot;&quot;&quot;
1164 | 
1165 |         return self._model_set_axis
1166 | 
1167 |     @property
1168 |     def param_sets(self):
1169 |         &quot;&quot;&quot;
1170 |         Return parameters as a pset.
1171 | 
1172 |         This is a list with one item per parameter set, which is an array of
1173 |         that parameter&#39;s values across all parameter sets, with the last axis
1174 |         associated with the parameter set.
1175 |         &quot;&quot;&quot;
1176 | 
1177 |         return self._param_sets()
1178 | 
1179 |     @property
1180 |     def parameters(self):
1181 |         &quot;&quot;&quot;
1182 |         A flattened array of all parameter values in all parameter sets.
1183 | 
1184 |         Fittable parameters maintain this list and fitters modify it.
1185 |         &quot;&quot;&quot;
1186 | 
1187 |         # Currently the sequence of a model&#39;s parameters must be contiguous
1188 |         # within the _parameters array (which may be a view of a larger array,
1189 |         # for example when taking a sub-expression of a compound model), so
1190 |         # the assumption here is reliable:
1191 |         if not self.param_names:
1192 |             # Trivial, but not unheard of
1193 |             return self._parameters
1194 | 
1195 |         self._parameters_to_array()
1196 |         start = self._param_metrics[self.param_names[0]][&#39;slice&#39;].start
1197 |         stop = self._param_metrics[self.param_names[-1]][&#39;slice&#39;].stop
1198 | 
1199 |         return self._parameters[start:stop]
1200 | 
1201 |     @parameters.setter
1202 |     def parameters(self, value):
1203 |         &quot;&quot;&quot;
1204 |         Assigning to this attribute updates the parameters array rather than
1205 |         replacing it.
1206 |         &quot;&quot;&quot;
1207 | 
1208 |         if not self.param_names:
1209 |             return
1210 | 
1211 |         start = self._param_metrics[self.param_names[0]][&#39;slice&#39;].start
1212 |         stop = self._param_metrics[self.param_names[-1]][&#39;slice&#39;].stop
1213 | 
1214 |         try:
1215 |             value = np.array(value).flatten()
1216 |             self._parameters[start:stop] = value
1217 |         except ValueError as e:
1218 |             raise InputParameterError(
1219 |                 &quot;Input parameter values not compatible with the model &quot;
1220 |                 &quot;parameters array: {0}&quot;.format(e))
1221 |         self._array_to_parameters()
1222 | 
1223 |     @property
1224 |     def sync_constraints(self):
1225 |         &#39;&#39;&#39;
1226 |         This is a boolean property that indicates whether or not accessing constraints
1227 |         automatically check the constituent models current values. It defaults to True
1228 |         on creation of a model, but for fitting purposes it should be set to False
1229 |         for performance reasons.
1230 |         &#39;&#39;&#39;
1231 |         if not hasattr(self, &#39;_sync_constraints&#39;):
1232 |             self._sync_constraints = True
1233 |         return self._sync_constraints
1234 | 
1235 |     @sync_constraints.setter
1236 |     def sync_constraints(self, value):
1237 |         if not isinstance(value, bool):
1238 |             raise ValueError(&#39;sync_constraints only accepts True or False as values&#39;)
1239 |         self._sync_constraints = value
1240 | 
1241 |     @property
1242 |     def fixed(self):
1243 |         &quot;&quot;&quot;
1244 |         A ``dict`` mapping parameter names to their fixed constraint.
1245 |         &quot;&quot;&quot;
1246 |         if not hasattr(self, &#39;_fixed&#39;) or self.sync_constraints:
1247 |             self._fixed = _ConstraintsDict(self, &#39;fixed&#39;)
1248 |         return self._fixed
1249 | 
1250 |     @property
1251 |     def bounds(self):
1252 |         &quot;&quot;&quot;
1253 |         A ``dict`` mapping parameter names to their upper and lower bounds as
1254 |         ``(min, max)`` tuples or ``[min, max]`` lists.
1255 |         &quot;&quot;&quot;
1256 |         if not hasattr(self, &#39;_bounds&#39;) or self.sync_constraints:
1257 |             self._bounds = _ConstraintsDict(self, &#39;bounds&#39;)
1258 |         return self._bounds
1259 | 
1260 |     @property
1261 |     def tied(self):
1262 |         &quot;&quot;&quot;
1263 |         A ``dict`` mapping parameter names to their tied constraint.
1264 |         &quot;&quot;&quot;
1265 |         if not hasattr(self, &#39;_tied&#39;) or self.sync_constraints:
1266 |             self._tied = _ConstraintsDict(self, &#39;tied&#39;)
1267 |         return self._tied
1268 | 
1269 |     @property
1270 |     def eqcons(self):
1271 |         &quot;&quot;&quot;List of parameter equality constraints.&quot;&quot;&quot;
1272 | 
1273 |         return self._mconstraints[&#39;eqcons&#39;]
1274 | 
1275 |     @property
1276 |     def ineqcons(self):
1277 |         &quot;&quot;&quot;List of parameter inequality constraints.&quot;&quot;&quot;
1278 | 
1279 |         return self._mconstraints[&#39;ineqcons&#39;]
1280 | 
1281 |     def has_inverse(self):
1282 |         &quot;&quot;&quot;
1283 |         Returns True if the model has an analytic or user
1284 |         inverse defined.
1285 |         &quot;&quot;&quot;
1286 |         try:
1287 |             self.inverse
1288 |         except NotImplementedError:
1289 |             return False
1290 | 
1291 |         return True
1292 | 
1293 |     @property
1294 |     def inverse(self):
1295 |         &quot;&quot;&quot;
1296 |         Returns a new `~astropy.modeling.Model` instance which performs the
1297 |         inverse transform, if an analytic inverse is defined for this model.
1298 | 
1299 |         Even on models that don&#39;t have an inverse defined, this property can be
1300 |         set with a manually-defined inverse, such a pre-computed or
1301 |         experimentally determined inverse (often given as a
1302 |         `~astropy.modeling.polynomial.PolynomialModel`, but not by
1303 |         requirement).
1304 | 
1305 |         A custom inverse can be deleted with ``del model.inverse``.  In this
1306 |         case the model&#39;s inverse is reset to its default, if a default exists
1307 |         (otherwise the default is to raise `NotImplementedError`).
1308 | 
1309 |         Note to authors of `~astropy.modeling.Model` subclasses:  To define an
1310 |         inverse for a model simply override this property to return the
1311 |         appropriate model representing the inverse.  The machinery that will
1312 |         make the inverse manually-overridable is added automatically by the
1313 |         base class.
1314 |         &quot;&quot;&quot;
1315 |         if self._user_inverse is not None:
1316 |             return self._user_inverse
1317 |         elif self._inverse is not None:
1318 |             result = self._inverse()
1319 |             if result is not NotImplemented:
1320 |                 if not self._has_inverse_bounding_box:
1321 |                     result.bounding_box = None
1322 |                 return result
1323 | 
1324 |         raise NotImplementedError(&quot;No analytical or user-supplied inverse transform &quot;
1325 |                                   &quot;has been implemented for this model.&quot;)
1326 | 
1327 |     @inverse.setter
1328 |     def inverse(self, value):
1329 |         if not isinstance(value, (Model, type(None))):
1330 |             raise ValueError(
1331 |                 &quot;The ``inverse`` attribute may be assigned a `Model` &quot;
1332 |                 &quot;instance or `None` (where `None` explicitly forces the &quot;
1333 |                 &quot;model to have no inverse.&quot;)
1334 | 
1335 |         self._user_inverse = value
1336 | 
1337 |     @inverse.deleter
1338 |     def inverse(self):
1339 |         &quot;&quot;&quot;
1340 |         Resets the model&#39;s inverse to its default (if one exists, otherwise
1341 |         the model will have no inverse).
1342 |         &quot;&quot;&quot;
1343 | 
1344 |         try:
1345 |             del self._user_inverse
1346 |         except AttributeError:
1347 |             pass
1348 | 
1349 |     @property
1350 |     def has_user_inverse(self):
1351 |         &quot;&quot;&quot;
1352 |         A flag indicating whether or not a custom inverse model has been
1353 |         assigned to this model by a user, via assignment to ``model.inverse``.
1354 |         &quot;&quot;&quot;
1355 |         return self._user_inverse is not None
1356 | 
1357 |     @property
1358 |     def bounding_box(self):
1359 |         r&quot;&quot;&quot;
1360 |         A `tuple` of length `n_inputs` defining the bounding box limits, or
1361 |         raise `NotImplementedError` for no bounding_box.
1362 | 
1363 |         The default limits are given by a ``bounding_box`` property or method
1364 |         defined in the class body of a specific model.  If not defined then
1365 |         this property just raises `NotImplementedError` by default (but may be
1366 |         assigned a custom value by a user).  ``bounding_box`` can be set
1367 |         manually to an array-like object of shape ``(model.n_inputs, 2)``. For
1368 |         further usage, see :ref:`astropy:bounding-boxes`
1369 | 
1370 |         The limits are ordered according to the `numpy` ``&#39;C&#39;`` indexing
1371 |         convention, and are the reverse of the model input order,
1372 |         e.g. for inputs ``(&#39;x&#39;, &#39;y&#39;, &#39;z&#39;)``, ``bounding_box`` is defined:
1373 | 
1374 |         * for 1D: ``(x_low, x_high)``
1375 |         * for 2D: ``((y_low, y_high), (x_low, x_high))``
1376 |         * for 3D: ``((z_low, z_high), (y_low, y_high), (x_low, x_high))``
1377 | 
1378 |         Examples
1379 |         --------
1380 | 
1381 |         Setting the ``bounding_box`` limits for a 1D and 2D model:
1382 | 
1383 |         &gt;&gt;&gt; from astropy.modeling.models import Gaussian1D, Gaussian2D
1384 |         &gt;&gt;&gt; model_1d = Gaussian1D()
1385 |         &gt;&gt;&gt; model_2d = Gaussian2D(x_stddev=1, y_stddev=1)
1386 |         &gt;&gt;&gt; model_1d.bounding_box = (-5, 5)
1387 |         &gt;&gt;&gt; model_2d.bounding_box = ((-6, 6), (-5, 5))
1388 | 
1389 |         Setting the bounding_box limits for a user-defined 3D `custom_model`:
1390 | 
1391 |         &gt;&gt;&gt; from astropy.modeling.models import custom_model
1392 |         &gt;&gt;&gt; def const3d(x, y, z, amp=1):
1393 |         ...    return amp
1394 |         ...
1395 |         &gt;&gt;&gt; Const3D = custom_model(const3d)
1396 |         &gt;&gt;&gt; model_3d = Const3D()
1397 |         &gt;&gt;&gt; model_3d.bounding_box = ((-6, 6), (-5, 5), (-4, 4))
1398 | 
1399 |         To reset ``bounding_box`` to its default limits just delete the
1400 |         user-defined value--this will reset it back to the default defined
1401 |         on the class:
1402 | 
1403 |         &gt;&gt;&gt; del model_1d.bounding_box
1404 | 
1405 |         To disable the bounding box entirely (including the default),
1406 |         set ``bounding_box`` to `None`:
1407 | 
1408 |         &gt;&gt;&gt; model_1d.bounding_box = None
1409 |         &gt;&gt;&gt; model_1d.bounding_box  # doctest: +IGNORE_EXCEPTION_DETAIL
1410 |         Traceback (most recent call last):
1411 |         NotImplementedError: No bounding box is defined for this model
1412 |         (note: the bounding box was explicitly disabled for this model;
1413 |         use `del model.bounding_box` to restore the default bounding box,
1414 |         if one is defined for this model).
1415 |         &quot;&quot;&quot;
1416 | 
1417 |         if self._user_bounding_box is not None:
1418 |             if self._user_bounding_box is NotImplemented:
1419 |                 raise NotImplementedError(
1420 |                     &quot;No bounding box is defined for this model (note: the &quot;
1421 |                     &quot;bounding box was explicitly disabled for this model; &quot;
1422 |                     &quot;use `del model.bounding_box` to restore the default &quot;
1423 |                     &quot;bounding box, if one is defined for this model).&quot;)
1424 |             return self._user_bounding_box
1425 |         elif self._bounding_box is None:
1426 |             raise NotImplementedError(
1427 |                 &quot;No bounding box is defined for this model.&quot;)
1428 |         elif isinstance(self._bounding_box, ModelBoundingBox):
1429 |             # This typically implies a hard-coded bounding box.  This will
1430 |             # probably be rare, but it is an option
1431 |             return self._bounding_box
1432 |         elif isinstance(self._bounding_box, types.MethodType):
1433 |             return ModelBoundingBox.validate(self, self._bounding_box())
1434 |         else:
1435 |             # The only other allowed possibility is that it&#39;s a ModelBoundingBox
1436 |             # subclass, so we call it with its default arguments and return an
1437 |             # instance of it (that can be called to recompute the bounding box
1438 |             # with any optional parameters)
1439 |             # (In other words, in this case self._bounding_box is a *class*)
1440 |             bounding_box = self._bounding_box((), model=self)()
1441 |             return self._bounding_box(bounding_box, model=self)
1442 | 
1443 |     @bounding_box.setter
1444 |     def bounding_box(self, bounding_box):
1445 |         &quot;&quot;&quot;
1446 |         Assigns the bounding box limits.
1447 |         &quot;&quot;&quot;
1448 | 
1449 |         if bounding_box is None:
1450 |             cls = None
1451 |             # We use this to explicitly set an unimplemented bounding box (as
1452 |             # opposed to no user bounding box defined)
1453 |             bounding_box = NotImplemented
1454 |         elif (isinstance(bounding_box, CompoundBoundingBox) or
1455 |               isinstance(bounding_box, dict)):
1456 |             cls = CompoundBoundingBox
1457 |         elif (isinstance(self._bounding_box, type) and
1458 |               issubclass(self._bounding_box, ModelBoundingBox)):
1459 |             cls = self._bounding_box
1460 |         else:
1461 |             cls = ModelBoundingBox
1462 | 
1463 |         if cls is not None:
1464 |             try:
1465 |                 bounding_box = cls.validate(self, bounding_box, _preserve_ignore=True)
1466 |             except ValueError as exc:
1467 |                 raise ValueError(exc.args[0])
1468 | 
1469 |         self._user_bounding_box = bounding_box
1470 | 
1471 |     def set_slice_args(self, *args):
1472 |         if isinstance(self._user_bounding_box, CompoundBoundingBox):
1473 |             self._user_bounding_box.slice_args = args
1474 |         else:
1475 |             raise RuntimeError(&#39;The bounding_box for this model is not compound&#39;)
1476 | 
1477 |     @bounding_box.deleter
1478 |     def bounding_box(self):
1479 |         self._user_bounding_box = None
1480 | 
1481 |     @property
1482 |     def has_user_bounding_box(self):
1483 |         &quot;&quot;&quot;
1484 |         A flag indicating whether or not a custom bounding_box has been
1485 |         assigned to this model by a user, via assignment to
1486 |         ``model.bounding_box``.
1487 |         &quot;&quot;&quot;
1488 | 
1489 |         return self._user_bounding_box is not None
1490 | 
1491 |     @property
1492 |     def cov_matrix(self):
1493 |         &quot;&quot;&quot;
1494 |         Fitter should set covariance matrix, if available.
1495 |         &quot;&quot;&quot;
1496 |         return self._cov_matrix
1497 | 
1498 |     @cov_matrix.setter
1499 |     def cov_matrix(self, cov):
1500 | 
1501 |         self._cov_matrix = cov
1502 | 
1503 |         unfix_untied_params = [p for p in self.param_names if (self.fixed[p] is False)
1504 |                                and (self.tied[p] is False)]
1505 |         if type(cov) == list:  # model set
1506 |             param_stds = []
1507 |             for c in cov:
1508 |                 param_stds.append([np.sqrt(x) if x &gt; 0 else None for x in np.diag(c.cov_matrix)])
1509 |             for p, param_name in enumerate(unfix_untied_params):
1510 |                 par = getattr(self, param_name)
1511 |                 par.std = [item[p] for item in param_stds]
1512 |                 setattr(self, param_name, par)
1513 |         else:
1514 |             param_stds = [np.sqrt(x) if x &gt; 0 else None for x in np.diag(cov.cov_matrix)]
1515 |             for param_name in unfix_untied_params:
1516 |                 par = getattr(self, param_name)
1517 |                 par.std = param_stds.pop(0)
1518 |                 setattr(self, param_name, par)
1519 | 
1520 |     @property
1521 |     def stds(self):
1522 |         &quot;&quot;&quot;
1523 |         Standard deviation of parameters, if covariance matrix is available.
1524 |         &quot;&quot;&quot;
1525 |         return self._stds
1526 | 
1527 |     @stds.setter
1528 |     def stds(self, stds):
1529 |         self._stds = stds
1530 | 
1531 |     @property
1532 |     def separable(self):
1533 |         &quot;&quot;&quot; A flag indicating whether a model is separable.&quot;&quot;&quot;
1534 | 
1535 |         if self._separable is not None:
1536 |             return self._separable
1537 |         raise NotImplementedError(
1538 |             &#39;The &quot;separable&quot; property is not defined for &#39;
1539 |             &#39;model {}&#39;.format(self.__class__.__name__))
1540 | 
1541 |     # *** Public methods ***
1542 | 
1543 |     def without_units_for_data(self, **kwargs):
1544 |         &quot;&quot;&quot;
1545 |         Return an instance of the model for which the parameter values have
1546 |         been converted to the right units for the data, then the units have
1547 |         been stripped away.
1548 | 
1549 |         The input and output Quantity objects should be given as keyword
1550 |         arguments.
1551 | 
1552 |         Notes
1553 |         -----
1554 | 
1555 |         This method is needed in order to be able to fit models with units in
1556 |         the parameters, since we need to temporarily strip away the units from
1557 |         the model during the fitting (which might be done by e.g. scipy
1558 |         functions).
1559 | 
1560 |         The units that the parameters should be converted to are not
1561 |         necessarily the units of the input data, but are derived from them.
1562 |         Model subclasses that want fitting to work in the presence of
1563 |         quantities need to define a ``_parameter_units_for_data_units`` method
1564 |         that takes the input and output units (as two dictionaries) and
1565 |         returns a dictionary giving the target units for each parameter.
1566 | 
1567 |         &quot;&quot;&quot;
1568 |         model = self.copy()
1569 | 
1570 |         inputs_unit = {inp: getattr(kwargs[inp], &#39;unit&#39;, dimensionless_unscaled)
1571 |                        for inp in self.inputs if kwargs[inp] is not None}
1572 | 
1573 |         outputs_unit = {out: getattr(kwargs[out], &#39;unit&#39;, dimensionless_unscaled)
1574 |                         for out in self.outputs if kwargs[out] is not None}
1575 |         parameter_units = self._parameter_units_for_data_units(inputs_unit,
1576 |                                                                outputs_unit)
1577 |         for name, unit in parameter_units.items():
1578 |             parameter = getattr(model, name)
1579 |             if parameter.unit is not None:
1580 |                 parameter.value = parameter.quantity.to(unit).value
1581 |                 parameter._set_unit(None, force=True)
1582 | 
1583 |         if isinstance(model, CompoundModel):
1584 |             model.strip_units_from_tree()
1585 | 
1586 |         return model
1587 | 
1588 |     def output_units(self, **kwargs):
1589 |         &quot;&quot;&quot;
1590 |         Return a dictionary of output units for this model given a dictionary
1591 |         of fitting inputs and outputs
1592 | 
1593 |         The input and output Quantity objects should be given as keyword
1594 |         arguments.
1595 | 
1596 |         Notes
1597 |         -----
1598 | 
1599 |         This method is needed in order to be able to fit models with units in
1600 |         the parameters, since we need to temporarily strip away the units from
1601 |         the model during the fitting (which might be done by e.g. scipy
1602 |         functions).
1603 | 
1604 |         This method will force extra model evaluations, which maybe computationally
1605 |         expensive. To avoid this, one can add a return_units property to the model,
1606 |         see :ref:`astropy:models_return_units`.
1607 |         &quot;&quot;&quot;
1608 |         units = self.return_units
1609 | 
1610 |         if units is None or units == {}:
1611 |             inputs = {inp: kwargs[inp] for inp in self.inputs}
1612 | 
1613 |             values = self(**inputs)
1614 |             if self.n_outputs == 1:
1615 |                 values = (values,)
1616 | 
1617 |             units = {out: getattr(values[index], &#39;unit&#39;, dimensionless_unscaled)
1618 |                      for index, out in enumerate(self.outputs)}
1619 | 
1620 |         return units
1621 | 
1622 |     def strip_units_from_tree(self):
1623 |         for item in self._leaflist:
1624 |             for parname in item.param_names:
1625 |                 par = getattr(item, parname)
1626 |                 par._set_unit(None, force=True)
1627 | 
1628 |     def with_units_from_data(self, **kwargs):
1629 |         &quot;&quot;&quot;
1630 |         Return an instance of the model which has units for which the parameter
1631 |         values are compatible with the data units specified.
1632 | 
1633 |         The input and output Quantity objects should be given as keyword
1634 |         arguments.
1635 | 
1636 |         Notes
1637 |         -----
1638 | 
1639 |         This method is needed in order to be able to fit models with units in
1640 |         the parameters, since we need to temporarily strip away the units from
1641 |         the model during the fitting (which might be done by e.g. scipy
1642 |         functions).
1643 | 
1644 |         The units that the parameters will gain are not necessarily the units
1645 |         of the input data, but are derived from them. Model subclasses that
1646 |         want fitting to work in the presence of quantities need to define a
1647 |         ``_parameter_units_for_data_units`` method that takes the input and output
1648 |         units (as two dictionaries) and returns a dictionary giving the target
1649 |         units for each parameter.
1650 |         &quot;&quot;&quot;
1651 |         model = self.copy()
1652 |         inputs_unit = {inp: getattr(kwargs[inp], &#39;unit&#39;, dimensionless_unscaled)
1653 |                        for inp in self.inputs if kwargs[inp] is not None}
1654 | 
1655 |         outputs_unit = {out: getattr(kwargs[out], &#39;unit&#39;, dimensionless_unscaled)
1656 |                         for out in self.outputs if kwargs[out] is not None}
1657 | 
1658 |         parameter_units = self._parameter_units_for_data_units(inputs_unit,
1659 |                                                                outputs_unit)
1660 | 
1661 |         # We are adding units to parameters that already have a value, but we
1662 |         # don&#39;t want to convert the parameter, just add the unit directly,
1663 |         # hence the call to ``_set_unit``.
1664 |         for name, unit in parameter_units.items():
1665 |             parameter = getattr(model, name)
1666 |             parameter._set_unit(unit, force=True)
1667 | 
1668 |         return model
1669 | 
1670 |     @property
1671 |     def _has_units(self):
1672 |         # Returns True if any of the parameters have units
1673 |         for param in self.param_names:
1674 |             if getattr(self, param).unit is not None:
1675 |                 return True
1676 |         else:
1677 |             return False
1678 | 
1679 |     @property
1680 |     def _supports_unit_fitting(self):
1681 |         # If the model has a ``_parameter_units_for_data_units`` method, this
1682 |         # indicates that we have enough information to strip the units away
1683 |         # and add them back after fitting, when fitting quantities
1684 |         return hasattr(self, &#39;_parameter_units_for_data_units&#39;)
1685 | 
1686 |     @abc.abstractmethod
1687 |     def evaluate(self, *args, **kwargs):
1688 |         &quot;&quot;&quot;Evaluate the model on some input variables.&quot;&quot;&quot;
1689 | 
1690 |     def sum_of_implicit_terms(self, *args, **kwargs):
1691 |         &quot;&quot;&quot;
1692 |         Evaluate the sum of any implicit model terms on some input variables.
1693 |         This includes any fixed terms used in evaluating a linear model that
1694 |         do not have corresponding parameters exposed to the user. The
1695 |         prototypical case is `astropy.modeling.functional_models.Shift`, which
1696 |         corresponds to a function y = a + bx, where b=1 is intrinsically fixed
1697 |         by the type of model, such that sum_of_implicit_terms(x) == x. This
1698 |         method is needed by linear fitters to correct the dependent variable
1699 |         for the implicit term(s) when solving for the remaining terms
1700 |         (ie. a = y - bx).
1701 |         &quot;&quot;&quot;
1702 | 
1703 |     def render(self, out=None, coords=None):
1704 |         &quot;&quot;&quot;
1705 |         Evaluate a model at fixed positions, respecting the ``bounding_box``.
1706 | 
1707 |         The key difference relative to evaluating the model directly is that
1708 |         this method is limited to a bounding box if the `Model.bounding_box`
1709 |         attribute is set.
1710 | 
1711 |         Parameters
1712 |         ----------
1713 |         out : `numpy.ndarray`, optional
1714 |             An array that the evaluated model will be added to.  If this is not
1715 |             given (or given as ``None``), a new array will be created.
1716 |         coords : array-like, optional
1717 |             An array to be used to translate from the model&#39;s input coordinates
1718 |             to the ``out`` array. It should have the property that
1719 |             ``self(coords)`` yields the same shape as ``out``.  If ``out`` is
1720 |             not specified, ``coords`` will be used to determine the shape of
1721 |             the returned array. If this is not provided (or None), the model
1722 |             will be evaluated on a grid determined by `Model.bounding_box`.
1723 | 
1724 |         Returns
1725 |         -------
1726 |         out : `numpy.ndarray`
1727 |             The model added to ``out`` if  ``out`` is not ``None``, or else a
1728 |             new array from evaluating the model over ``coords``.
1729 |             If ``out`` and ``coords`` are both `None`, the returned array is
1730 |             limited to the `Model.bounding_box` limits. If
1731 |             `Model.bounding_box` is `None`, ``arr`` or ``coords`` must be
1732 |             passed.
1733 | 
1734 |         Raises
1735 |         ------
1736 |         ValueError
1737 |             If ``coords`` are not given and the the `Model.bounding_box` of
1738 |             this model is not set.
1739 | 
1740 |         Examples
1741 |         --------
1742 |         :ref:`astropy:bounding-boxes`
1743 |         &quot;&quot;&quot;
1744 | 
1745 |         try:
1746 |             bbox = self.bounding_box
1747 |         except NotImplementedError:
1748 |             bbox = None
1749 | 
1750 |         if isinstance(bbox, ModelBoundingBox):
1751 |             bbox = bbox.bounding_box()
1752 | 
1753 |         ndim = self.n_inputs
1754 | 
1755 |         if (coords is None) and (out is None) and (bbox is None):
1756 |             raise ValueError(&#39;If no bounding_box is set, &#39;
1757 |                              &#39;coords or out must be input.&#39;)
1758 | 
1759 |         # for consistent indexing
1760 |         if ndim == 1:
1761 |             if coords is not None:
1762 |                 coords = [coords]
1763 |             if bbox is not None:
1764 |                 bbox = [bbox]
1765 | 
1766 |         if coords is not None:
1767 |             coords = np.asanyarray(coords, dtype=float)
1768 |             # Check dimensions match out and model
1769 |             assert len(coords) == ndim
1770 |             if out is not None:
1771 |                 if coords[0].shape != out.shape:
1772 |                     raise ValueError(&#39;inconsistent shape of the output.&#39;)
1773 |             else:
1774 |                 out = np.zeros(coords[0].shape)
1775 | 
1776 |         if out is not None:
1777 |             out = np.asanyarray(out)
1778 |             if out.ndim != ndim:
1779 |                 raise ValueError(&#39;the array and model must have the same &#39;
1780 |                                  &#39;number of dimensions.&#39;)
1781 | 
1782 |         if bbox is not None:
1783 |             # Assures position is at center pixel,
1784 |             # important when using add_array.
1785 |             pd = np.array([(np.mean(bb), np.ceil((bb[1] - bb[0]) / 2))
1786 |                            for bb in bbox]).astype(int).T
1787 |             pos, delta = pd
1788 | 
1789 |             if coords is not None:
1790 |                 sub_shape = tuple(delta * 2 + 1)
1791 |                 sub_coords = np.array([extract_array(c, sub_shape, pos)
1792 |                                        for c in coords])
1793 |             else:
1794 |                 limits = [slice(p - d, p + d + 1, 1) for p, d in pd.T]
1795 |                 sub_coords = np.mgrid[limits]
1796 | 
1797 |             sub_coords = sub_coords[::-1]
1798 | 
1799 |             if out is None:
1800 |                 out = self(*sub_coords)
1801 |             else:
1802 |                 try:
1803 |                     out = add_array(out, self(*sub_coords), pos)
1804 |                 except ValueError:
1805 |                     raise ValueError(
1806 |                         &#39;The `bounding_box` is larger than the input out in &#39;
1807 |                         &#39;one or more dimensions. Set &#39;
1808 |                         &#39;`model.bounding_box = None`.&#39;)
1809 |         else:
1810 |             if coords is None:
1811 |                 im_shape = out.shape
1812 |                 limits = [slice(i) for i in im_shape]
1813 |                 coords = np.mgrid[limits]
1814 | 
1815 |             coords = coords[::-1]
1816 | 
1817 |             out += self(*coords)
1818 | 
1819 |         return out
1820 | 
1821 |     @property
1822 |     def input_units(self):
1823 |         &quot;&quot;&quot;
1824 |         This property is used to indicate what units or sets of units the
1825 |         evaluate method expects, and returns a dictionary mapping inputs to
1826 |         units (or `None` if any units are accepted).
1827 | 
1828 |         Model sub-classes can also use function annotations in evaluate to
1829 |         indicate valid input units, in which case this property should
1830 |         not be overridden since it will return the input units based on the
1831 |         annotations.
1832 |         &quot;&quot;&quot;
1833 |         if hasattr(self, &#39;_input_units&#39;):
1834 |             return self._input_units
1835 |         elif hasattr(self.evaluate, &#39;__annotations__&#39;):
1836 |             annotations = self.evaluate.__annotations__.copy()
1837 |             annotations.pop(&#39;return&#39;, None)
1838 |             if annotations:
1839 |                 # If there are not annotations for all inputs this will error.
1840 |                 return dict((name, annotations[name]) for name in self.inputs)
1841 |         else:
1842 |             # None means any unit is accepted
1843 |             return None
1844 | 
1845 |     @property
1846 |     def return_units(self):
1847 |         &quot;&quot;&quot;
1848 |         This property is used to indicate what units or sets of units the
1849 |         output of evaluate should be in, and returns a dictionary mapping
1850 |         outputs to units (or `None` if any units are accepted).
1851 | 
1852 |         Model sub-classes can also use function annotations in evaluate to
1853 |         indicate valid output units, in which case this property should not be
1854 |         overridden since it will return the return units based on the
1855 |         annotations.
1856 |         &quot;&quot;&quot;
1857 |         if hasattr(self, &#39;_return_units&#39;):
1858 |             return self._return_units
1859 |         elif hasattr(self.evaluate, &#39;__annotations__&#39;):
1860 |             return self.evaluate.__annotations__.get(&#39;return&#39;, None)
1861 |         else:
1862 |             # None means any unit is accepted
1863 |             return None
1864 | 
1865 |     def _prepare_inputs_single_model(self, params, inputs, **kwargs):
1866 |         broadcasts = []
1867 |         for idx, _input in enumerate(inputs):
1868 |             input_shape = _input.shape
1869 | 
1870 |             # Ensure that array scalars are always upgrade to 1-D arrays for the
1871 |             # sake of consistency with how parameters work.  They will be cast back
1872 |             # to scalars at the end
1873 |             if not input_shape:
1874 |                 inputs[idx] = _input.reshape((1,))
1875 | 
1876 |             if not params:
1877 |                 max_broadcast = input_shape
1878 |             else:
1879 |                 max_broadcast = ()
1880 | 
1881 |             for param in params:
1882 |                 try:
1883 |                     if self.standard_broadcasting:
1884 |                         broadcast = check_broadcast(input_shape, param.shape)
1885 |                     else:
1886 |                         broadcast = input_shape
1887 |                 except IncompatibleShapeError:
1888 |                     raise ValueError(
1889 |                         &quot;self input argument {0!r} of shape {1!r} cannot be &quot;
1890 |                         &quot;broadcast with parameter {2!r} of shape &quot;
1891 |                         &quot;{3!r}.&quot;.format(self.inputs[idx], input_shape,
1892 |                                         param.name, param.shape))
1893 | 
1894 |                 if len(broadcast) &gt; len(max_broadcast):
1895 |                     max_broadcast = broadcast
1896 |                 elif len(broadcast) == len(max_broadcast):
1897 |                     max_broadcast = max(max_broadcast, broadcast)
1898 | 
1899 |             broadcasts.append(max_broadcast)
1900 | 
1901 |         if self.n_outputs &gt; self.n_inputs:
1902 |             extra_outputs = self.n_outputs - self.n_inputs
1903 |             if not broadcasts:
1904 |                 # If there were no inputs then the broadcasts list is empty
1905 |                 # just add a None since there is no broadcasting of outputs and
1906 |                 # inputs necessary (see _prepare_outputs_single_self)
1907 |                 broadcasts.append(None)
1908 |             broadcasts.extend([broadcasts[0]] * extra_outputs)
1909 | 
1910 |         return inputs, (broadcasts,)
1911 | 
1912 |     @staticmethod
1913 |     def _remove_axes_from_shape(shape, axis):
1914 |         &quot;&quot;&quot;
1915 |         Given a shape tuple as the first input, construct a new one by  removing
1916 |         that particular axis from the shape and all preceeding axes. Negative axis
1917 |         numbers are permittted, where the axis is relative to the last axis.
1918 |         &quot;&quot;&quot;
1919 |         if len(shape) == 0:
1920 |             return shape
1921 |         if axis &lt; 0:
1922 |             axis = len(shape) + axis
1923 |             return shape[:axis] + shape[axis+1:]
1924 |         if axis &gt;= len(shape):
1925 |             axis = len(shape)-1
1926 |         shape = shape[axis+1:]
1927 |         return shape
1928 | 
1929 |     def _prepare_inputs_model_set(self, params, inputs, model_set_axis_input,
1930 |                                   **kwargs):
1931 |         reshaped = []
1932 |         pivots = []
1933 | 
1934 |         model_set_axis_param = self.model_set_axis  # needed to reshape param
1935 |         for idx, _input in enumerate(inputs):
1936 |             max_param_shape = ()
1937 |             if self._n_models &gt; 1 and model_set_axis_input is not False:
1938 |                 # Use the shape of the input *excluding* the model axis
1939 |                 input_shape = (_input.shape[:model_set_axis_input] +
1940 |                                _input.shape[model_set_axis_input + 1:])
1941 |             else:
1942 |                 input_shape = _input.shape
1943 | 
1944 |             for param in params:
1945 |                 try:
1946 |                     check_broadcast(input_shape,
1947 |                                     self._remove_axes_from_shape(param.shape,
1948 |                                                                  model_set_axis_param))
1949 |                 except IncompatibleShapeError:
1950 |                     raise ValueError(
1951 |                         &quot;Model input argument {0!r} of shape {1!r} cannot be &quot;
1952 |                         &quot;broadcast with parameter {2!r} of shape &quot;
1953 |                         &quot;{3!r}.&quot;.format(self.inputs[idx], input_shape,
1954 |                                         param.name,
1955 |                                         self._remove_axes_from_shape(param.shape,
1956 |                                                                      model_set_axis_param)))
1957 | 
1958 |                 if len(param.shape) - 1 &gt; len(max_param_shape):
1959 |                     max_param_shape = self._remove_axes_from_shape(param.shape,
1960 |                                                                    model_set_axis_param)
1961 | 
1962 |             # We&#39;ve now determined that, excluding the model_set_axis, the
1963 |             # input can broadcast with all the parameters
1964 |             input_ndim = len(input_shape)
1965 |             if model_set_axis_input is False:
1966 |                 if len(max_param_shape) &gt; input_ndim:
1967 |                     # Just needs to prepend new axes to the input
1968 |                     n_new_axes = 1 + len(max_param_shape) - input_ndim
1969 |                     new_axes = (1,) * n_new_axes
1970 |                     new_shape = new_axes + _input.shape
1971 |                     pivot = model_set_axis_param
1972 |                 else:
1973 |                     pivot = input_ndim - len(max_param_shape)
1974 |                     new_shape = (_input.shape[:pivot] + (1,) +
1975 |                                  _input.shape[pivot:])
1976 |                 new_input = _input.reshape(new_shape)
1977 |             else:
1978 |                 if len(max_param_shape) &gt;= input_ndim:
1979 |                     n_new_axes = len(max_param_shape) - input_ndim
1980 |                     pivot = self.model_set_axis
1981 |                     new_axes = (1,) * n_new_axes
1982 |                     new_shape = (_input.shape[:pivot + 1] + new_axes +
1983 |                                  _input.shape[pivot + 1:])
1984 |                     new_input = _input.reshape(new_shape)
1985 |                 else:
1986 |                     pivot = _input.ndim - len(max_param_shape) - 1
1987 |                     new_input = np.rollaxis(_input, model_set_axis_input,
1988 |                                             pivot + 1)
1989 |             pivots.append(pivot)
1990 |             reshaped.append(new_input)
1991 | 
1992 |         if self.n_inputs &lt; self.n_outputs:
1993 |             pivots.extend([model_set_axis_input] * (self.n_outputs - self.n_inputs))
1994 | 
1995 |         return reshaped, (pivots,)
1996 | 
1997 |     def prepare_inputs(self, *inputs, model_set_axis=None, equivalencies=None,
1998 |                        **kwargs):
1999 |         &quot;&quot;&quot;
2000 |         This method is used in `~astropy.modeling.Model.__call__` to ensure
2001 |         that all the inputs to the model can be broadcast into compatible
2002 |         shapes (if one or both of them are input as arrays), particularly if
2003 |         there are more than one parameter sets. This also makes sure that (if
2004 |         applicable) the units of the input will be compatible with the evaluate
2005 |         method.
2006 |         &quot;&quot;&quot;
2007 |         # When we instantiate the model class, we make sure that __call__ can
2008 |         # take the following two keyword arguments: model_set_axis and
2009 |         # equivalencies.
2010 |         if model_set_axis is None:
2011 |             # By default the model_set_axis for the input is assumed to be the
2012 |             # same as that for the parameters the model was defined with
2013 |             # TODO: Ensure that negative model_set_axis arguments are respected
2014 |             model_set_axis = self.model_set_axis
2015 | 
2016 |         params = [getattr(self, name) for name in self.param_names]
2017 |         inputs = [np.asanyarray(_input, dtype=float) for _input in inputs]
2018 | 
2019 |         self._validate_input_shapes(inputs, self.inputs, model_set_axis)
2020 | 
2021 |         inputs_map = kwargs.get(&#39;inputs_map&#39;, None)
2022 | 
2023 |         inputs = self._validate_input_units(inputs, equivalencies, inputs_map)
2024 | 
2025 |         # The input formatting required for single models versus a multiple
2026 |         # model set are different enough that they&#39;ve been split into separate
2027 |         # subroutines
2028 |         if self._n_models == 1:
2029 |             return self._prepare_inputs_single_model(params, inputs, **kwargs)
2030 |         else:
2031 |             return self._prepare_inputs_model_set(params, inputs,
2032 |                                                   model_set_axis, **kwargs)
2033 | 
2034 |     def _validate_input_units(self, inputs, equivalencies=None, inputs_map=None):
2035 |         inputs = list(inputs)
2036 |         name = self.name or self.__class__.__name__
2037 |         # Check that the units are correct, if applicable
2038 | 
2039 |         if self.input_units is not None:
2040 |             # If a leaflist is provided that means this is in the context of
2041 |             # a compound model and it is necessary to create the appropriate
2042 |             # alias for the input coordinate name for the equivalencies dict
2043 |             if inputs_map:
2044 |                 edict = {}
2045 |                 for mod, mapping in inputs_map:
2046 |                     if self is mod:
2047 |                         edict[mapping[0]] = equivalencies[mapping[1]]
2048 |             else:
2049 |                 edict = equivalencies
2050 |             # We combine any instance-level input equivalencies with user
2051 |             # specified ones at call-time.
2052 |             input_units_equivalencies = _combine_equivalency_dict(self.inputs,
2053 |                                                                   edict,
2054 |                                                                   self.input_units_equivalencies)
2055 | 
2056 |             # We now iterate over the different inputs and make sure that their
2057 |             # units are consistent with those specified in input_units.
2058 |             for i in range(len(inputs)):
2059 | 
2060 |                 input_name = self.inputs[i]
2061 |                 input_unit = self.input_units.get(input_name, None)
2062 | 
2063 |                 if input_unit is None:
2064 |                     continue
2065 | 
2066 |                 if isinstance(inputs[i], Quantity):
2067 | 
2068 |                     # We check for consistency of the units with input_units,
2069 |                     # taking into account any equivalencies
2070 | 
2071 |                     if inputs[i].unit.is_equivalent(
2072 |                             input_unit,
2073 |                             equivalencies=input_units_equivalencies[input_name]):
2074 | 
2075 |                         # If equivalencies have been specified, we need to
2076 |                         # convert the input to the input units - this is
2077 |                         # because some equivalencies are non-linear, and
2078 |                         # we need to be sure that we evaluate the model in
2079 |                         # its own frame of reference. If input_units_strict
2080 |                         # is set, we also need to convert to the input units.
2081 |                         if len(input_units_equivalencies) &gt; 0 or self.input_units_strict[input_name]:
2082 |                             inputs[i] = inputs[i].to(input_unit,
2083 |                                                      equivalencies=input_units_equivalencies[input_name])
2084 | 
2085 |                     else:
2086 | 
2087 |                         # We consider the following two cases separately so as
2088 |                         # to be able to raise more appropriate/nicer exceptions
2089 | 
2090 |                         if input_unit is dimensionless_unscaled:
2091 |                             raise UnitsError(&quot;{0}: Units of input &#39;{1}&#39;, {2} ({3}),&quot;
2092 |                                              &quot;could not be converted to &quot;
2093 |                                              &quot;required dimensionless &quot;
2094 |                                              &quot;input&quot;.format(name,
2095 |                                                             self.inputs[i],
2096 |                                                             inputs[i].unit,
2097 |                                                             inputs[i].unit.physical_type))
2098 |                         else:
2099 |                             raise UnitsError(&quot;{0}: Units of input &#39;{1}&#39;, {2} ({3}),&quot;
2100 |                                              &quot; could not be &quot;
2101 |                                              &quot;converted to required input&quot;
2102 |                                              &quot; units of {4} ({5})&quot;.format(
2103 |                                                  name,
2104 |                                                  self.inputs[i],
2105 |                                                  inputs[i].unit,
2106 |                                                  inputs[i].unit.physical_type,
2107 |                                                  input_unit,
2108 |                                                  input_unit.physical_type))
2109 |                 else:
2110 | 
2111 |                     # If we allow dimensionless input, we add the units to the
2112 |                     # input values without conversion, otherwise we raise an
2113 |                     # exception.
2114 | 
2115 |                     if (not self.input_units_allow_dimensionless[input_name] and
2116 |                         input_unit is not dimensionless_unscaled and
2117 |                         input_unit is not None):
2118 |                         if np.any(inputs[i] != 0):
2119 |                             raise UnitsError(&quot;{0}: Units of input &#39;{1}&#39;, (dimensionless), could not be &quot;
2120 |                                              &quot;converted to required input units of &quot;
2121 |                                              &quot;{2} ({3})&quot;.format(name, self.inputs[i], input_unit,
2122 |                                                                 input_unit.physical_type))
2123 |         return inputs
2124 | 
2125 |     def _process_output_units(self, inputs, outputs):
2126 |         inputs_are_quantity = any([isinstance(i, Quantity) for i in inputs])
2127 |         if self.return_units and inputs_are_quantity:
2128 |             # We allow a non-iterable unit only if there is one output
2129 |             if self.n_outputs == 1 and not isiterable(self.return_units):
2130 |                 return_units = {self.outputs[0]: self.return_units}
2131 |             else:
2132 |                 return_units = self.return_units
2133 | 
2134 |             outputs = tuple([Quantity(out, return_units.get(out_name, None), subok=True)
2135 |                              for out, out_name in zip(outputs, self.outputs)])
2136 |         return outputs
2137 | 
2138 |     @staticmethod
2139 |     def _prepare_output_single_model(output, broadcast_shape):
2140 |         if broadcast_shape is not None:
2141 |             if not broadcast_shape:
2142 |                 return output.item()
2143 |             else:
2144 |                 try:
2145 |                     return output.reshape(broadcast_shape)
2146 |                 except ValueError:
2147 |                     try:
2148 |                         return output.item()
2149 |                     except ValueError:
2150 |                         return output
2151 | 
2152 |         return output
2153 | 
2154 |     def _prepare_outputs_single_model(self, outputs, broadcasted_shapes):
2155 |         outputs = list(outputs)
2156 |         for idx, output in enumerate(outputs):
2157 |             try:
2158 |                 broadcast_shape = check_broadcast(*broadcasted_shapes[0])
2159 |             except (IndexError, TypeError):
2160 |                 broadcast_shape = broadcasted_shapes[0][idx]
2161 | 
2162 |             outputs[idx] = self._prepare_output_single_model(output, broadcast_shape)
2163 | 
2164 |         return tuple(outputs)
2165 | 
2166 |     def _prepare_outputs_model_set(self, outputs, broadcasted_shapes, model_set_axis):
2167 |         pivots = broadcasted_shapes[0]
2168 |         # If model_set_axis = False was passed then use
2169 |         # self._model_set_axis to format the output.
2170 |         if model_set_axis is None or model_set_axis is False:
2171 |             model_set_axis = self.model_set_axis
2172 |         outputs = list(outputs)
2173 |         for idx, output in enumerate(outputs):
2174 |             pivot = pivots[idx]
2175 |             if pivot &lt; output.ndim and pivot != model_set_axis:
2176 |                 outputs[idx] = np.rollaxis(output, pivot,
2177 |                                            model_set_axis)
2178 |         return tuple(outputs)
2179 | 
2180 |     def prepare_outputs(self, broadcasted_shapes, *outputs, **kwargs):
2181 |         model_set_axis = kwargs.get(&#39;model_set_axis&#39;, None)
2182 | 
2183 |         if len(self) == 1:
2184 |             return self._prepare_outputs_single_model(outputs, broadcasted_shapes)
2185 |         else:
2186 |             return self._prepare_outputs_model_set(outputs, broadcasted_shapes, model_set_axis)
2187 | 
2188 |     def copy(self):
2189 |         &quot;&quot;&quot;
2190 |         Return a copy of this model.
2191 | 
2192 |         Uses a deep copy so that all model attributes, including parameter
2193 |         values, are copied as well.
2194 |         &quot;&quot;&quot;
2195 | 
2196 |         return copy.deepcopy(self)
2197 | 
2198 |     def deepcopy(self):
2199 |         &quot;&quot;&quot;
2200 |         Return a deep copy of this model.
2201 | 
2202 |         &quot;&quot;&quot;
2203 | 
2204 |         return self.copy()
2205 | 
2206 |     @sharedmethod
2207 |     def rename(self, name):
2208 |         &quot;&quot;&quot;
2209 |         Return a copy of this model with a new name.
2210 |         &quot;&quot;&quot;
2211 |         new_model = self.copy()
2212 |         new_model._name = name
2213 |         return new_model
2214 | 
2215 |     def coerce_units(
2216 |         self,
2217 |         input_units=None,
2218 |         return_units=None,
2219 |         input_units_equivalencies=None,
2220 |         input_units_allow_dimensionless=False
2221 |     ):
2222 |         &quot;&quot;&quot;
2223 |         Attach units to this (unitless) model.
2224 | 
2225 |         Parameters
2226 |         ----------
2227 |         input_units : dict or tuple, optional
2228 |             Input units to attach.  If dict, each key is the name of a model input,
2229 |             and the value is the unit to attach.  If tuple, the elements are units
2230 |             to attach in order corresponding to `Model.inputs`.
2231 |         return_units : dict or tuple, optional
2232 |             Output units to attach.  If dict, each key is the name of a model output,
2233 |             and the value is the unit to attach.  If tuple, the elements are units
2234 |             to attach in order corresponding to `Model.outputs`.
2235 |         input_units_equivalencies : dict, optional
2236 |             Default equivalencies to apply to input values.  If set, this should be a
2237 |             dictionary where each key is a string that corresponds to one of the
2238 |             model inputs.
2239 |         input_units_allow_dimensionless : bool or dict, optional
2240 |             Allow dimensionless input. If this is True, input values to evaluate will
2241 |             gain the units specified in input_units. If this is a dictionary then it
2242 |             should map input name to a bool to allow dimensionless numbers for that
2243 |             input.
2244 | 
2245 |         Returns
2246 |         -------
2247 |         `CompoundModel`
2248 |             A `CompoundModel` composed of the current model plus
2249 |             `~astropy.modeling.mappings.UnitsMapping` model(s) that attach the units.
2250 | 
2251 |         Raises
2252 |         ------
2253 |         ValueError
2254 |             If the current model already has units.
2255 | 
2256 |         Examples
2257 |         --------
2258 | 
2259 |         Wrapping a unitless model to require and convert units:
2260 | 
2261 |         &gt;&gt;&gt; from astropy.modeling.models import Polynomial1D
2262 |         &gt;&gt;&gt; from astropy import units as u
2263 |         &gt;&gt;&gt; poly = Polynomial1D(1, c0=1, c1=2)
2264 |         &gt;&gt;&gt; model = poly.coerce_units((u.m,), (u.s,))
2265 |         &gt;&gt;&gt; model(u.Quantity(10, u.m))  # doctest: +FLOAT_CMP
2266 |         &lt;Quantity 21. s&gt;
2267 |         &gt;&gt;&gt; model(u.Quantity(1000, u.cm))  # doctest: +FLOAT_CMP
2268 |         &lt;Quantity 21. s&gt;
2269 |         &gt;&gt;&gt; model(u.Quantity(10, u.cm))  # doctest: +FLOAT_CMP
2270 |         &lt;Quantity 1.2 s&gt;
2271 | 
2272 |         Wrapping a unitless model but still permitting unitless input:
2273 | 
2274 |         &gt;&gt;&gt; from astropy.modeling.models import Polynomial1D
2275 |         &gt;&gt;&gt; from astropy import units as u
2276 |         &gt;&gt;&gt; poly = Polynomial1D(1, c0=1, c1=2)
2277 |         &gt;&gt;&gt; model = poly.coerce_units((u.m,), (u.s,), input_units_allow_dimensionless=True)
2278 |         &gt;&gt;&gt; model(u.Quantity(10, u.m))  # doctest: +FLOAT_CMP
2279 |         &lt;Quantity 21. s&gt;
2280 |         &gt;&gt;&gt; model(10)  # doctest: +FLOAT_CMP
2281 |         &lt;Quantity 21. s&gt;
2282 |         &quot;&quot;&quot;
2283 |         from .mappings import UnitsMapping
2284 | 
2285 |         result = self
2286 | 
2287 |         if input_units is not None:
2288 |             if self.input_units is not None:
2289 |                 model_units = self.input_units
2290 |             else:
2291 |                 model_units = {}
2292 | 
2293 |             for unit in [model_units.get(i) for i in self.inputs]:
2294 |                 if unit is not None and unit != dimensionless_unscaled:
2295 |                     raise ValueError(&quot;Cannot specify input_units for model with existing input units&quot;)
2296 | 
2297 |             if isinstance(input_units, dict):
2298 |                 if input_units.keys() != set(self.inputs):
2299 |                     message = (
2300 |                         f&quot;&quot;&quot;input_units keys ({&quot;, &quot;.join(input_units.keys())}) &quot;&quot;&quot;
2301 |                         f&quot;&quot;&quot;do not match model inputs ({&quot;, &quot;.join(self.inputs)})&quot;&quot;&quot;
2302 |                     )
2303 |                     raise ValueError(message)
2304 |                 input_units = [input_units[i] for i in self.inputs]
2305 | 
2306 |             if len(input_units) != self.n_inputs:
2307 |                 message = (
2308 |                     &quot;input_units length does not match n_inputs: &quot;
2309 |                     f&quot;expected {self.n_inputs}, received {len(input_units)}&quot;
2310 |                 )
2311 |                 raise ValueError(message)
2312 | 
2313 |             mapping = tuple((unit, model_units.get(i)) for i, unit in zip(self.inputs, input_units))
2314 |             input_mapping = UnitsMapping(
2315 |                 mapping,
2316 |                 input_units_equivalencies=input_units_equivalencies,
2317 |                 input_units_allow_dimensionless=input_units_allow_dimensionless
2318 |             )
2319 |             input_mapping.inputs = self.inputs
2320 |             input_mapping.outputs = self.inputs
2321 |             result = input_mapping | result
2322 | 
2323 |         if return_units is not None:
2324 |             if self.return_units is not None:
2325 |                 model_units = self.return_units
2326 |             else:
2327 |                 model_units = {}
2328 | 
2329 |             for unit in [model_units.get(i) for i in self.outputs]:
2330 |                 if unit is not None and unit != dimensionless_unscaled:
2331 |                     raise ValueError(&quot;Cannot specify return_units for model with existing output units&quot;)
2332 | 
2333 |             if isinstance(return_units, dict):
2334 |                 if return_units.keys() != set(self.outputs):
2335 |                     message = (
2336 |                         f&quot;&quot;&quot;return_units keys ({&quot;, &quot;.join(return_units.keys())}) &quot;&quot;&quot;
2337 |                         f&quot;&quot;&quot;do not match model outputs ({&quot;, &quot;.join(self.outputs)})&quot;&quot;&quot;
2338 |                     )
2339 |                     raise ValueError(message)
2340 |                 return_units = [return_units[i] for i in self.outputs]
2341 | 
2342 |             if len(return_units) != self.n_outputs:
2343 |                 message = (
2344 |                     &quot;return_units length does not match n_outputs: &quot;
2345 |                     f&quot;expected {self.n_outputs}, received {len(return_units)}&quot;
2346 |                 )
2347 |                 raise ValueError(message)
2348 | 
2349 |             mapping = tuple((model_units.get(i), unit) for i, unit in zip(self.outputs, return_units))
2350 |             return_mapping = UnitsMapping(mapping)
2351 |             return_mapping.inputs = self.outputs
2352 |             return_mapping.outputs = self.outputs
2353 |             result = result | return_mapping
2354 | 
2355 |         return result
2356 | 
2357 |     @property
2358 |     def n_submodels(self):
2359 |         &quot;&quot;&quot;
2360 |         Return the number of components in a single model, which is
2361 |         obviously 1.
2362 |         &quot;&quot;&quot;
2363 |         return 1
2364 | 
2365 |     def _initialize_constraints(self, kwargs):
2366 |         &quot;&quot;&quot;
2367 |         Pop parameter constraint values off the keyword arguments passed to
2368 |         `Model.__init__` and store them in private instance attributes.
2369 |         &quot;&quot;&quot;
2370 | 
2371 |         # Pop any constraints off the keyword arguments
2372 |         for constraint in self.parameter_constraints:
2373 |             values = kwargs.pop(constraint, {})
2374 |             for ckey, cvalue in values.items():
2375 |                 param = getattr(self, ckey)
2376 |                 setattr(param, constraint, cvalue)
2377 |         self._mconstraints = {}
2378 |         for constraint in self.model_constraints:
2379 |             values = kwargs.pop(constraint, [])
2380 |             self._mconstraints[constraint] = values
2381 | 
2382 |     def _initialize_parameters(self, args, kwargs):
2383 |         &quot;&quot;&quot;
2384 |         Initialize the _parameters array that stores raw parameter values for
2385 |         all parameter sets for use with vectorized fitting algorithms; on
2386 |         FittableModels the _param_name attributes actually just reference
2387 |         slices of this array.
2388 |         &quot;&quot;&quot;
2389 |         n_models = kwargs.pop(&#39;n_models&#39;, None)
2390 | 
2391 |         if not (n_models is None or
2392 |                 (isinstance(n_models, (int, np.integer)) and n_models &gt;= 1)):
2393 |             raise ValueError(
2394 |                 &quot;n_models must be either None (in which case it is &quot;
2395 |                 &quot;determined from the model_set_axis of the parameter initial &quot;
2396 |                 &quot;values) or it must be a positive integer &quot;
2397 |                 &quot;(got {0!r})&quot;.format(n_models))
2398 | 
2399 |         model_set_axis = kwargs.pop(&#39;model_set_axis&#39;, None)
2400 |         if model_set_axis is None:
2401 |             if n_models is not None and n_models &gt; 1:
2402 |                 # Default to zero
2403 |                 model_set_axis = 0
2404 |             else:
2405 |                 # Otherwise disable
2406 |                 model_set_axis = False
2407 |         else:
2408 |             if not (model_set_axis is False or
2409 |                     np.issubdtype(type(model_set_axis), np.integer)):
2410 |                 raise ValueError(
2411 |                     &quot;model_set_axis must be either False or an integer &quot;
2412 |                     &quot;specifying the parameter array axis to map to each &quot;
2413 |                     &quot;model in a set of models (got {0!r}).&quot;.format(
2414 |                         model_set_axis))
2415 | 
2416 |         # Process positional arguments by matching them up with the
2417 |         # corresponding parameters in self.param_names--if any also appear as
2418 |         # keyword arguments this presents a conflict
2419 |         params = set()
2420 |         if len(args) &gt; len(self.param_names):
2421 |             raise TypeError(
2422 |                 &quot;{0}.__init__() takes at most {1} positional arguments ({2} &quot;
2423 |                 &quot;given)&quot;.format(self.__class__.__name__, len(self.param_names),
2424 |                                 len(args)))
2425 | 
2426 |         self._model_set_axis = model_set_axis
2427 |         self._param_metrics = defaultdict(dict)
2428 | 
2429 |         for idx, arg in enumerate(args):
2430 |             if arg is None:
2431 |                 # A value of None implies using the default value, if exists
2432 |                 continue
2433 |             # We use quantity_asanyarray here instead of np.asanyarray because
2434 |             # if any of the arguments are quantities, we need to return a
2435 |             # Quantity object not a plain Numpy array.
2436 |             param_name = self.param_names[idx]
2437 |             params.add(param_name)
2438 |             if not isinstance(arg, Parameter):
2439 |                 value = quantity_asanyarray(arg, dtype=float)
2440 |             else:
2441 |                 value = arg
2442 |             self._initialize_parameter_value(param_name, value)
2443 | 
2444 |         # At this point the only remaining keyword arguments should be
2445 |         # parameter names; any others are in error.
2446 |         for param_name in self.param_names:
2447 |             if param_name in kwargs:
2448 |                 if param_name in params:
2449 |                     raise TypeError(
2450 |                         &quot;{0}.__init__() got multiple values for parameter &quot;
2451 |                         &quot;{1!r}&quot;.format(self.__class__.__name__, param_name))
2452 |                 value = kwargs.pop(param_name)
2453 |                 if value is None:
2454 |                     continue
2455 |                 # We use quantity_asanyarray here instead of np.asanyarray
2456 |                 # because if any of the arguments are quantities, we need
2457 |                 # to return a Quantity object not a plain Numpy array.
2458 |                 value = quantity_asanyarray(value, dtype=float)
2459 |                 params.add(param_name)
2460 |                 self._initialize_parameter_value(param_name, value)
2461 |         # Now deal with case where param_name is not supplied by args or kwargs
2462 |         for param_name in self.param_names:
2463 |             if param_name not in params:
2464 |                 self._initialize_parameter_value(param_name, None)
2465 | 
2466 |         if kwargs:
2467 |             # If any keyword arguments were left over at this point they are
2468 |             # invalid--the base class should only be passed the parameter
2469 |             # values, constraints, and param_dim
2470 |             for kwarg in kwargs:
2471 |                 # Just raise an error on the first unrecognized argument
2472 |                 raise TypeError(
2473 |                     &#39;{0}.__init__() got an unrecognized parameter &#39;
2474 |                     &#39;{1!r}&#39;.format(self.__class__.__name__, kwarg))
2475 | 
2476 |         # Determine the number of model sets: If the model_set_axis is
2477 |         # None then there is just one parameter set; otherwise it is determined
2478 |         # by the size of that axis on the first parameter--if the other
2479 |         # parameters don&#39;t have the right number of axes or the sizes of their
2480 |         # model_set_axis don&#39;t match an error is raised
2481 |         if model_set_axis is not False and n_models != 1 and params:
2482 |             max_ndim = 0
2483 |             if model_set_axis &lt; 0:
2484 |                 min_ndim = abs(model_set_axis)
2485 |             else:
2486 |                 min_ndim = model_set_axis + 1
2487 | 
2488 |             for name in self.param_names:
2489 |                 value = getattr(self, name)
2490 |                 param_ndim = np.ndim(value)
2491 |                 if param_ndim &lt; min_ndim:
2492 |                     raise InputParameterError(
2493 |                         &quot;All parameter values must be arrays of dimension &quot;
2494 |                         &quot;at least {0} for model_set_axis={1} (the value &quot;
2495 |                         &quot;given for {2!r} is only {3}-dimensional)&quot;.format(
2496 |                             min_ndim, model_set_axis, name, param_ndim))
2497 | 
2498 |                 max_ndim = max(max_ndim, param_ndim)
2499 | 
2500 |                 if n_models is None:
2501 |                     # Use the dimensions of the first parameter to determine
2502 |                     # the number of model sets
2503 |                     n_models = value.shape[model_set_axis]
2504 |                 elif value.shape[model_set_axis] != n_models:
2505 |                     raise InputParameterError(
2506 |                         &quot;Inconsistent dimensions for parameter {0!r} for &quot;
2507 |                         &quot;{1} model sets.  The length of axis {2} must be the &quot;
2508 |                         &quot;same for all input parameter values&quot;.format(
2509 |                             name, n_models, model_set_axis))
2510 | 
2511 |             self._check_param_broadcast(max_ndim)
2512 |         else:
2513 |             if n_models is None:
2514 |                 n_models = 1
2515 | 
2516 |             self._check_param_broadcast(None)
2517 | 
2518 |         self._n_models = n_models
2519 |         # now validate parameters
2520 |         for name in params:
2521 |             param = getattr(self, name)
2522 |             if param._validator is not None:
2523 |                 param._validator(self, param.value)
2524 | 
2525 |     def _initialize_parameter_value(self, param_name, value):
2526 |         &quot;&quot;&quot;Mostly deals with consistency checks and determining unit issues.&quot;&quot;&quot;
2527 |         if isinstance(value, Parameter):
2528 |             self.__dict__[param_name] = value
2529 |             return
2530 |         param = getattr(self, param_name)
2531 |         # Use default if value is not provided
2532 |         if value is None:
2533 |             default = param.default
2534 |             if default is None:
2535 |                 # No value was supplied for the parameter and the
2536 |                 # parameter does not have a default, therefore the model
2537 |                 # is underspecified
2538 |                 raise TypeError(&quot;{0}.__init__() requires a value for parameter &quot;
2539 |                                 &quot;{1!r}&quot;.format(self.__class__.__name__, param_name))
2540 |             value = default
2541 |             unit = param.unit
2542 |         else:
2543 |             if isinstance(value, Quantity):
2544 |                 unit = value.unit
2545 |                 value = value.value
2546 |             else:
2547 |                 unit = None
2548 |         if unit is None and param.unit is not None:
2549 |             raise InputParameterError(
2550 |                 &quot;{0}.__init__() requires a Quantity for parameter &quot;
2551 |                 &quot;{1!r}&quot;.format(self.__class__.__name__, param_name))
2552 |         param._unit = unit
2553 |         param.internal_unit = None
2554 |         if param._setter is not None:
2555 |             if unit is not None:
2556 |                 _val = param._setter(value * unit)
2557 |             else:
2558 |                 _val = param._setter(value)
2559 |             if isinstance(_val, Quantity):
2560 |                 param.internal_unit = _val.unit
2561 |                 param._internal_value = np.array(_val.value)
2562 |             else:
2563 |                 param.internal_unit = None
2564 |                 param._internal_value = np.array(_val)
2565 |         else:
2566 |             param._value = np.array(value)
2567 | 
2568 |     def _initialize_slices(self):
2569 | 
2570 |         param_metrics = self._param_metrics
2571 |         total_size = 0
2572 | 
2573 |         for name in self.param_names:
2574 |             param = getattr(self, name)
2575 |             value = param.value
2576 |             param_size = np.size(value)
2577 |             param_shape = np.shape(value)
2578 |             param_slice = slice(total_size, total_size + param_size)
2579 |             param_metrics[name][&#39;slice&#39;] = param_slice
2580 |             param_metrics[name][&#39;shape&#39;] = param_shape
2581 |             param_metrics[name][&#39;size&#39;] = param_size
2582 |             total_size += param_size
2583 |         self._parameters = np.empty(total_size, dtype=np.float64)
2584 | 
2585 |     def _parameters_to_array(self):
2586 |         # Now set the parameter values (this will also fill
2587 |         # self._parameters)
2588 |         param_metrics = self._param_metrics
2589 |         for name in self.param_names:
2590 |             param = getattr(self, name)
2591 |             value = param.value
2592 |             if not isinstance(value, np.ndarray):
2593 |                 value = np.array([value])
2594 |             self._parameters[param_metrics[name][&#39;slice&#39;]] = value.ravel()
2595 | 
2596 |         # Finally validate all the parameters; we do this last so that
2597 |         # validators that depend on one of the other parameters&#39; values will
2598 |         # work
2599 | 
2600 |     def _array_to_parameters(self):
2601 |         param_metrics = self._param_metrics
2602 |         for name in self.param_names:
2603 |             param = getattr(self, name)
2604 |             value = self._parameters[param_metrics[name][&#39;slice&#39;]]
2605 |             value.shape = param_metrics[name][&#39;shape&#39;]
2606 |             param.value = value
2607 | 
2608 |     def _check_param_broadcast(self, max_ndim):
2609 |         &quot;&quot;&quot;
2610 |         This subroutine checks that all parameter arrays can be broadcast
2611 |         against each other, and determines the shapes parameters must have in
2612 |         order to broadcast correctly.
2613 | 
2614 |         If model_set_axis is None this merely checks that the parameters
2615 |         broadcast and returns an empty dict if so.  This mode is only used for
2616 |         single model sets.
2617 |         &quot;&quot;&quot;
2618 |         all_shapes = []
2619 |         model_set_axis = self._model_set_axis
2620 | 
2621 |         for name in self.param_names:
2622 |             param = getattr(self, name)
2623 |             value = param.value
2624 |             param_shape = np.shape(value)
2625 |             param_ndim = len(param_shape)
2626 |             if max_ndim is not None and param_ndim &lt; max_ndim:
2627 |                 # All arrays have the same number of dimensions up to the
2628 |                 # model_set_axis dimension, but after that they may have a
2629 |                 # different number of trailing axes.  The number of trailing
2630 |                 # axes must be extended for mutual compatibility.  For example
2631 |                 # if max_ndim = 3 and model_set_axis = 0, an array with the
2632 |                 # shape (2, 2) must be extended to (2, 1, 2).  However, an
2633 |                 # array with shape (2,) is extended to (2, 1).
2634 |                 new_axes = (1,) * (max_ndim - param_ndim)
2635 | 
2636 |                 if model_set_axis &lt; 0:
2637 |                     # Just need to prepend axes to make up the difference
2638 |                     broadcast_shape = new_axes + param_shape
2639 |                 else:
2640 |                     broadcast_shape = (param_shape[:model_set_axis + 1] +
2641 |                                        new_axes +
2642 |                                        param_shape[model_set_axis + 1:])
2643 |                 self._param_metrics[name][&#39;broadcast_shape&#39;] = broadcast_shape
2644 |                 all_shapes.append(broadcast_shape)
2645 |             else:
2646 |                 all_shapes.append(param_shape)
2647 | 
2648 |         # Now check mutual broadcastability of all shapes
2649 |         try:
2650 |             check_broadcast(*all_shapes)
2651 |         except IncompatibleShapeError as exc:
2652 |             shape_a, shape_a_idx, shape_b, shape_b_idx = exc.args
2653 |             param_a = self.param_names[shape_a_idx]
2654 |             param_b = self.param_names[shape_b_idx]
2655 | 
2656 |             raise InputParameterError(
2657 |                 &quot;Parameter {0!r} of shape {1!r} cannot be broadcast with &quot;
2658 |                 &quot;parameter {2!r} of shape {3!r}.  All parameter arrays &quot;
2659 |                 &quot;must have shapes that are mutually compatible according &quot;
2660 |                 &quot;to the broadcasting rules.&quot;.format(param_a, shape_a,
2661 |                                                     param_b, shape_b))
2662 | 
2663 |     def _param_sets(self, raw=False, units=False):
2664 |         &quot;&quot;&quot;
2665 |         Implementation of the Model.param_sets property.
2666 | 
2667 |         This internal implementation has a ``raw`` argument which controls
2668 |         whether or not to return the raw parameter values (i.e. the values that
2669 |         are actually stored in the ._parameters array, as opposed to the values
2670 |         displayed to users.  In most cases these are one in the same but there
2671 |         are currently a few exceptions.
2672 | 
2673 |         Note: This is notably an overcomplicated device and may be removed
2674 |         entirely in the near future.
2675 |         &quot;&quot;&quot;
2676 | 
2677 |         values = []
2678 |         shapes = []
2679 |         for name in self.param_names:
2680 |             param = getattr(self, name)
2681 | 
2682 |             if raw and param._setter:
2683 |                 value = param._internal_value
2684 |             else:
2685 |                 value = param.value
2686 | 
2687 |             broadcast_shape = self._param_metrics[name].get(&#39;broadcast_shape&#39;)
2688 |             if broadcast_shape is not None:
2689 |                 value = value.reshape(broadcast_shape)
2690 | 
2691 |             shapes.append(np.shape(value))
2692 | 
2693 |             if len(self) == 1:
2694 |                 # Add a single param set axis to the parameter&#39;s value (thus
2695 |                 # converting scalars to shape (1,) array values) for
2696 |                 # consistency
2697 |                 value = np.array([value])
2698 | 
2699 |             if units:
2700 |                 if raw and param.internal_unit is not None:
2701 |                     unit = param.internal_unit
2702 |                 else:
2703 |                     unit = param.unit
2704 |                 if unit is not None:
2705 |                     value = Quantity(value, unit)
2706 | 
2707 |             values.append(value)
2708 | 
2709 |         if len(set(shapes)) != 1 or units:
2710 |             # If the parameters are not all the same shape, converting to an
2711 |             # array is going to produce an object array
2712 |             # However the way Numpy creates object arrays is tricky in that it
2713 |             # will recurse into array objects in the list and break them up
2714 |             # into separate objects.  Doing things this way ensures a 1-D
2715 |             # object array the elements of which are the individual parameter
2716 |             # arrays.  There&#39;s not much reason to do this over returning a list
2717 |             # except for consistency
2718 |             psets = np.empty(len(values), dtype=object)
2719 |             psets[:] = values
2720 |             return psets
2721 | 
2722 |         return np.array(values)
2723 | 
2724 |     def _format_repr(self, args=[], kwargs={}, defaults={}):
2725 |         &quot;&quot;&quot;
2726 |         Internal implementation of ``__repr__``.
2727 | 
2728 |         This is separated out for ease of use by subclasses that wish to
2729 |         override the default ``__repr__`` while keeping the same basic
2730 |         formatting.
2731 |         &quot;&quot;&quot;
2732 | 
2733 |         parts = [repr(a) for a in args]
2734 | 
2735 |         parts.extend(
2736 |             f&quot;{name}={param_repr_oneline(getattr(self, name))}&quot;
2737 |             for name in self.param_names)
2738 | 
2739 |         if self.name is not None:
2740 |             parts.append(f&#39;name={self.name!r}&#39;)
2741 | 
2742 |         for kwarg, value in kwargs.items():
2743 |             if kwarg in defaults and defaults[kwarg] == value:
2744 |                 continue
2745 |             parts.append(f&#39;{kwarg}={value!r}&#39;)
2746 | 
2747 |         if len(self) &gt; 1:
2748 |             parts.append(f&quot;n_models={len(self)}&quot;)
2749 | 
2750 |         return f&quot;&lt;{self.__class__.__name__}({&#39;, &#39;.join(parts)})&gt;&quot;
2751 | 
2752 |     def _format_str(self, keywords=[], defaults={}):
2753 |         &quot;&quot;&quot;
2754 |         Internal implementation of ``__str__``.
2755 | 
2756 |         This is separated out for ease of use by subclasses that wish to
2757 |         override the default ``__str__`` while keeping the same basic
2758 |         formatting.
2759 |         &quot;&quot;&quot;
2760 | 
2761 |         default_keywords = [
2762 |             (&#39;Model&#39;, self.__class__.__name__),
2763 |             (&#39;Name&#39;, self.name),
2764 |             (&#39;Inputs&#39;, self.inputs),
2765 |             (&#39;Outputs&#39;, self.outputs),
2766 |             (&#39;Model set size&#39;, len(self))
2767 |         ]
2768 | 
2769 |         parts = [f&#39;{keyword}: {value}&#39;
2770 |                  for keyword, value in default_keywords
2771 |                  if value is not None]
2772 | 
2773 |         for keyword, value in keywords:
2774 |             if keyword.lower() in defaults and defaults[keyword.lower()] == value:
2775 |                 continue
2776 |             parts.append(f&#39;{keyword}: {value}&#39;)
2777 |         parts.append(&#39;Parameters:&#39;)
2778 | 
2779 |         if len(self) == 1:
2780 |             columns = [[getattr(self, name).value]
2781 |                        for name in self.param_names]
2782 |         else:
2783 |             columns = [getattr(self, name).value
2784 |                        for name in self.param_names]
2785 | 
2786 |         if columns:
2787 |             param_table = Table(columns, names=self.param_names)
2788 |             # Set units on the columns
2789 |             for name in self.param_names:
2790 |                 param_table[name].unit = getattr(self, name).unit
2791 |             parts.append(indent(str(param_table), width=4))
2792 | 
2793 |         return &#39;\n&#39;.join(parts)
```


##Searching for entity `astropy/modeling/separable.py:_separable`...
### Search Result:
Found function `astropy/modeling/separable.py:_separable`.
Source: Exact match found for entity name `astropy/modeling/separable.py:_separable`.
```
290 | def _separable(transform):
291 |     &quot;&quot;&quot;
292 |     Calculate the separability of outputs.
293 | 
294 |     Parameters
295 |     ----------
296 |     transform : `astropy.modeling.Model`
297 |         A transform (usually a compound model).
298 | 
299 |     Returns :
300 |     is_separable : ndarray of dtype np.bool
301 |         An array of shape (transform.n_outputs,) of boolean type
302 |         Each element represents the separablity of the corresponding output.
303 |     &quot;&quot;&quot;
304 |     if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
305 |         return transform_matrix
306 |     elif isinstance(transform, CompoundModel):
307 |         sepleft = _separable(transform.left)
308 |         sepright = _separable(transform.right)
309 |         return _operators[transform.op](sepleft, sepright)
310 |     elif isinstance(transform, Model):
311 |         return _coord_matrix(transform, &#39;left&#39;, transform.n_outputs)
```
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-11-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-11-0" class="toggle-btn">Show</button></div><div id="call-11-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_t12g6gW5TqWa9WJHdjugvtAn</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "_calculate_separability_matrix"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;_calculate_separability_matrix&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-12')"><span>View full output (614 chars)</span><button id="btn-tool-12" class="toggle-btn">Show</button></div><div id="tool-12" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;_calculate_separability_matrix&quot;...
### Search Result:
Found function `astropy/modeling/core.py:Model._calculate_separability_matrix`.
Source: Match found for entity name `_calculate_separability_matrix`.
```
808 | def _calculate_separability_matrix(self):
809 |         &quot;&quot;&quot;
810 |         This is a hook which customises the behavior of modeling.separable.
811 | 
812 |         This allows complex subclasses to customise the separability matrix.
813 |         If it returns `NotImplemented` the default behavior is used.
814 |         &quot;&quot;&quot;
815 |         return NotImplemented
```
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-13-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-13-0" class="toggle-btn">Show</button></div><div id="call-13-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_b9JbJYCxDztOCzd3GolTpsFH</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "_operators"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-14')"><span>View full output (1575 chars)</span><button id="btn-tool-14" class="toggle-btn">Show</button></div><div id="tool-14" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:
Found function `astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists`.
Source: Retrieved entity using keyword search (bm25).
```
3080 | def _apply_operators_to_value_lists(self, leftval, rightval, **kw):
3081 |         op = self.op
3082 |         if op == &#39;+&#39;:
3083 |             return binary_operation(operator.add, leftval, rightval)
3084 |         elif op == &#39;-&#39;:
3085 |             return binary_operation(operator.sub, leftval, rightval)
3086 |         elif op == &#39;*&#39;:
3087 |             return binary_operation(operator.mul, leftval, rightval)
3088 |         elif op == &#39;/&#39;:
3089 |             return binary_operation(operator.truediv, leftval, rightval)
3090 |         elif op == &#39;**&#39;:
3091 |             return binary_operation(operator.pow, leftval, rightval)
3092 |         elif op == &#39;&amp;&#39;:
3093 |             if not isinstance(leftval, tuple):
3094 |                 leftval = (leftval,)
3095 |             if not isinstance(rightval, tuple):
3096 |                 rightval = (rightval,)
3097 |             return leftval + rightval
3098 |         elif op in SPECIAL_OPERATORS:
3099 |             return binary_operation(SPECIAL_OPERATORS[op], leftval, rightval)
3100 |         else:
3101 |             raise ModelDefinitionError(&#39;Unrecognized operator {op}&#39;)
```

Found file `astropy/units/decorators.py`.
Found file `astropy/utils/decorators.py`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-15-0')"><span>📞 <code>get_entity_contents</code></span><button id="btn-call-15-0" class="toggle-btn">Show</button></div><div id="call-15-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_TlAFrGDJBvorcwexMh5AlW9I</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "entity_names": [
    "astropy/modeling/separable.py:_operators"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: get_entity_contents</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py:_operators`...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-16')"><span>View full output (210 chars)</span><button id="btn-tool-16" class="toggle-btn">Show</button></div><div id="tool-16" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py:_operators`...
### Search Result:
Invalid name. 
Hint: Valid entity name should be formatted as &quot;file_path:QualifiedName&quot; or just &quot;file_path&quot;.
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-17-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-17-0" class="toggle-btn">Show</button></div><div id="call-17-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_5GJpvElBQzorEdd5RTPiPjkV</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "_operators"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-18')"><span>View full output (1575 chars)</span><button id="btn-tool-18" class="toggle-btn">Show</button></div><div id="tool-18" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:
Found function `astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists`.
Source: Retrieved entity using keyword search (bm25).
```
3080 | def _apply_operators_to_value_lists(self, leftval, rightval, **kw):
3081 |         op = self.op
3082 |         if op == &#39;+&#39;:
3083 |             return binary_operation(operator.add, leftval, rightval)
3084 |         elif op == &#39;-&#39;:
3085 |             return binary_operation(operator.sub, leftval, rightval)
3086 |         elif op == &#39;*&#39;:
3087 |             return binary_operation(operator.mul, leftval, rightval)
3088 |         elif op == &#39;/&#39;:
3089 |             return binary_operation(operator.truediv, leftval, rightval)
3090 |         elif op == &#39;**&#39;:
3091 |             return binary_operation(operator.pow, leftval, rightval)
3092 |         elif op == &#39;&amp;&#39;:
3093 |             if not isinstance(leftval, tuple):
3094 |                 leftval = (leftval,)
3095 |             if not isinstance(rightval, tuple):
3096 |                 rightval = (rightval,)
3097 |             return leftval + rightval
3098 |         elif op in SPECIAL_OPERATORS:
3099 |             return binary_operation(SPECIAL_OPERATORS[op], leftval, rightval)
3100 |         else:
3101 |             raise ModelDefinitionError(&#39;Unrecognized operator {op}&#39;)
```

Found file `astropy/units/decorators.py`.
Found file `astropy/utils/decorators.py`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-19-0')"><span>📞 <code>search_code_snippets</code></span><button id="btn-call-19-0" class="toggle-btn">Show</button></div><div id="call-19-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_NmCy2mdNzt5TTqzburCgyOUT</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "search_terms": [
    "_operators",
    "separable"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: search_code_snippets</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-20')"><span>View full output (3750 chars)</span><button id="btn-tool-20" class="toggle-btn">Show</button></div><div id="tool-20" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for term &quot;_operators&quot;...
### Search Result:
Found function `astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists`.
Source: Retrieved entity using keyword search (bm25).
```
3080 | def _apply_operators_to_value_lists(self, leftval, rightval, **kw):
3081 |         op = self.op
3082 |         if op == &#39;+&#39;:
3083 |             return binary_operation(operator.add, leftval, rightval)
3084 |         elif op == &#39;-&#39;:
3085 |             return binary_operation(operator.sub, leftval, rightval)
3086 |         elif op == &#39;*&#39;:
3087 |             return binary_operation(operator.mul, leftval, rightval)
3088 |         elif op == &#39;/&#39;:
3089 |             return binary_operation(operator.truediv, leftval, rightval)
3090 |         elif op == &#39;**&#39;:
3091 |             return binary_operation(operator.pow, leftval, rightval)
3092 |         elif op == &#39;&amp;&#39;:
3093 |             if not isinstance(leftval, tuple):
3094 |                 leftval = (leftval,)
3095 |             if not isinstance(rightval, tuple):
3096 |                 rightval = (rightval,)
3097 |             return leftval + rightval
3098 |         elif op in SPECIAL_OPERATORS:
3099 |             return binary_operation(SPECIAL_OPERATORS[op], leftval, rightval)
3100 |         else:
3101 |             raise ModelDefinitionError(&#39;Unrecognized operator {op}&#39;)
```

Found file `astropy/units/decorators.py`.
Found file `astropy/utils/decorators.py`.
Source: Retrieved entity using keyword search (bm25).
Hint: Use more detailed query to get the full content of some if needed.



##Searching for term &quot;separable&quot;...
### Search Result:
Found file `astropy/modeling/separable.py`.
Source: Match found for entity name `separable`.
Just show the structure of this file due to response length limitations:
```
# Licensed under a 3-clause BSD style license - see LICENSE.rst



__all__ = [&quot;is_separable&quot;, &quot;separability_matrix&quot;]


def is_separable(transform):
...

def separability_matrix(transform):
...

def _compute_n_outputs(left, right):
...

def _arith_oper(left, right):
...

def _coord_matrix(model, pos, noutp):
...

def _cstack(left, right):
...

def _cdot(left, right):
...

def _separable(transform):
...

# Maps modeling operators to a function computing and represents the
# relationship of axes as an array of 0-es and 1-s
_operators = {&#39;&amp;&#39;: _cstack, &#39;|&#39;: _cdot, &#39;+&#39;: _arith_oper, &#39;-&#39;: _arith_oper,
              &#39;*&#39;: _arith_oper, &#39;/&#39;: _arith_oper, &#39;**&#39;: _arith_oper}

```
Hint: Search `astropy/modeling/separable.py` to get the full content if needed.

Found function `astropy/convolution/core.py:Kernel.separable`.
Source: Match found for entity name `separable`.
```
124 | def separable(self):
125 |         &quot;&quot;&quot;
126 |         Indicates if the filter kernel is separable.
127 | 
128 |         A 2D filter is separable, when its filter array can be written as the
129 |         outer product of two 1D arrays.
130 | 
131 |         If a filter kernel is separable, higher dimension convolutions will be
132 |         performed by applying the 1D filter array consecutively on every dimension.
133 |         This is significantly faster, than using a filter array with the same
134 |         dimension.
135 |         &quot;&quot;&quot;
136 |         return self._separable
```

Found function `astropy/modeling/core.py:Model.separable`.
Source: Match found for entity name `separable`.
```
1532 | def separable(self):
1533 |         &quot;&quot;&quot; A flag indicating whether a model is separable.&quot;&quot;&quot;
1534 | 
1535 |         if self._separable is not None:
1536 |             return self._separable
1537 |         raise NotImplementedError(
1538 |             &#39;The &quot;separable&quot; property is not defined for &#39;
1539 |             &#39;model {}&#39;.format(self.__class__.__name__))
```
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-21-0')"><span>📞 <code>get_entity_contents</code></span><button id="btn-call-21-0" class="toggle-btn">Show</button></div><div id="call-21-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_IaRmz0kO6WjLaslWH9mDfCdZ</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "entity_names": [
    "astropy/modeling/separable.py"
  ]
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: get_entity_contents</div><div class="message-content"><div class="observation-preview">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py`...
### Search Result:...</div><div class="collapsible" onclick="toggleContent('tool-22')"><span>View full output (12004 chars)</span><button id="btn-tool-22" class="toggle-btn">Show</button></div><div id="tool-22" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
##Searching for entity `astropy/modeling/separable.py`...
### Search Result:
Found file `astropy/modeling/separable.py`.
Source: Exact match found for entity name `astropy/modeling/separable.py`.
```
  1 | # Licensed under a 3-clause BSD style license - see LICENSE.rst
  2 | 
  3 | &quot;&quot;&quot;
  4 | Functions to determine if a model is separable, i.e.
  5 | if the model outputs are independent.
  6 | 
  7 | It analyzes ``n_inputs``, ``n_outputs`` and the operators
  8 | in a compound model by stepping through the transforms
  9 | and creating a ``coord_matrix`` of shape (``n_outputs``, ``n_inputs``).
 10 | 
 11 | 
 12 | Each modeling operator is represented by a function which
 13 | takes two simple models (or two ``coord_matrix`` arrays) and
 14 | returns an array of shape (``n_outputs``, ``n_inputs``).
 15 | 
 16 | &quot;&quot;&quot;
 17 | 
 18 | import numpy as np
 19 | 
 20 | from .core import Model, ModelDefinitionError, CompoundModel
 21 | from .mappings import Mapping
 22 | 
 23 | 
 24 | __all__ = [&quot;is_separable&quot;, &quot;separability_matrix&quot;]
 25 | 
 26 | 
 27 | def is_separable(transform):
 28 |     &quot;&quot;&quot;
 29 |     A separability test for the outputs of a transform.
 30 | 
 31 |     Parameters
 32 |     ----------
 33 |     transform : `~astropy.modeling.core.Model`
 34 |         A (compound) model.
 35 | 
 36 |     Returns
 37 |     -------
 38 |     is_separable : ndarray
 39 |         A boolean array with size ``transform.n_outputs`` where
 40 |         each element indicates whether the output is independent
 41 |         and the result of a separable transform.
 42 | 
 43 |     Examples
 44 |     --------
 45 |     &gt;&gt;&gt; from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
 46 |     &gt;&gt;&gt; is_separable(Shift(1) &amp; Shift(2) | Scale(1) &amp; Scale(2))
 47 |         array([ True,  True]...)
 48 |     &gt;&gt;&gt; is_separable(Shift(1) &amp; Shift(2) | Rotation2D(2))
 49 |         array([False, False]...)
 50 |     &gt;&gt;&gt; is_separable(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]) | \
 51 |         Polynomial2D(1) &amp; Polynomial2D(2))
 52 |         array([False, False]...)
 53 |     &gt;&gt;&gt; is_separable(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]))
 54 |         array([ True,  True,  True,  True]...)
 55 | 
 56 |     &quot;&quot;&quot;
 57 |     if transform.n_inputs == 1 and transform.n_outputs &gt; 1:
 58 |         is_separable = np.array([False] * transform.n_outputs).T
 59 |         return is_separable
 60 |     separable_matrix = _separable(transform)
 61 |     is_separable = separable_matrix.sum(1)
 62 |     is_separable = np.where(is_separable != 1, False, True)
 63 |     return is_separable
 64 | 
 65 | 
 66 | def separability_matrix(transform):
 67 |     &quot;&quot;&quot;
 68 |     Compute the correlation between outputs and inputs.
 69 | 
 70 |     Parameters
 71 |     ----------
 72 |     transform : `~astropy.modeling.core.Model`
 73 |         A (compound) model.
 74 | 
 75 |     Returns
 76 |     -------
 77 |     separable_matrix : ndarray
 78 |         A boolean correlation matrix of shape (n_outputs, n_inputs).
 79 |         Indicates the dependence of outputs on inputs. For completely
 80 |         independent outputs, the diagonal elements are True and
 81 |         off-diagonal elements are False.
 82 | 
 83 |     Examples
 84 |     --------
 85 |     &gt;&gt;&gt; from astropy.modeling.models import Shift, Scale, Rotation2D, Polynomial2D
 86 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Scale(1) &amp; Scale(2))
 87 |         array([[ True, False], [False,  True]]...)
 88 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Rotation2D(2))
 89 |         array([[ True,  True], [ True,  True]]...)
 90 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]) | \
 91 |         Polynomial2D(1) &amp; Polynomial2D(2))
 92 |         array([[ True,  True], [ True,  True]]...)
 93 |     &gt;&gt;&gt; separability_matrix(Shift(1) &amp; Shift(2) | Mapping([0, 1, 0, 1]))
 94 |         array([[ True, False], [False,  True], [ True, False], [False,  True]]...)
 95 | 
 96 |     &quot;&quot;&quot;
 97 |     if transform.n_inputs == 1 and transform.n_outputs &gt; 1:
 98 |         return np.ones((transform.n_outputs, transform.n_inputs),
 99 |                        dtype=np.bool_)
100 |     separable_matrix = _separable(transform)
101 |     separable_matrix = np.where(separable_matrix != 0, True, False)
102 |     return separable_matrix
103 | 
104 | 
105 | def _compute_n_outputs(left, right):
106 |     &quot;&quot;&quot;
107 |     Compute the number of outputs of two models.
108 | 
109 |     The two models are the left and right model to an operation in
110 |     the expression tree of a compound model.
111 | 
112 |     Parameters
113 |     ----------
114 |     left, right : `astropy.modeling.Model` or ndarray
115 |         If input is of an array, it is the output of `coord_matrix`.
116 | 
117 |     &quot;&quot;&quot;
118 |     if isinstance(left, Model):
119 |         lnout = left.n_outputs
120 |     else:
121 |         lnout = left.shape[0]
122 |     if isinstance(right, Model):
123 |         rnout = right.n_outputs
124 |     else:
125 |         rnout = right.shape[0]
126 |     noutp = lnout + rnout
127 |     return noutp
128 | 
129 | 
130 | def _arith_oper(left, right):
131 |     &quot;&quot;&quot;
132 |     Function corresponding to one of the arithmetic operators
133 |     [&#39;+&#39;, &#39;-&#39;. &#39;*&#39;, &#39;/&#39;, &#39;**&#39;].
134 | 
135 |     This always returns a nonseparable output.
136 | 
137 | 
138 |     Parameters
139 |     ----------
140 |     left, right : `astropy.modeling.Model` or ndarray
141 |         If input is of an array, it is the output of `coord_matrix`.
142 | 
143 |     Returns
144 |     -------
145 |     result : ndarray
146 |         Result from this operation.
147 |     &quot;&quot;&quot;
148 |     # models have the same number of inputs and outputs
149 |     def _n_inputs_outputs(input):
150 |         if isinstance(input, Model):
151 |             n_outputs, n_inputs = input.n_outputs, input.n_inputs
152 |         else:
153 |             n_outputs, n_inputs = input.shape
154 |         return n_inputs, n_outputs
155 | 
156 |     left_inputs, left_outputs = _n_inputs_outputs(left)
157 |     right_inputs, right_outputs = _n_inputs_outputs(right)
158 | 
159 |     if left_inputs != right_inputs or left_outputs != right_outputs:
160 |         raise ModelDefinitionError(
161 |             &quot;Unsupported operands for arithmetic operator: left (n_inputs={}, &quot;
162 |             &quot;n_outputs={}) and right (n_inputs={}, n_outputs={}); &quot;
163 |             &quot;models must have the same n_inputs and the same &quot;
164 |             &quot;n_outputs for this operator.&quot;.format(
165 |                 left_inputs, left_outputs, right_inputs, right_outputs))
166 | 
167 |     result = np.ones((left_outputs, left_inputs))
168 |     return result
169 | 
170 | 
171 | def _coord_matrix(model, pos, noutp):
172 |     &quot;&quot;&quot;
173 |     Create an array representing inputs and outputs of a simple model.
174 | 
175 |     The array has a shape (noutp, model.n_inputs).
176 | 
177 |     Parameters
178 |     ----------
179 |     model : `astropy.modeling.Model`
180 |         model
181 |     pos : str
182 |         Position of this model in the expression tree.
183 |         One of [&#39;left&#39;, &#39;right&#39;].
184 |     noutp : int
185 |         Number of outputs of the compound model of which the input model
186 |         is a left or right child.
187 | 
188 |     &quot;&quot;&quot;
189 |     if isinstance(model, Mapping):
190 |         axes = []
191 |         for i in model.mapping:
192 |             axis = np.zeros((model.n_inputs,))
193 |             axis[i] = 1
194 |             axes.append(axis)
195 |         m = np.vstack(axes)
196 |         mat = np.zeros((noutp, model.n_inputs))
197 |         if pos == &#39;left&#39;:
198 |             mat[: model.n_outputs, :model.n_inputs] = m
199 |         else:
200 |             mat[-model.n_outputs:, -model.n_inputs:] = m
201 |         return mat
202 |     if not model.separable:
203 |         # this does not work for more than 2 coordinates
204 |         mat = np.zeros((noutp, model.n_inputs))
205 |         if pos == &#39;left&#39;:
206 |             mat[:model.n_outputs, : model.n_inputs] = 1
207 |         else:
208 |             mat[-model.n_outputs:, -model.n_inputs:] = 1
209 |     else:
210 |         mat = np.zeros((noutp, model.n_inputs))
211 | 
212 |         for i in range(model.n_inputs):
213 |             mat[i, i] = 1
214 |         if pos == &#39;right&#39;:
215 |             mat = np.roll(mat, (noutp - model.n_outputs))
216 |     return mat
217 | 
218 | 
219 | def _cstack(left, right):
220 |     &quot;&quot;&quot;
221 |     Function corresponding to &#39;&amp;&#39; operation.
222 | 
223 |     Parameters
224 |     ----------
225 |     left, right : `astropy.modeling.Model` or ndarray
226 |         If input is of an array, it is the output of `coord_matrix`.
227 | 
228 |     Returns
229 |     -------
230 |     result : ndarray
231 |         Result from this operation.
232 | 
233 |     &quot;&quot;&quot;
234 |     noutp = _compute_n_outputs(left, right)
235 | 
236 |     if isinstance(left, Model):
237 |         cleft = _coord_matrix(left, &#39;left&#39;, noutp)
238 |     else:
239 |         cleft = np.zeros((noutp, left.shape[1]))
240 |         cleft[: left.shape[0], : left.shape[1]] = left
241 |     if isinstance(right, Model):
242 |         cright = _coord_matrix(right, &#39;right&#39;, noutp)
243 |     else:
244 |         cright = np.zeros((noutp, right.shape[1]))
245 |         cright[-right.shape[0]:, -right.shape[1]:] = 1
246 | 
247 |     return np.hstack([cleft, cright])
248 | 
249 | 
250 | def _cdot(left, right):
251 |     &quot;&quot;&quot;
252 |     Function corresponding to &quot;|&quot; operation.
253 | 
254 |     Parameters
255 |     ----------
256 |     left, right : `astropy.modeling.Model` or ndarray
257 |         If input is of an array, it is the output of `coord_matrix`.
258 | 
259 |     Returns
260 |     -------
261 |     result : ndarray
262 |         Result from this operation.
263 |     &quot;&quot;&quot;
264 | 
265 |     left, right = right, left
266 | 
267 |     def _n_inputs_outputs(input, position):
268 |         &quot;&quot;&quot;
269 |         Return ``n_inputs``, ``n_outputs`` for a model or coord_matrix.
270 |         &quot;&quot;&quot;
271 |         if isinstance(input, Model):
272 |             coords = _coord_matrix(input, position, input.n_outputs)
273 |         else:
274 |             coords = input
275 |         return coords
276 | 
277 |     cleft = _n_inputs_outputs(left, &#39;left&#39;)
278 |     cright = _n_inputs_outputs(right, &#39;right&#39;)
279 | 
280 |     try:
281 |         result = np.dot(cleft, cright)
282 |     except ValueError:
283 |         raise ModelDefinitionError(
284 |             &#39;Models cannot be combined with the &quot;|&quot; operator; &#39;
285 |             &#39;left coord_matrix is {}, right coord_matrix is {}&#39;.format(
286 |                 cright, cleft))
287 |     return result
288 | 
289 | 
290 | def _separable(transform):
291 |     &quot;&quot;&quot;
292 |     Calculate the separability of outputs.
293 | 
294 |     Parameters
295 |     ----------
296 |     transform : `astropy.modeling.Model`
297 |         A transform (usually a compound model).
298 | 
299 |     Returns :
300 |     is_separable : ndarray of dtype np.bool
301 |         An array of shape (transform.n_outputs,) of boolean type
302 |         Each element represents the separablity of the corresponding output.
303 |     &quot;&quot;&quot;
304 |     if (transform_matrix := transform._calculate_separability_matrix()) is not NotImplemented:
305 |         return transform_matrix
306 |     elif isinstance(transform, CompoundModel):
307 |         sepleft = _separable(transform.left)
308 |         sepright = _separable(transform.right)
309 |         return _operators[transform.op](sepleft, sepright)
310 |     elif isinstance(transform, Model):
311 |         return _coord_matrix(transform, &#39;left&#39;, transform.n_outputs)
312 | 
313 | 
314 | # Maps modeling operators to a function computing and represents the
315 | # relationship of axes as an array of 0-es and 1-s
316 | _operators = {&#39;&amp;&#39;: _cstack, &#39;|&#39;: _cdot, &#39;+&#39;: _arith_oper, &#39;-&#39;: _arith_oper,
317 |               &#39;*&#39;: _arith_oper, &#39;/&#39;: _arith_oper, &#39;**&#39;: _arith_oper}
318 | 
```
</pre></div></div></div><div class="message-block assistant-message"><div class="message-header">🤖 Assistant Response</div><div class="message-content">```
astropy/modeling/separable.py
line: 66
function: separability_matrix
line: 290
function: _separable
line: 316
variable: _operators

astropy/modeling/core.py
line: 2894
class: CompoundModel
line: 3080
function: _apply_operators_to_value_lists (inside CompoundModel)
line: 807
function: _calculate_separability_matrix (inside Model)
line: 1532
function: separable (property inside Model)
```</div></div><div class="message-block user-message"><div class="message-header">👤 User Input <span class="step-number">Step 2</span></div><div class="message-content">Verify if the found locations contain all the necessary information to address the issue, and check for any relevant references in other parts of the codebase that may not have appeared in the search results. If not, continue searching for additional locations related to the issue.
Verify that you have carefully analyzed the impact of the found locations on the repository, especially their dependencies. If you think you have solved the task, please send your final answer (including the former an...</div><div class="collapsible" onclick="toggleContent('user-24')"><span>Show full content (626 chars)</span><button id="btn-user-24" class="toggle-btn">Show</button></div><div id="user-24" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: inherit; font-size: 14px; margin: 0;">Verify if the found locations contain all the necessary information to address the issue, and check for any relevant references in other parts of the codebase that may not have appeared in the search results. If not, continue searching for additional locations related to the issue.
Verify that you have carefully analyzed the impact of the found locations on the repository, especially their dependencies. If you think you have solved the task, please send your final answer (including the former answer and reranking) to user through message and then call `finish` to finish.
IMPORTANT: YOU SHOULD NEVER ASK FOR HUMAN HELP.
</pre></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (3 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-25-0')"><span>📞 <code>explore_tree_structure</code></span><button id="btn-call-25-0" class="toggle-btn">Show</button></div><div id="call-25-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_kezOlfpwREso7dlnC19X3Yvv</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "start_entities": [
    "astropy/modeling/separable.py:separability_matrix",
    "astropy/modeling/separable.py:_separable"
  ],
  "direction": "downstream",
  "traversal_depth": 2
}</div></div><div class="collapsible" onclick="toggleContent('call-25-1')"><span>📞 <code>explore_tree_structure</code></span><button id="btn-call-25-1" class="toggle-btn">Show</button></div><div id="call-25-1" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_i72hwzrx1QfAKFeZuUqrkzud</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "start_entities": [
    "astropy/modeling/core.py:CompoundModel"
  ],
  "direction": "downstream",
  "traversal_depth": 2
}</div></div><div class="collapsible" onclick="toggleContent('call-25-2')"><span>📞 <code>explore_tree_structure</code></span><button id="btn-call-25-2" class="toggle-btn">Show</button></div><div id="call-25-2" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_NINRva9kkD8qb164UHN7m9E0</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{
  "start_entities": [
    "astropy/modeling/core.py:Model._calculate_separability_matrix"
  ],
  "direction": "upstream",
  "traversal_depth": 2
}</div></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: explore_tree_structure</div><div class="message-content"><div class="observation-preview">OBSERVATION:
astropy/modeling/separable.py:separability_matrix
├── invokes ── astropy/modeling/separable.py:_separable...</div><div class="collapsible" onclick="toggleContent('tool-26')"><span>View full output (2633 chars)</span><button id="btn-tool-26" class="toggle-btn">Show</button></div><div id="tool-26" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
astropy/modeling/separable.py:separability_matrix
├── invokes ── astropy/modeling/separable.py:_separable
│              ├── invokes ── astropy/modeling/core.py:Model._calculate_separability_matrix
│              ├── invokes ── astropy/modeling/separable.py:_separable
│              ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│              └── invokes ── astropy/modeling/separable.py:_coord_matrix
├── invokes ── astropy/table/index.py:SlicedIndex.where
│              ├── invokes ── astropy/table/index.py:SlicedIndex.where
│              ├── invokes ── astropy/units/quantity_helper/function_helpers.py:where
│              ├── invokes ── astropy/utils/masked/function_helpers.py:where
│              └── invokes ── astropy/table/index.py:SlicedIndex.sliced_coords
├── invokes ── astropy/units/quantity_helper/function_helpers.py:where
└── invokes ── astropy/utils/masked/function_helpers.py:where

astropy/modeling/separable.py:_separable
├── invokes ── astropy/modeling/core.py:Model._calculate_separability_matrix
├── invokes ── astropy/modeling/separable.py:_separable
├── invokes ── astropy/table/table.py:TableColumns.isinstance
│              ├── invokes ── astropy/table/row.py:Row.values
│              ├── invokes ── astropy/config/configuration.py:ConfigNamespace.values
│              ├── invokes ── astropy/table/table.py:Table.values
│              ├── invokes ── astropy/units/structured.py:StructuredUnit.values
│              └── invokes ── astropy/table/table.py:TableColumns.isinstance
└── invokes ── astropy/modeling/separable.py:_coord_matrix
               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
               ├── invokes ── astropy/io/fits/convenience.py:append
               ├── invokes ── astropy/io/fits/header.py:Header.append
               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
               ├── invokes ── astropy/utils/masked/function_helpers.py:append
               ├── invokes ── astropy/table/operations.py:vstack
               ├── invokes ── astropy/table/bst.py:BST.range
               ├── invokes ── astropy/table/index.py:Index.range
               ├── invokes ── astropy/table/index.py:SlicedIndex.range
               ├── invokes ── astropy/table/soco.py:SCEngine.range
               └── invokes ── astropy/table/sorted_array.py:SortedArray.range
</pre></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: explore_tree_structure</div><div class="message-content"><div class="observation-preview">OBSERVATION:
astropy/modeling/core.py:CompoundModel
├── contains ── astropy/modeling/core.py:CompoundModel._get_left_inputs_from_args...</div><div class="collapsible" onclick="toggleContent('tool-27')"><span>View full output (37136 chars)</span><button id="btn-tool-27" class="toggle-btn">Show</button></div><div id="tool-27" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
astropy/modeling/core.py:CompoundModel
├── contains ── astropy/modeling/core.py:CompoundModel._get_left_inputs_from_args
├── contains ── astropy/modeling/core.py:CompoundModel._get_right_inputs_from_args
├── contains ── astropy/modeling/core.py:CompoundModel._get_left_params_from_args
├── contains ── astropy/modeling/core.py:CompoundModel._get_right_params_from_args
├── contains ── astropy/modeling/core.py:CompoundModel._get_kwarg_model_parameters_as_positional
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.pop
│               ├── invokes ── astropy/io/fits/header.py:Header.pop
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.pop
│               ├── invokes ── astropy/io/fits/convenience.py:append
│               ├── invokes ── astropy/io/fits/header.py:Header.append
│               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
│               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
│               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
│               └── invokes ── astropy/utils/masked/function_helpers.py:append
├── contains ── astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists
│               ├── invokes ── astropy/modeling/core.py:binary_operation
│               ├── invokes ── astropy/modeling/core.py:ModelDefinitionError
│               └── invokes ── astropy/table/table.py:TableColumns.isinstance
├── contains ── astropy/modeling/core.py:CompoundModel.evaluate
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.dict
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/io/votable/tree.py:Values.type
│               ├── invokes ── astropy/io/votable/tree.py:Field.type
│               ├── invokes ── astropy/io/votable/tree.py:Resource.type
│               ├── invokes ── astropy/units/structured.py:StructuredUnit.items
│               ├── invokes ── astropy/table/bst.py:BST.range
│               ├── invokes ── astropy/table/index.py:Index.range
│               ├── invokes ── astropy/table/index.py:SlicedIndex.range
│               ├── invokes ── astropy/table/soco.py:SCEngine.range
│               ├── invokes ── astropy/table/sorted_array.py:SortedArray.range
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._get_right_inputs_from_args
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._get_left_params_from_args
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._get_left_inputs_from_args
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._get_kwarg_model_parameters_as_positional
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._get_right_params_from_args
│               ├── invokes ── astropy/modeling/core.py:Model.evaluate
│               ├── invokes ── astropy/modeling/mappings.py:UnitsMapping.evaluate
│               └── invokes ── astropy/units/structured.py:StructuredUnit.keys
├── contains ── astropy/modeling/core.py:CompoundModel.n_submodels
│               └── invokes ── astropy/modeling/core.py:CompoundModel._make_leaflist
├── contains ── astropy/modeling/core.py:CompoundModel.submodel_names
│               ├── invokes ── astropy/io/fits/convenience.py:append
│               ├── invokes ── astropy/io/fits/header.py:Header.append
│               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
│               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
│               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
│               ├── invokes ── astropy/utils/masked/function_helpers.py:append
│               └── invokes ── astropy/modeling/core.py:CompoundModel._make_leaflist
├── contains ── astropy/modeling/core.py:CompoundModel.both_inverses_exist
├── contains ── astropy/modeling/core.py:CompoundModel._pre_evaluate
│               ├── contains ── astropy/modeling/core.py:CompoundModel._pre_evaluate.evaluate
│               ├── invokes ── astropy/modeling/core.py:CompoundModel.inputs_map
│               └── invokes ── astropy/units/structured.py:StructuredUnit.items
├── contains ── astropy/modeling/core.py:CompoundModel._argnames
├── contains ── astropy/modeling/core.py:CompoundModel._post_evaluate
│               └── invokes ── astropy/modeling/core.py:Model.get_bounding_box
├── contains ── astropy/modeling/core.py:CompoundModel._evaluate
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/io/votable/tree.py:Values.type
│               ├── invokes ── astropy/io/votable/tree.py:Field.type
│               ├── invokes ── astropy/io/votable/tree.py:Resource.type
│               ├── invokes ── astropy/io/fits/convenience.py:append
│               ├── invokes ── astropy/io/fits/header.py:Header.append
│               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
│               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
│               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
│               ├── invokes ── astropy/utils/masked/function_helpers.py:append
│               ├── invokes ── astropy/table/bst.py:BST.sort
│               ├── invokes ── astropy/table/index.py:Index.sort
│               ├── invokes ── astropy/table/index.py:SlicedIndex.sort
│               ├── invokes ── astropy/table/meta.py:ColumnOrderList.sort
│               ├── invokes ── astropy/table/soco.py:SCEngine.sort
│               ├── invokes ── astropy/table/sorted_array.py:SortedArray.sort
│               ├── invokes ── astropy/table/table.py:Table.sort
│               ├── invokes ── astropy/time/core.py:TimeBase.sort
│               ├── invokes ── astropy/utils/masked/core.py:MaskedNDArray.sort
│               ├── invokes ── astropy/visualization/wcsaxes/ticklabels.py:TickLabels.sort
│               ├── invokes ── astropy/units/quantity.py:QuantityIterator.index
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._apply_operators_to_value_lists
│               ├── invokes ── astropy/units/quantity.py:Quantity.insert
│               └── invokes ── astropy/units/structured.py:StructuredUnit.keys
├── contains ── astropy/modeling/core.py:CompoundModel.param_names
├── contains ── astropy/modeling/core.py:CompoundModel._make_leaflist
├── contains ── astropy/modeling/core.py:CompoundModel.__getattr__
├── contains ── astropy/modeling/core.py:CompoundModel.__getitem__
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/io/votable/tree.py:Values.type
│               ├── invokes ── astropy/io/votable/tree.py:Field.type
│               ├── invokes ── astropy/io/votable/tree.py:Resource.type
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._make_leaflist
│               └── invokes ── astropy/modeling/core.py:CompoundModel._str_index_to_int
├── contains ── astropy/modeling/core.py:CompoundModel._str_index_to_int
├── contains ── astropy/modeling/core.py:CompoundModel.n_inputs
├── contains ── astropy/modeling/core.py:CompoundModel.n_outputs
├── contains ── astropy/modeling/core.py:CompoundModel.eqcons
├── contains ── astropy/modeling/core.py:CompoundModel.ineqcons
├── contains ── astropy/modeling/core.py:CompoundModel.traverse_postorder
│               ├── invokes ── astropy/modeling/core.py:CompoundModel.traverse_postorder
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/io/fits/convenience.py:append
│               ├── invokes ── astropy/io/fits/header.py:Header.append
│               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
│               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
│               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
│               └── invokes ── astropy/utils/masked/function_helpers.py:append
├── contains ── astropy/modeling/core.py:CompoundModel._format_expression
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/io/fits/convenience.py:append
│               ├── invokes ── astropy/io/fits/header.py:Header.append
│               ├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
│               ├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
│               ├── invokes ── astropy/utils/collections.py:HomogeneousList.append
│               ├── invokes ── astropy/utils/masked/function_helpers.py:append
│               ├── invokes ── astropy/io/ascii/core.py:BaseSplitter.join
│               ├── invokes ── astropy/io/ascii/core.py:DefaultSplitter.join
│               ├── invokes ── astropy/io/ascii/fixedwidth.py:FixedWidthSplitter.join
│               ├── invokes ── astropy/io/ascii/ipac.py:IpacHeaderSplitter.join
│               ├── invokes ── astropy/io/ascii/latex.py:LatexSplitter.join
│               ├── invokes ── astropy/io/ascii/latex.py:AASTexHeaderSplitter.join
│               ├── invokes ── astropy/io/ascii/mrt.py:MrtSplitter.join
│               ├── invokes ── astropy/table/operations.py:join
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.pop
│               ├── invokes ── astropy/io/fits/header.py:Header.pop
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.pop
│               └── invokes ── astropy/modeling/core.py:CompoundModel.traverse_postorder
├── contains ── astropy/modeling/core.py:CompoundModel._format_components
│               ├── invokes ── astropy/io/votable/tree.py:Table.format
│               ├── invokes ── astropy/table/column.py:BaseColumn.format
│               ├── invokes ── astropy/time/core.py:TimeBase.format
│               ├── invokes ── astropy/visualization/time.py:time_support.MplTimeConverter.format
│               ├── invokes ── astropy/visualization/wcsaxes/formatter_locator.py:AngleFormatterLocator.format
│               ├── invokes ── astropy/visualization/wcsaxes/formatter_locator.py:ScalarFormatterLocator.format
│               ├── invokes ── astropy/io/ascii/core.py:BaseSplitter.join
│               ├── invokes ── astropy/io/ascii/core.py:DefaultSplitter.join
│               ├── invokes ── astropy/io/ascii/fixedwidth.py:FixedWidthSplitter.join
│               ├── invokes ── astropy/io/ascii/ipac.py:IpacHeaderSplitter.join
│               ├── invokes ── astropy/io/ascii/latex.py:LatexSplitter.join
│               ├── invokes ── astropy/io/ascii/latex.py:AASTexHeaderSplitter.join
│               ├── invokes ── astropy/io/ascii/mrt.py:MrtSplitter.join
│               ├── invokes ── astropy/table/operations.py:join
│               └── invokes ── astropy/modeling/core.py:CompoundModel._map_parameters
├── contains ── astropy/modeling/core.py:CompoundModel.__str__
│               ├── invokes ── astropy/modeling/core.py:Model._format_str
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._format_components
│               ├── invokes ── astropy/utils/misc.py:indent
│               └── invokes ── astropy/modeling/core.py:CompoundModel._format_expression
├── contains ── astropy/modeling/core.py:CompoundModel.rename
├── contains ── astropy/modeling/core.py:CompoundModel.isleaf
├── contains ── astropy/modeling/core.py:CompoundModel.inverse
├── contains ── astropy/modeling/core.py:CompoundModel.fittable
│               ├── invokes ── astropy/units/quantity.py:Quantity.all
│               └── invokes ── astropy/modeling/core.py:CompoundModel._map_parameters
├── contains ── astropy/modeling/core.py:CompoundModel._map_parameters
├── invokes ── astropy/modeling/core.py:CompoundModel._map_parameters
├── contains ── astropy/modeling/core.py:CompoundModel._initialize_slices
│               ├── invokes ── astropy/modeling/parameters.py:Parameter.shape
│               ├── invokes ── astropy/utils/shapes.py:ShapedLikeNDArray.shape
│               ├── invokes ── astropy/wcs/wcs.py:WCS.slice
│               ├── invokes ── astropy/modeling/parameters.py:Parameter.size
│               └── invokes ── astropy/utils/shapes.py:ShapedLikeNDArray.size
├── contains ── astropy/modeling/core.py:CompoundModel._recursive_lookup
│               └── invokes ── astropy/table/table.py:TableColumns.isinstance
├── contains ── astropy/modeling/core.py:CompoundModel.inputs_map
├── contains ── astropy/modeling/core.py:CompoundModel._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._map_parameters
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Gaussian1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Gaussian2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Shift._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Scale._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Multiply._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Sersic1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:_Trigonometric1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:_InverseTrigonometric1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Linear1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Planar2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Lorentz1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Voigt1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Const1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Const2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Ellipse2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Disk2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Ring2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Box1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Box2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Trapezoid1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:TrapezoidDisk2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:RickerWavelet1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:RickerWavelet2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:AiryDisk2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Moffat1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Moffat2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Sersic2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:KingProjectedAnalytic1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Logarithmic1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/functional_models.py:Exponential1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/physical_models.py:BlackBody._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/physical_models.py:Drude1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/physical_models.py:Plummer1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/physical_models.py:NFW._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/polynomial.py:Polynomial1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/polynomial.py:Polynomial2D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/powerlaws.py:PowerLaw1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/powerlaws.py:BrokenPowerLaw1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/powerlaws.py:SmoothlyBrokenPowerLaw1D._parameter_units_for_data_units
│               ├── invokes ── astropy/modeling/powerlaws.py:ExponentialCutoffPowerLaw1D._parameter_units_for_data_units
│               └── invokes ── astropy/modeling/powerlaws.py:LogParabola1D._parameter_units_for_data_units
├── contains ── astropy/modeling/core.py:CompoundModel.input_units
│               ├── invokes ── astropy/units/structured.py:StructuredUnit.items
│               └── invokes ── astropy/modeling/core.py:CompoundModel.inputs_map
├── contains ── astropy/modeling/core.py:CompoundModel.input_units_equivalencies
│               ├── invokes ── astropy/units/structured.py:StructuredUnit.items
│               └── invokes ── astropy/modeling/core.py:CompoundModel.inputs_map
├── contains ── astropy/modeling/core.py:CompoundModel.input_units_allow_dimensionless
│               ├── invokes ── astropy/units/structured.py:StructuredUnit.items
│               └── invokes ── astropy/modeling/core.py:CompoundModel.inputs_map
├── contains ── astropy/modeling/core.py:CompoundModel.input_units_strict
│               ├── invokes ── astropy/units/structured.py:StructuredUnit.items
│               └── invokes ── astropy/modeling/core.py:CompoundModel.inputs_map
├── contains ── astropy/modeling/core.py:CompoundModel.return_units
│               ├── invokes ── astropy/modeling/core.py:CompoundModel.outputs_map
│               └── invokes ── astropy/units/structured.py:StructuredUnit.items
├── contains ── astropy/modeling/core.py:CompoundModel.outputs_map
├── contains ── astropy/modeling/core.py:CompoundModel.has_user_bounding_box
├── contains ── astropy/modeling/core.py:CompoundModel.render
│               ├── invokes ── astropy/nddata/utils.py:add_array
│               ├── invokes ── astropy/modeling/core.py:Model.get_bounding_box
│               ├── invokes ── astropy/convolution/core.py:Kernel.array
│               ├── invokes ── astropy/convolution/kernels.py:CustomKernel.array
│               ├── invokes ── astropy/io/fits/column.py:Column.array
│               ├── invokes ── astropy/nddata/nduncertainty.py:NDUncertainty.array
│               ├── invokes ── astropy/wcs/wcs.py:WCS.slice
│               ├── invokes ── astropy/nddata/utils.py:extract_array
│               └── invokes ── astropy/units/quantity.py:Quantity.mean
├── contains ── astropy/modeling/core.py:CompoundModel.replace_submodel
│               ├── invokes ── astropy/modeling/core.py:_get_submodel_path
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.pop
│               ├── invokes ── astropy/io/fits/header.py:Header.pop
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.pop
│               ├── invokes ── astropy/modeling/core.py:CompoundModel
│               ├── invokes ── astropy/modeling/core.py:CompoundModel.traverse_postorder
│               ├── invokes ── astropy/units/quantity.py:QuantityIterator.copy
│               ├── invokes ── astropy/modeling/core.py:Model.copy
│               ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.copy
│               ├── invokes ── astropy/modeling/parameters.py:Parameter.copy
│               ├── invokes ── astropy/utils/shapes.py:NDArrayShapeMethods.copy
│               └── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.copy
├── contains ── astropy/modeling/core.py:CompoundModel._set_sub_models_and_parameter_units
│               ├── invokes ── astropy/units/quantity.py:SpecificTypeQuantity._set_unit
│               ├── invokes ── astropy/units/quantity.py:Quantity._set_unit
│               ├── invokes ── astropy/modeling/parameters.py:Parameter._set_unit
│               ├── invokes ── astropy/units/function/core.py:FunctionQuantity._set_unit
│               └── invokes ── astropy/modeling/core.py:CompoundModel
├── contains ── astropy/modeling/core.py:CompoundModel.without_units_for_data
│               ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._set_sub_models_and_parameter_units
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.update
│               ├── invokes ── astropy/io/fits/convenience.py:update
│               ├── invokes ── astropy/io/fits/header.py:Header.update
│               ├── invokes ── astropy/io/fits/hdu/table.py:_TableBaseHDU.update
│               ├── invokes ── astropy/io/fits/scripts/fitscheck.py:update
│               ├── invokes ── astropy/table/table.py:Table.update
│               ├── invokes ── astropy/utils/console.py:ProgressBar.update
│               ├── invokes ── astropy/utils/console.py:Spinner.update
│               ├── invokes ── astropy/utils/console.py:ProgressBarOrSpinner.update
│               ├── invokes ── astropy/modeling/core.py:Model.without_units_for_data
│               ├── invokes ── astropy/modeling/core.py:Model.output_units
│               ├── invokes ── astropy/units/quantity.py:QuantityIterator.copy
│               ├── invokes ── astropy/modeling/core.py:Model.copy
│               ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.copy
│               ├── invokes ── astropy/modeling/parameters.py:Parameter.copy
│               ├── invokes ── astropy/utils/shapes.py:NDArrayShapeMethods.copy
│               └── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.copy
├── contains ── astropy/modeling/core.py:CompoundModel.with_units_from_data
│               ├── invokes ── astropy/modeling/core.py:CompoundModel._set_sub_models_and_parameter_units
│               ├── invokes ── astropy/extern/configobj/configobj.py:Section.pop
│               ├── invokes ── astropy/io/fits/header.py:Header.pop
│               ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.pop
│               ├── invokes ── astropy/units/quantity.py:QuantityIterator.copy
│               ├── invokes ── astropy/modeling/core.py:Model.copy
│               ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.copy
│               ├── invokes ── astropy/modeling/parameters.py:Parameter.copy
│               ├── invokes ── astropy/utils/shapes.py:NDArrayShapeMethods.copy
│               ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.copy
│               └── invokes ── astropy/modeling/core.py:Model.with_units_from_data
├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.fix_inputs
│              ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.fix_inputs
│              ├── invokes ── astropy/modeling/bounding_box.py:_BoundingDomain.fix_inputs
│              ├── invokes ── astropy/table/table.py:TableColumns.isinstance
│              ├── invokes ── astropy/extern/configobj/configobj.py:Section.keys
│              ├── invokes ── astropy/io/fits/header.py:Header.keys
│              ├── invokes ── astropy/table/groups.py:ColumnGroups.keys
│              ├── invokes ── astropy/table/groups.py:TableGroups.keys
│              ├── invokes ── astropy/table/row.py:Row.keys
│              ├── invokes ── astropy/table/table.py:Table.keys
│              ├── invokes ── astropy/units/structured.py:StructuredUnit.keys
│              ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.validate
│              ├── invokes ── astropy/modeling/bounding_box.py:_Interval.validate
│              ├── invokes ── astropy/modeling/bounding_box.py:_SelectorArguments.validate
│              ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.validate
│              ├── invokes ── astropy/modeling/bounding_box.py:_SelectorArgument.validate
│              ├── invokes ── astropy/extern/configobj/configobj.py:Section.pop
│              ├── invokes ── astropy/io/fits/header.py:Header.pop
│              ├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.pop
│              ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox._fix_input_selector_arg
│              ├── invokes ── astropy/utils/shapes.py:NDArrayShapeMethods.copy
│              ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.copy
│              ├── invokes ── astropy/modeling/bounding_box.py:_Interval.copy
│              ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.copy
│              ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox._fix_input_bbox_arg
│              ├── invokes ── astropy/modeling/bounding_box.py:_SelectorArguments.is_argument
│              └── invokes ── astropy/modeling/bounding_box.py:_SelectorArgument.is_argument
├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.fix_inputs
├── invokes ── astropy/modeling/core.py:fix_inputs
│              ├── invokes ── astropy/modeling/bounding_box.py:ModelBoundingBox.validate
│              ├── invokes ── astropy/modeling/bounding_box.py:CompoundBoundingBox.validate
│              ├── invokes ── astropy/modeling/parameters.py:Parameter.validate
│              ├── invokes ── astropy/modeling/core.py:CompoundModel
│              ├── invokes ── astropy/modeling/bounding_box.py:_SelectorArguments.get_fixed_values
│              └── invokes ── astropy/units/structured.py:StructuredUnit.keys
├── invokes ── astropy/table/table.py:TableColumns.isinstance
├── invokes ── astropy/io/votable/tree.py:Values.type
├── invokes ── astropy/io/votable/tree.py:Field.type
├── invokes ── astropy/io/votable/tree.py:Resource.type
├── invokes ── astropy/io/votable/tree.py:Table.format
├── invokes ── astropy/table/column.py:BaseColumn.format
├── invokes ── astropy/time/core.py:TimeBase.format
├── invokes ── astropy/visualization/time.py:time_support.MplTimeConverter.format
├── invokes ── astropy/visualization/wcsaxes/formatter_locator.py:AngleFormatterLocator.format
├── invokes ── astropy/visualization/wcsaxes/formatter_locator.py:ScalarFormatterLocator.format
├── invokes ── astropy/table/bst.py:BST.sort
├── invokes ── astropy/table/index.py:Index.sort
├── invokes ── astropy/table/index.py:SlicedIndex.sort
├── invokes ── astropy/table/meta.py:ColumnOrderList.sort
├── invokes ── astropy/table/soco.py:SCEngine.sort
├── invokes ── astropy/table/sorted_array.py:SortedArray.sort
├── invokes ── astropy/table/table.py:Table.sort
├── invokes ── astropy/time/core.py:TimeBase.sort
├── invokes ── astropy/utils/masked/core.py:MaskedNDArray.sort
├── invokes ── astropy/visualization/wcsaxes/ticklabels.py:TickLabels.sort
├── invokes ── astropy/io/fits/convenience.py:append
├── invokes ── astropy/io/fits/header.py:Header.append
├── invokes ── astropy/io/fits/hdu/compressed.py:CompImageHeader.append
├── invokes ── astropy/io/fits/hdu/hdulist.py:HDUList.append
├── invokes ── astropy/units/quantity_helper/function_helpers.py:append
├── invokes ── astropy/utils/collections.py:HomogeneousList.append
├── invokes ── astropy/utils/masked/function_helpers.py:append
├── invokes ── astropy/units/quantity.py:QuantityIterator.index
├── invokes ── astropy/modeling/core.py:ModelDefinitionError
├── invokes ── astropy/table/table.py:Table.reverse
│              ├── invokes ── astropy/table/table.py:Table.reverse
│              ├── invokes ── astropy/table/row.py:Row.values
│              ├── invokes ── astropy/config/configuration.py:ConfigNamespace.values
│              ├── invokes ── astropy/table/table.py:Table.values
│              └── invokes ── astropy/units/structured.py:StructuredUnit.values
├── invokes ── astropy/modeling/utils.py:combine_labels
│              ├── invokes ── astropy/__init__.py:base_constants_version.set
│              ├── invokes ── astropy/config/configuration.py:ConfigItem.set
│              ├── invokes ── astropy/io/fits/header.py:Header.set
│              ├── invokes ── astropy/table/bst.py:Node.set
│              ├── invokes ── astropy/table/table.py:PprintIncludeExclude.set
│              ├── invokes ── astropy/utils/decorators.py:deprecated_attribute.set
│              └── invokes ── astropy/utils/state.py:ScienceState.set
├── invokes ── astropy/units/structured.py:StructuredUnit.keys
└── inherits ── astropy/modeling/core.py:Model
                ├── contains ── astropy/modeling/core.py:Model.__init_subclass__
                ├── contains ── astropy/modeling/core.py:Model._default_inputs_outputs
                ├── invokes ── astropy/modeling/core.py:Model._default_inputs_outputs
                ├── contains ── astropy/modeling/core.py:Model._initialize_setters
                ├── invokes ── astropy/modeling/core.py:Model._initialize_setters
                ├── contains ── astropy/modeling/core.py:Model.inputs
                ├── contains ── astropy/modeling/core.py:Model.outputs
                ├── contains ── astropy/modeling/core.py:Model.n_inputs
                ├── contains ── astropy/modeling/core.py:Model.n_outputs
                ├── contains ── astropy/modeling/core.py:Model._calculate_separability_matrix
                ├── contains ── astropy/modeling/core.py:Model._initialize_unit_support
                ├── invokes ── astropy/modeling/core.py:Model._initialize_unit_support
                ├── contains ── astropy/modeling/core.py:Model.input_units_strict
                ├── contains ── astropy/modeling/core.py:Model.input_units_allow_dimensionless
                ├── contains ── astropy/modeling/core.py:Model.uses_quantity
                ├── contains ── astropy/modeling/core.py:Model.__repr__
                ├── contains ── astropy/modeling/core.py:Model.__str__
                ├── contains ── astropy/modeling/core.py:Model.__len__
                ├── contains ── astropy/modeling/core.py:Model._strip_ones
                ├── contains ── astropy/modeling/core.py:Model.__setattr__
                ├── contains ── astropy/modeling/core.py:Model._pre_evaluate
                ├── contains ── astropy/modeling/core.py:Model.get_bounding_box
                ├── contains ── astropy/modeling/core.py:Model._argnames
                ├── contains ── astropy/modeling/core.py:Model._validate_input_shape
                ├── contains ── astropy/modeling/core.py:Model._validate_input_shapes
                ├── contains ── astropy/modeling/core.py:Model.input_shape
                ├── contains ── astropy/modeling/core.py:Model._generic_evaluate
                ├── contains ── astropy/modeling/core.py:Model._post_evaluate
                ├── contains ── astropy/modeling/core.py:Model.bbox_with_units
                ├── contains ── astropy/modeling/core.py:Model.__call__
                ├── contains ── astropy/modeling/core.py:Model._get_renamed_inputs_as_positional
                ├── contains ── astropy/modeling/core.py:Model.name
                ├── contains ── astropy/modeling/core.py:Model.model_set_axis
                ├── contains ── astropy/modeling/core.py:Model.param_sets
                ├── contains ── astropy/modeling/core.py:Model.parameters
                ├── contains ── astropy/modeling/core.py:Model.sync_constraints
                ├── contains ── astropy/modeling/core.py:Model.fixed
                ├── contains ── astropy/modeling/core.py:Model.bounds
                ├── contains ── astropy/modeling/core.py:Model.tied
                ├── contains ── astropy/modeling/core.py:Model.eqcons
                ├── contains ── astropy/modeling/core.py:Model.ineqcons
                ├── contains ── astropy/modeling/core.py:Model.has_inverse
                ├── contains ── astropy/modeling/core.py:Model.inverse
                ├── contains ── astropy/modeling/core.py:Model.has_user_inverse
                ├── contains ── astropy/modeling/core.py:Model.bounding_box
                ├── contains ── astropy/modeling/core.py:Model.set_slice_args
                ├── contains ── astropy/modeling/core.py:Model.has_user_bounding_box
                ├── contains ── astropy/modeling/core.py:Model.cov_matrix
                ├── contains ── astropy/modeling/core.py:Model.stds
                ├── contains ── astropy/modeling/core.py:Model.separable
                ├── contains ── astropy/modeling/core.py:Model.without_units_for_data
                ├── contains ── astropy/modeling/core.py:Model.output_units
                ├── contains ── astropy/modeling/core.py:Model.strip_units_from_tree
                ├── contains ── astropy/modeling/core.py:Model.with_units_from_data
                ├── contains ── astropy/modeling/core.py:Model._has_units
                ├── contains ── astropy/modeling/core.py:Model._supports_unit_fitting
                ├── contains ── astropy/modeling/core.py:Model.evaluate
                ├── contains ── astropy/modeling/core.py:Model.sum_of_implicit_terms
                ├── contains ── astropy/modeling/core.py:Model.render
                ├── contains ── astropy/modeling/core.py:Model.input_units
                ├── contains ── astropy/modeling/core.py:Model.return_units
                ├── contains ── astropy/modeling/core.py:Model._prepare_inputs_single_model
                ├── contains ── astropy/modeling/core.py:Model._remove_axes_from_shape
                ├── contains ── astropy/modeling/core.py:Model._prepare_inputs_model_set
                ├── contains ── astropy/modeling/core.py:Model.prepare_inputs
                ├── contains ── astropy/modeling/core.py:Model._validate_input_units
                ├── contains ── astropy/modeling/core.py:Model._process_output_units
                ├── contains ── astropy/modeling/core.py:Model._prepare_output_single_model
                ├── contains ── astropy/modeling/core.py:Model._prepare_outputs_single_model
                ├── contains ── astropy/modeling/core.py:Model._prepare_outputs_model_set
                ├── contains ── astropy/modeling/core.py:Model.prepare_outputs
                ├── contains ── astropy/modeling/core.py:Model.copy
                ├── contains ── astropy/modeling/core.py:Model.deepcopy
                ├── invokes ── astropy/modeling/core.py:Model.deepcopy
                ├── contains ── astropy/modeling/core.py:Model.rename
                ├── contains ── astropy/modeling/core.py:Model.coerce_units
                ├── contains ── astropy/modeling/core.py:Model.n_submodels
                ├── contains ── astropy/modeling/core.py:Model._initialize_constraints
                ├── invokes ── astropy/modeling/core.py:Model._initialize_constraints
                ├── contains ── astropy/modeling/core.py:Model._initialize_parameters
                ├── invokes ── astropy/modeling/core.py:Model._initialize_parameters
                ├── contains ── astropy/modeling/core.py:Model._initialize_parameter_value
                ├── contains ── astropy/modeling/core.py:Model._initialize_slices
                ├── invokes ── astropy/modeling/core.py:Model._initialize_slices
                ├── contains ── astropy/modeling/core.py:Model._parameters_to_array
                ├── contains ── astropy/modeling/core.py:Model._array_to_parameters
                ├── contains ── astropy/modeling/core.py:Model._check_param_broadcast
                ├── contains ── astropy/modeling/core.py:Model._param_sets
                ├── contains ── astropy/modeling/core.py:Model._format_repr
                ├── contains ── astropy/modeling/core.py:Model._format_str
                ├── invokes ── astropy/units/structured.py:StructuredUnit.items
                ├── invokes ── astropy/modeling/core.py:_ModelMeta._handle_special_methods.__init__
                └── invokes ── astropy/modeling/core.py:CompoundModel._initialize_slices
</pre></div></div></div><div class="message-block tool-response"><div class="message-header">🔧 Tool Response: explore_tree_structure</div><div class="message-content"><div class="observation-preview">OBSERVATION:
astropy/modeling/core.py:Model._calculate_separability_matrix
├── contains-by ── astropy/modeling/core.py:Model...</div><div class="collapsible" onclick="toggleContent('tool-28')"><span>View full output (2172 chars)</span><button id="btn-tool-28" class="toggle-btn">Show</button></div><div id="tool-28" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">OBSERVATION:
astropy/modeling/core.py:Model._calculate_separability_matrix
├── contains-by ── astropy/modeling/core.py:Model
│                  ├── contains-by ── astropy/modeling/core.py
│                  ├── imports-by ── astropy/io/misc/asdf/tags/transform/compound.py
│                  ├── imports-by ── astropy/modeling/mappings.py
│                  ├── imports-by ── astropy/modeling/math_functions.py
│                  ├── imports-by ── astropy/modeling/polynomial.py
│                  ├── imports-by ── astropy/modeling/projections.py
│                  ├── imports-by ── astropy/modeling/rotations.py
│                  ├── imports-by ── astropy/modeling/separable.py
│                  ├── imports-by ── astropy/modeling/tabular.py
│                  ├── inherits-by ── astropy/modeling/core.py:FittableModel
│                  ├── inherits-by ── astropy/modeling/core.py:CompoundModel
│                  ├── inherits-by ── astropy/modeling/mappings.py:UnitsMapping
│                  ├── inherits-by ── astropy/modeling/math_functions.py:_NPUfuncModel
│                  ├── inherits-by ── astropy/modeling/polynomial.py:SIP
│                  ├── inherits-by ── astropy/modeling/polynomial.py:InverseSIP
│                  ├── inherits-by ── astropy/modeling/projections.py:Projection
│                  ├── inherits-by ── astropy/modeling/projections.py:AffineTransformation2D
│                  ├── inherits-by ── astropy/modeling/rotations.py:RotationSequence3D
│                  ├── inherits-by ── astropy/modeling/rotations.py:EulerAngleRotation
│                  ├── inherits-by ── astropy/modeling/rotations.py:_SkyRotation
│                  ├── inherits-by ── astropy/modeling/rotations.py:Rotation2D
│                  └── inherits-by ── astropy/modeling/tabular.py:_Tabular
└── invokes-by ── astropy/modeling/separable.py:_separable
                  ├── contains-by ── astropy/modeling/separable.py
                  ├── invokes-by ── astropy/modeling/separable.py:is_separable
                  ├── invokes-by ── astropy/modeling/separable.py:separability_matrix
                  └── invokes-by ── astropy/modeling/separable.py:_separable
</pre></div></div></div><div class="message-block tool-call"><div class="message-header">🤖 Assistant → Tool Calls (1 calls)</div><div class="message-content"><div class="collapsible" onclick="toggleContent('call-29-0')"><span>📞 <code>finish</code></span><button id="btn-call-29-0" class="toggle-btn">Show</button></div><div id="call-29-0" class="collapsed-content"><p><strong>Call ID:</strong> <code>call_73j4e3eIg3C7ef9yK22jFmvQ</code></p><p><strong>Arguments:</strong></p><div class="tool-args">{}</div></div></div></div></div>
            <div class="raw-output-section">
                <h2>Raw LLM Output</h2>
                <div class="collapsible" onclick="toggleContent('raw-output')">
                    <span>Show raw output (3172 chars)</span>
                    <button id="btn-raw-output" class="toggle-btn">Show</button>
                </div>
                <div id="raw-output" class="collapsed-content">
                    <pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px;">
The located files and their components contain the essential information for addressing the issue with the separability matrix of nested CompoundModels.

1. astropy/modeling/separable.py:
   - separability_matrix function (lines 66-102): Computes correlation between outputs and inputs.
   - _separable function (lines 290-312): Recursively calculates separability of outputs, especially for CompoundModels by combining the separability of left and right submodels using operator functions.
   - _operators dictionary (line 316): Maps model operators to functions that compute relationships in the separability matrix. The &#39;&amp;&#39; operator (compound model stacking) uses the _cstack function, which might hold clues about nesting behavior.

2. astropy/modeling/core.py:
   - CompoundModel class (lines 2894-4000): The core class for compound models with key methods such as _apply_operators_to_value_lists which performs operations on left and right model values, and evaluate, which evaluates the compound model. The internal logic of how submodels combine and how inputs/outputs are managed is critical here.
   - Model._calculate_separability_matrix (around line 807): A placeholder method to be overridden by more complex models if needed but currently returns NotImplemented.
   - Model.separable property (around line 1532): Returns if the model is separable with default NotImplementedError for unsupported models.

The separability check uses the _separable function in separable.py which in turn uses the _operators dictionary to calculate how the separability matrix for compound models is computed by combining results from left and right submodels. The &#39;&amp;&#39; operation uses _cstack, which stacks matrices horizontally, which might not handle nesting correctly as per the bug description.

The CompoundModel class is central to this issue, as it implements the behavior of combining models and their evaluations. The method _apply_operators_to_value_lists and evaluate could have influence on the separability matrix if the nested structure is not handled properly when generating the matrix.

No direct bug fix or modification place is evident yet, but the key area to investigate would be:
- How _separable handles nesting and the use of _operators and _cstack for &#39;&amp;&#39; inside separable.py.
- The corresponding behavior and attributes in CompoundModel class in core.py to see if it properly supports nested compounds in separability computations.
- Potentially overriding or improving _calculate_separability_matrix in CompoundModel or handling nested cases specifically.

Therefore, the locations listed are comprehensive and are the correct places to address the issue.

Final locations requiring modification or investigation for the bug:

```
astropy/modeling/separable.py
line: 66
function: separability_matrix
line: 290
function: _separable
line: 316
variable: _operators

astropy/modeling/core.py
line: 2894
class: CompoundModel
line: 3080
function: _apply_operators_to_value_lists (inside CompoundModel)
line: 807
function: _calculate_separability_matrix (inside Model)
line: 1532
function: separable (property inside Model)
```

I will now finish the task.</pre>
                </div>
            </div>
            
            <div class="tools-section">
                <h2>Available Tools (4 tools)</h2>
                <div class="collapsible" onclick="toggleContent('tools-list')">
                    <span>Click to view available tools</span>
                    <button id="btn-tools-list" class="toggle-btn">Show</button>
                </div>
                <div id="tools-list" class="collapsed-content">
            
                    <div class="tool-definition">
                        <h4>🔧 finish</h4>
                        <div style="margin-left: 20px;">
                            <p><strong>Description:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: inherit; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 13px;">Finish the interaction when the task is complete OR if the assistant cannot proceed further with the task.</pre>
                            
                            <p><strong>Parameters:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">No parameters</pre>
                        </div>
                    </div>
                    <hr style="margin: 20px 0; border: none; border-top: 1px solid #e0e0e0;">
                    <div class="tool-definition">
                        <h4>🔧 search_code_snippets</h4>
                        <div style="margin-left: 20px;">
                            <p><strong>Description:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: inherit; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 13px;">Searches the codebase to retrieve relevant code snippets based on given queries(terms or line numbers).
** Note:
- Either `search_terms` or `line_nums` must be provided to perform a search.
- If `search_terms` are provided, it searches for code snippets based on each term:
- If `line_nums` is provided, it searches for code snippets around the specified lines within the file defined by `file_path_or_pattern`.

** Example Usage:
# Search for code content contain keyword `order`, `bill`
search_code_snippets(search_terms=[&quot;order&quot;, &quot;bill&quot;])

# Search for a class
search_code_snippets(search_terms=[&quot;MyClass&quot;])

# Search for context around specific lines (10 and 15) within a file
search_code_snippets(line_nums=[10, 15], file_path_or_pattern=&#39;src/example.py&#39;)</pre>
                            
                            <p><strong>Parameters:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">{
  &quot;type&quot;: &quot;object&quot;,

  &quot;properties&quot;: {
    &quot;search_terms&quot;: {
      &quot;type&quot;: &quot;array&quot;,

      &quot;items&quot;: {
        &quot;type&quot;: &quot;string&quot;
      },
      &quot;description&quot;: &quot;A list of names, keywords, or code snippets to search for within the codebase. This can include potential function names, class names, or general code fragments. Either `search_terms` or `line_nums` must be provided to perform a search.&quot;
    },
    &quot;line_nums&quot;: {
      &quot;type&quot;: &quot;array&quot;,

      &quot;items&quot;: {
        &quot;type&quot;: &quot;integer&quot;
      },
      &quot;description&quot;: &quot;Specific line numbers to locate code snippets within a specified file. Must be used alongside a valid `file_path_or_pattern`. Either `line_nums` or `search_terms` must be provided to perform a search.&quot;
    },
    &quot;file_path_or_pattern&quot;: {
      &quot;type&quot;: &quot;string&quot;,

      &quot;description&quot;: &quot;A glob pattern or specific file path used to filter search results to particular files or directories. Defaults to \&quot;**/*.py\&quot;,
 meaning all Python files are searched by default. If `line_nums` are provided, this must specify a specific file path.&quot;,

      &quot;default&quot;: &quot;**/*.py&quot;
    }
  },
  &quot;required&quot;: []
}</pre>
                        </div>
                    </div>
                    <hr style="margin: 20px 0; border: none; border-top: 1px solid #e0e0e0;">
                    <div class="tool-definition">
                        <h4>🔧 get_entity_contents</h4>
                        <div style="margin-left: 20px;">
                            <p><strong>Description:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: inherit; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 13px;">Searches the codebase to retrieve the complete implementations of specified entities based on the provided entity names. 
The tool can handle specific entity queries such as function names, class names, or file paths.

**Usage Example:**
# Search for a specific function implementation
get_entity_contents([&#39;src/my_file.py:MyClass.func_name&#39;])

# Search for a file&#39;s complete content
get_entity_contents([&#39;src/my_file.py&#39;])

**Entity Name Format:**
- To specify a function or class, use the format: `file_path:QualifiedName`
  (e.g., &#39;src/helpers/math_helpers.py:MathUtils.calculate_sum&#39;).
- To search for a file&#39;s content, use only the file path (e.g., &#39;src/my_file.py&#39;).</pre>
                            
                            <p><strong>Parameters:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">{
  &quot;type&quot;: &quot;object&quot;,

  &quot;properties&quot;: {
    &quot;entity_names&quot;: {
      &quot;type&quot;: &quot;array&quot;,

      &quot;items&quot;: {
        &quot;type&quot;: &quot;string&quot;
      },
      &quot;description&quot;: &quot;A list of entity names to query. Each entity name can represent a function, class, or file. For functions or classes, the format should be &#39;file_path:QualifiedName&#39; (e.g., &#39;src/helpers/math_helpers.py:MathUtils.calculate_sum&#39;). For files, use just the file path (e.g., &#39;src/my_file.py&#39;).&quot;
    }
  },
  &quot;required&quot;: [
    &quot;entity_names&quot;
  ]
}</pre>
                        </div>
                    </div>
                    <hr style="margin: 20px 0; border: none; border-top: 1px solid #e0e0e0;">
                    <div class="tool-definition">
                        <h4>🔧 explore_tree_structure</h4>
                        <div style="margin-left: 20px;">
                            <p><strong>Description:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: inherit; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 13px;">A unified tool that traverses a pre-built code graph to retrieve dependency structure around specified entities, 
with options to explore upstream or downstream, and control traversal depth and filters for entity and dependency types.

Example Usage:
1. Exploring Downstream Dependencies:
    ```
    explore_tree_structure(
        start_entities=[&#39;src/module_a.py:ClassA&#39;],
        direction=&#39;downstream&#39;,
        traversal_depth=2,
        dependency_type_filter=[&#39;invokes&#39;, &#39;imports&#39;]
    )
    ```
2. Exploring the repository structure from the root directory (/) up to two levels deep:
    ```
    explore_tree_structure(
      start_entities=[&#39;/&#39;],
      traversal_depth=2,
      dependency_type_filter=[&#39;contains&#39;]
    )
    ```
3. Generate Class Diagrams:
    ```
    explore_tree_structure(
        start_entities=selected_entity_ids,
        direction=&#39;both&#39;,
        traverse_depth=-1,
        dependency_type_filter=[&#39;inherits&#39;]
    )
    ```</pre>
                            
                            <p><strong>Parameters:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">{
  &quot;type&quot;: &quot;object&quot;,

  &quot;properties&quot;: {
    &quot;start_entities&quot;: {
      &quot;description&quot;: &quot;List of entities (e.g., class, function, file, or directory paths) to begin the search from.\nEntities representing classes or functions must be formatted as \&quot;file_path:QualifiedName\&quot; (e.g., `interface/C.py:C.method_a.inner_func`).\nFor files or directories, provide only the file or directory path (e.g., `src/module_a.py` or `src/`).&quot;,

      &quot;type&quot;: &quot;array&quot;,

      &quot;items&quot;: {
        &quot;type&quot;: &quot;string&quot;
      }
    },
    &quot;direction&quot;: {
      &quot;description&quot;: &quot;Direction of traversal in the code graph; allowed options are: `upstream`, `downstream`, `both`.\n- &#39;upstream&#39;: Traversal to explore dependencies that the specified entities rely on (how they depend on others).\n- &#39;downstream&#39;: Traversal to explore the effects or interactions of the specified entities on others (how others depend on them).\n- &#39;both&#39;: Traversal on both direction.&quot;,

      &quot;type&quot;: &quot;string&quot;,

      &quot;enum&quot;: [
        &quot;upstream&quot;,

        &quot;downstream&quot;,

        &quot;both&quot;
      ],
      &quot;default&quot;: &quot;downstream&quot;
    },
    &quot;traversal_depth&quot;: {
      &quot;description&quot;: &quot;Maximum depth of traversal. A value of -1 indicates unlimited depth (subject to a maximum limit).Must be either `-1` or a non-negative integer (\u2265 0).&quot;,

      &quot;type&quot;: &quot;integer&quot;,

      &quot;default&quot;: 2
    },
    &quot;entity_type_filter&quot;: {
      &quot;description&quot;: &quot;List of entity types (e.g., &#39;class&#39;, &#39;function&#39;, &#39;file&#39;, &#39;directory&#39;) to include in the traversal. If None, all entity types are included.&quot;,

      &quot;type&quot;: [
        &quot;array&quot;,

        &quot;null&quot;
      ],
      &quot;items&quot;: {
        &quot;type&quot;: &quot;string&quot;
      },
      &quot;default&quot;: null
    },
    &quot;dependency_type_filter&quot;: {
      &quot;description&quot;: &quot;List of dependency types (e.g., &#39;contains&#39;, &#39;imports&#39;, &#39;invokes&#39;, &#39;inherits&#39;) to include in the traversal. If None, all dependency types are included.&quot;,

      &quot;type&quot;: [
        &quot;array&quot;,

        &quot;null&quot;
      ],
      &quot;items&quot;: {
        &quot;type&quot;: &quot;string&quot;
      },
      &quot;default&quot;: null
    }
  },
  &quot;required&quot;: [
    &quot;start_entities&quot;
  ]
}</pre>
                        </div>
                    </div>
                    
                </div>
            </div>
            </div>
            </body>
            </html>
            