#!/bin/bash
set -e

image_name="$1"
tag="$2"
registry_name="$3"
workspace="$4"
resource_group="$5"
env_name="$6"
subscription="$7"
sudo bash $(dirname $0)/build_environment.sh "$image_name" "$tag" "$registry_name" "$resource_group" "$subscription"
sudo az ml environment create -i "$registry_name.azurecr.io/$image_name:$tag" -n "$env_name" -w "$workspace" -g "$resource_group" --description "Environment for OpenRLHF training and inference."
