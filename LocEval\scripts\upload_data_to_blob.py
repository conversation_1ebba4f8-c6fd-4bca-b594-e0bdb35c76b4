# One-time upload script
from azure.ai.ml import M<PERSON><PERSON>
from azure.ai.ml.entities import Data
from azure.ai.ml.constants import AssetTypes
from azure.identity import DefaultAzureCredential

credential = DefaultAzureCredential()
# TODO: Input corresponding id and resource group, workspace name here
ml_client = MLClient(
    subscription_id="d0c05057-7972-46ff-9bcf-3c932250155e",
    resource_group_name="SingularityH100",
    workspace_name="H100CentralUS",
    credential=credential,
)

# Upload entire dataset folder (includes both graph_index_v2.3 and BM25_index subfolders)
print("Uploading SWE-bench_Lite dataset with all indexes...")
# TODO: specify folder and corresponding metadata to upload here
swe_bench_lite_data = Data(
    path="results/czSWEBenchLite",  # This uploads the whole folder
    type=AssetTypes.URI_FOLDER,
    name="locagent_swe_bench_lite_gpt_results",
    description="SWE-bench Lite loc agent results using GPT-4-1-mini and GPT-4-1",
    tags={
        "project": "LocAgent",
        "dataset": "SWE-bench_Lite-results",
        "contains": "GPT-4-1-mini, GPT-4-1",
    },
)
asset = ml_client.data.create_or_update(swe_bench_lite_data)

print(f"Uploaded: {asset.name}")
print(f"Version: {asset.version}")
print(f"ID: {asset.id}")

# Verify the upload
print("\nVerifying upload...")
try:
    # Retrieve the data asset to confirm it exists
    retrieved_asset = ml_client.data.get(name=asset.name, version=asset.version)
    print(f"✅ Data asset confirmed: {retrieved_asset.name}")
    print(f"   Description: {retrieved_asset.description}")
    print(f"   Path: {retrieved_asset.path}")
    print(f"   Tags: {retrieved_asset.tags}")

    # List all versions of this data asset
    versions = ml_client.data.list(name=asset.name)
    print(f"   Available versions: {[v.version for v in versions]}")

except Exception as e:
    print(f"❌ Error retrieving data asset: {e}")

# When you add more datasets later, just repeat:
# For example, when you get SWE-bench_Full:
"""
swe_bench_full_data = Data(
    path="./index_data/SWE-bench_Full",
    type=AssetTypes.URI_FOLDER,
    name="locagent_swe_bench_full",
    description="SWE-bench Full dataset with graph_index_v2.3 and BM25_index subfolders"
)
"""
