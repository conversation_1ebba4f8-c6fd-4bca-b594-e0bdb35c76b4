@echo off
REM Batch file to run the cost and latency analysis script
REM This will try different Python commands to find the right one

echo Cost and Latency Analysis Tool
echo ==============================
echo.

REM Check if a file was provided as argument
if "%1"=="" (
    echo Usage: run_analysis.bat path\to\loc_trajs.jsonl
    echo.
    echo Example: run_analysis.bat "LocEval\example_results\experiment1\gpt-4.1-mini-czlll-swebenchlite\loc_trajs.jsonl"
    echo.
    pause
    exit /b 1
)

REM Try different Python commands
echo Trying to find Python installation...

python --version >nul 2>&1
if %errorlevel%==0 (
    echo Found: python
    python extract_cost_latency.py "%1" %2 %3 %4 %5
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel%==0 (
    echo Found: python3
    python3 extract_cost_latency.py "%1" %2 %3 %4 %5
    goto :end
)

py --version >nul 2>&1
if %errorlevel%==0 (
    echo Found: py
    py extract_cost_latency.py "%1" %2 %3 %4 %5
    goto :end
)

echo ERROR: Python not found!
echo.
echo Please install Python from: https://www.python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation.
echo.
pause
exit /b 1

:end
echo.
echo Analysis complete!
pause
