{"cells": [{"cell_type": "code", "execution_count": 11, "id": "08b49818", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"id\": \"chatcmpl-BmnNdXK1YYJkcbUOlWO4QQznuHCnl\",\n", "  \"choices\": [\n", "    {\n", "      \"finish_reason\": \"stop\",\n", "      \"index\": 0,\n", "      \"logprobs\": null,\n", "      \"message\": {\n", "        \"content\": \"9.11 is nine and eleven-hundredths (9.11 = 9.11), while 9.9 is nine and nine-tenths, which is the same as 9.90.\\n\\nWhen you line up the decimals to the same number of places:\\n\\n9.11  \\n9.90\\n\\nit’s clear that 9.90 > 9.11. Therefore, 9.9 is larger than 9.11.\",\n", "        \"refusal\": null,\n", "        \"role\": \"assistant\",\n", "        \"annotations\": []\n", "      },\n", "      \"content_filter_results\": {\n", "        \"hate\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"protected_material_code\": {\n", "          \"filtered\": false,\n", "          \"detected\": false\n", "        },\n", "        \"protected_material_text\": {\n", "          \"filtered\": false,\n", "          \"detected\": false\n", "        },\n", "        \"self_harm\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"sexual\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"violence\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        }\n", "      }\n", "    }\n", "  ],\n", "  \"created\": 1750969261,\n", "  \"model\": \"o3-2025-04-16\",\n", "  \"object\": \"chat.completion\",\n", "  \"system_fingerprint\": null,\n", "  \"usage\": {\n", "    \"completion_tokens\": 176,\n", "    \"prompt_tokens\": 20,\n", "    \"total_tokens\": 196,\n", "    \"completion_tokens_details\": {\n", "      \"accepted_prediction_tokens\": 0,\n", "      \"audio_tokens\": 0,\n", "      \"reasoning_tokens\": 64,\n", "      \"rejected_prediction_tokens\": 0\n", "    },\n", "    \"prompt_tokens_details\": {\n", "      \"audio_tokens\": 0,\n", "      \"cached_tokens\": 0\n", "    }\n", "  },\n", "  \"prompt_filter_results\": [\n", "    {\n", "      \"prompt_index\": 0,\n", "      \"content_filter_results\": {\n", "        \"hate\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"jailbreak\": {\n", "          \"filtered\": false,\n", "          \"detected\": false\n", "        },\n", "        \"self_harm\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"sexual\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"violence\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        }\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import os\n", "from openai import AzureOpenAI\n", "from azure.keyvault.secrets import SecretClient\n", "from azure.identity import AzureCliCredential\n", "\n", "credential = AzureCliCredential()\n", "kv_url = \"https://vscode-xtab.vault.azure.net/\"\n", "client = SecretClient(vault_url=kv_url, credential=credential)\n", "\n", "endpoint = os.getenv(\"ENDPOINT_URL\", \"https://mass-eus2.openai.azure.com/\")\n", "deployment = os.getenv(\"DEPLOYMENT_NAME\", \"o3\")\n", "mass_eus2 = os.getenv(\"SECRET_NAME\", \"mass-eus2\")\n", "mass_eus2_key = client.get_secret(mass_eus2).value\n", "\n", "# # Initialize Azure OpenAI client with key-based authentication\n", "client = AzureOpenAI(\n", "    azure_endpoint=endpoint,\n", "    api_key=mass_eus2_key,\n", "    api_version=\"2025-01-01-preview\",\n", ")\n", "\n", "#Prepare the chat prompt\n", "chat_prompt = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"9.11 vs 9.9, which one is bigger?\"\n", "    }\n", "]\n", "\n", "# Include speech result if speech is enabled\n", "messages = chat_prompt\n", "\n", "# Generate the completion\n", "completion = client.chat.completions.create(\n", "    model=deployment,\n", "    messages=messages,\n", "    max_completion_tokens=1000,\n", "    temperature=1,\n", "    top_p=1,\n", "    frequency_penalty=0,\n", "    presence_penalty=0,\n", "    stop=None,\n", "    stream=False\n", ")\n", "\n", "print(completion.to_json())\n"]}, {"cell_type": "markdown", "id": "c3e5ab1c", "metadata": {}, "source": ["## o3-pro"]}, {"cell_type": "code", "execution_count": null, "id": "f375c8c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "xtab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}