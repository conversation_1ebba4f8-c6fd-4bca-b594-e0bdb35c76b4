#!/usr/bin/env python3
"""
Script to extract cost and latency information from loc_trajs.jsonl files.

This script reads a JSONL file and extracts cost and latency metrics from each entry,
providing summary statistics and detailed analysis.

REQUIREMENTS:
- Python 3.6 or higher
- No additional packages required (uses only standard library)

INSTALLATION:
If Python is not installed, download and install from: https://www.python.org/downloads/
Make sure to check "Add Python to PATH" during installation.

USAGE:
python extract_cost_latency.py loc_trajs.jsonl
python extract_cost_latency.py loc_trajs.jsonl --detailed 10
python extract_cost_latency.py loc_trajs.jsonl --export metrics.csv
"""

import json
import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any
import statistics


def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from a JSONL file."""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:  # Skip empty lines
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        print(f"Warning: Failed to parse JSON on line {line_num}: {e}", file=sys.stderr)
                        continue
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}", file=sys.stderr)
        sys.exit(1)
    
    return data


def extract_metrics(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract cost and latency metrics from the data."""
    metrics = []
    
    for entry in data:
        instance_id = entry.get('instance_id', 'unknown')
        usage = entry.get('usage', {})
        time_taken = entry.get('time', None)
        
        # Extract cost and token information
        cost = usage.get('cost', None)
        prompt_tokens = usage.get('prompt_tokens', None)
        completion_tokens = usage.get('completion_tokens', None)
        total_tokens = None
        if prompt_tokens is not None and completion_tokens is not None:
            total_tokens = prompt_tokens + completion_tokens
        
        metrics.append({
            'instance_id': instance_id,
            'cost': cost,
            'latency': time_taken,
            'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens,
            'total_tokens': total_tokens
        })
    
    return metrics


def calculate_statistics(values: List[float]) -> Dict[str, float]:
    """Calculate basic statistics for a list of values."""
    if not values:
        return {}
    
    return {
        'count': len(values),
        'min': min(values),
        'max': max(values),
        'mean': statistics.mean(values),
        'median': statistics.median(values),
        'total': sum(values)
    }


def print_summary(metrics: List[Dict[str, Any]]) -> None:
    """Print summary statistics."""
    print("=" * 60)
    print("COST AND LATENCY ANALYSIS SUMMARY")
    print("=" * 60)
    
    # Filter out None values for calculations
    costs = [m['cost'] for m in metrics if m['cost'] is not None]
    latencies = [m['latency'] for m in metrics if m['latency'] is not None]
    prompt_tokens = [m['prompt_tokens'] for m in metrics if m['prompt_tokens'] is not None]
    completion_tokens = [m['completion_tokens'] for m in metrics if m['completion_tokens'] is not None]
    total_tokens = [m['total_tokens'] for m in metrics if m['total_tokens'] is not None]
    
    print(f"Total entries processed: {len(metrics)}")
    print()
    
    # Cost statistics
    if costs:
        cost_stats = calculate_statistics(costs)
        print("COST STATISTICS:")
        print(f"  Entries with cost data: {cost_stats['count']}")
        print(f"  Total cost: ${cost_stats['total']:.6f}")
        print(f"  Average cost: ${cost_stats['mean']:.6f}")
        print(f"  Median cost: ${cost_stats['median']:.6f}")
        print(f"  Min cost: ${cost_stats['min']:.6f}")
        print(f"  Max cost: ${cost_stats['max']:.6f}")
    else:
        print("COST STATISTICS: No cost data found")
    print()
    
    # Latency statistics
    if latencies:
        latency_stats = calculate_statistics(latencies)
        print("LATENCY STATISTICS:")
        print(f"  Entries with latency data: {latency_stats['count']}")
        print(f"  Total latency: {latency_stats['total']:.3f} seconds")
        print(f"  Average latency: {latency_stats['mean']:.3f} seconds")
        print(f"  Median latency: {latency_stats['median']:.3f} seconds")
        print(f"  Min latency: {latency_stats['min']:.3f} seconds")
        print(f"  Max latency: {latency_stats['max']:.3f} seconds")
    else:
        print("LATENCY STATISTICS: No latency data found")
    print()
    
    # Token statistics
    if total_tokens:
        token_stats = calculate_statistics(total_tokens)
        print("TOKEN STATISTICS:")
        print(f"  Entries with token data: {token_stats['count']}")
        print(f"  Total tokens: {int(token_stats['total']):,}")
        print(f"  Average tokens per request: {token_stats['mean']:.1f}")
        print(f"  Median tokens per request: {token_stats['median']:.1f}")
        print(f"  Min tokens: {int(token_stats['min']):,}")
        print(f"  Max tokens: {int(token_stats['max']):,}")
        
        if prompt_tokens and completion_tokens:
            prompt_stats = calculate_statistics(prompt_tokens)
            completion_stats = calculate_statistics(completion_tokens)
            print(f"  Average prompt tokens: {prompt_stats['mean']:.1f}")
            print(f"  Average completion tokens: {completion_stats['mean']:.1f}")
    else:
        print("TOKEN STATISTICS: No token data found")


def print_detailed_report(metrics: List[Dict[str, Any]], limit: int = None) -> None:
    """Print detailed per-instance report."""
    print("\n" + "=" * 60)
    print("DETAILED REPORT")
    print("=" * 60)
    
    if limit:
        print(f"Showing first {limit} entries:")
        metrics = metrics[:limit]
    
    print(f"{'Instance ID':<30} {'Cost ($)':<12} {'Latency (s)':<12} {'Tokens':<8}")
    print("-" * 70)
    
    for metric in metrics:
        instance_id = metric['instance_id'][:28] + '..' if len(metric['instance_id']) > 30 else metric['instance_id']
        cost_str = f"{metric['cost']:.6f}" if metric['cost'] is not None else "N/A"
        latency_str = f"{metric['latency']:.3f}" if metric['latency'] is not None else "N/A"
        tokens_str = f"{metric['total_tokens']:,}" if metric['total_tokens'] is not None else "N/A"
        
        print(f"{instance_id:<30} {cost_str:<12} {latency_str:<12} {tokens_str:<8}")


def export_csv(metrics: List[Dict[str, Any]], output_file: str) -> None:
    """Export metrics to CSV file."""
    import csv
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['instance_id', 'cost', 'latency', 'prompt_tokens', 'completion_tokens', 'total_tokens']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(metrics)
        print(f"\nData exported to: {output_file}")
    except Exception as e:
        print(f"Error exporting to CSV: {e}", file=sys.stderr)


def main():
    parser = argparse.ArgumentParser(
        description="Extract cost and latency information from loc_trajs.jsonl files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python extract_cost_latency.py loc_trajs.jsonl
  python extract_cost_latency.py loc_trajs.jsonl --detailed 10
  python extract_cost_latency.py loc_trajs.jsonl --export metrics.csv
  python extract_cost_latency.py loc_trajs.jsonl --detailed --export metrics.csv
        """
    )
    
    parser.add_argument('file', help='Path to the loc_trajs.jsonl file')
    parser.add_argument('--detailed', type=int, nargs='?', const=0, 
                       help='Show detailed per-instance report (optionally limit to N entries)')
    parser.add_argument('--export', type=str, metavar='CSV_FILE',
                       help='Export data to CSV file')
    
    args = parser.parse_args()
    
    # Load and process data
    print(f"Loading data from: {args.file}")
    data = load_jsonl(args.file)
    
    if not data:
        print("No data found in the file.")
        return
    
    metrics = extract_metrics(data)
    
    # Print summary
    print_summary(metrics)
    
    # Print detailed report if requested
    if args.detailed is not None:
        limit = args.detailed if args.detailed > 0 else None
        print_detailed_report(metrics, limit)
    
    # Export to CSV if requested
    if args.export:
        export_csv(metrics, args.export)


if __name__ == "__main__":
    main()
