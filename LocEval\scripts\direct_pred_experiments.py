"""Run experiments with different models and filter settings."""

import subprocess
import sys
from pathlib import Path
import os
import json
import time
from datetime import datetime, timedelta
import threading

# Experiment configuration
DATASET = "princeton-nlp/SWE-bench_Lite_bm25_13K"
METHOD = "direct_prediction"

# Azure OpenAI configuration
AZURE_CONFIG = {
    "AZURE_OPENAI_ENDPOINT": "https://mass-swc.openai.azure.com/",
    "AZURE_OPENAI_API_VERSION": "2025-01-01-preview",
}

# Token refresh settings
TOKEN_LIFETIME_MINUTES = 55  # Refresh token before it expires (Azure tokens last ~60 min)
TOKEN_REFRESH_INTERVAL = 30  # Refresh every 30 minutes to be safe
last_token_time = None
token_refresh_thread = None
stop_refresh = False

# Models with their respective date filters (None means no date filter)
EXPERIMENTS = [
    # ("o4-mini", "o4-mini", "2025-04-16", True),
    # ("o4-mini", "o4-mini", None, True),
    # ("gpt-4.1-swc", "gpt-4.1-swc", "2024-06-30", True),
    ("gpt-4.1-swc", "gpt-4.1-swc", None, True),
    # ("gpt-4.1-mini", "gpt-4.1-mini", "2024-06-30", True),
    ("gpt-4.1-mini", "gpt-4.1-mini", None, True),
]

def get_azure_token():
    """Get Azure access token."""
    global last_token_time
    
    print(f"\n🔄 Getting Azure access token at {datetime.now().strftime('%H:%M:%S')}...")
    result = subprocess.run(
        "az account get-access-token --scope https://cognitiveservices.azure.com/.default --query accessToken -o tsv",
        shell=True,
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        print("❌ Failed to get access token")
        print(f"Error: {result.stderr}")
        return None
    
    token = result.stdout.strip()
    last_token_time = datetime.now()
    print(f"✅ Token obtained successfully at {last_token_time.strftime('%H:%M:%S')}")
    return token

def token_refresh_worker():
    """Background thread that refreshes token periodically."""
    global stop_refresh
    
    while not stop_refresh:
        # Wait for refresh interval
        for _ in range(TOKEN_REFRESH_INTERVAL * 60):  # Check every second
            if stop_refresh:
                return
            time.sleep(1)
        
        if not stop_refresh:
            print(f"\n⏰ Background token refresh triggered at {datetime.now().strftime('%H:%M:%S')}")
            token = get_azure_token()
            if token:
                os.environ["AZURE_OPENAI_API_KEY"] = token
            else:
                print("⚠️  Failed to refresh token in background")

def start_token_refresh_thread():
    """Start the background token refresh thread."""
    global token_refresh_thread, stop_refresh
    
    stop_refresh = False
    token_refresh_thread = threading.Thread(target=token_refresh_worker, daemon=True)
    token_refresh_thread.start()
    print(f"🚀 Started background token refresh thread (interval: {TOKEN_REFRESH_INTERVAL} minutes)")

def stop_token_refresh_thread():
    """Stop the background token refresh thread."""
    global stop_refresh
    
    stop_refresh = True
    if token_refresh_thread and token_refresh_thread.is_alive():
        token_refresh_thread.join(timeout=5)
        print("🛑 Stopped background token refresh thread")

def run_experiment(model, deployment, date_filter, simple_filter=True):
    """Run a single experiment."""
    
    # Build experiment name
    date_suffix = f"_after_{date_filter.replace('-', '')}" if date_filter else "_no_date_filter"
    simple_suffix = "_simple" if simple_filter else ""
    experiment_name = f"{model.replace('.', '_').replace('-', '_')}{simple_suffix}{date_suffix}"
    
    # Check if result already exists
    output_path = Path(f"results/experiments/{experiment_name}.jsonl")
    if output_path.exists():
        print(f"⏭️  Skipping {experiment_name} - results already exist")
        return
    
    # Set Azure OpenAI deployment
    env = os.environ.copy()
    env["AZURE_OPENAI_DEPLOYMENT"] = deployment

    # Build command
    cmd = [
        sys.executable, "-m", "IssueLocEval.main", "run",
        "--dataset", DATASET,
        "--method", METHOD,
        "--model", model,
        "--evaluate",
        "--temperature", "0.7"
    ]
    # Add simple filter if specified
    if simple_filter:
        cmd.append("--filter-out-simple")
    # Add date filter if specified
    if date_filter:
        cmd.extend(["--filter-out-old", date_filter])
    
    print(f"\nRunning: {experiment_name}")
    print(f"Model: {model}, Deployment: {deployment}, Date Filter: {date_filter}")
    
    # Track experiment time
    start_time = time.time()
    
    # Run the command
    result = subprocess.run(cmd, env=env)
    
    # Log experiment duration
    duration = time.time() - start_time
    print(f"{'✅' if result.returncode == 0 else '❌'} Experiment completed in {duration/60:.1f} minutes")
    
    # If experiment failed, note it
    if result.returncode != 0:
        print(f"❌ Experiment {experiment_name} failed with return code {result.returncode}")

def main():
    """Run all experiments."""
    
    try:
        # Get initial Azure token and set environment variables
        token = get_azure_token()
        if not token:
            print("Failed to get initial token. Exiting.")
            sys.exit(1)
        
        # Set Azure OpenAI environment variables
        os.environ.update(AZURE_CONFIG)
        os.environ["AZURE_OPENAI_API_KEY"] = token
        
        # Start background token refresh thread
        start_token_refresh_thread()
        
        # Create results directory
        Path("results/experiments").mkdir(parents=True, exist_ok=True)
        
        print(f"\nRunning {len(EXPERIMENTS)} experiments")
        print(f"Dataset: {DATASET}")
        print(f"Token auto-refresh: every {TOKEN_REFRESH_INTERVAL} minutes")
        print("=" * 50)
        
        # Track overall progress
        start_time = time.time()
        completed = 0
        
        # Run experiments
        for i, (model, deployment, date_filter, simple_filter) in enumerate(EXPERIMENTS, 1):
            print(f"\n{'='*50}")
            print(f"Experiment {i}/{len(EXPERIMENTS)}")
            print(f"{'='*50}")
            
            run_experiment(model, deployment, date_filter, simple_filter)
            completed += 1
            
            # Show progress
            elapsed = time.time() - start_time
            avg_time = elapsed / completed
            remaining = len(EXPERIMENTS) - completed
            eta = remaining * avg_time
            
            print(f"\nProgress: {completed}/{len(EXPERIMENTS)} experiments completed")
            print(f"Elapsed time: {elapsed/60:.1f} minutes")
            if remaining > 0:
                print(f"Estimated time remaining: {eta/60:.1f} minutes")
        
        total_time = time.time() - start_time
        print(f"\n{'='*50}")
        print(f"All experiments completed in {total_time/60:.1f} minutes!")
        print(f"{'='*50}")
        
    finally:
        # Stop the token refresh thread
        stop_token_refresh_thread()

if __name__ == "__main__":
    main()