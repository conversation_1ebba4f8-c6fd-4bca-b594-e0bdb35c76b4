#!/usr/bin/env python3
"""
Detailed analysis of loc_trajs.jsonl file to extract comprehensive information.
"""

import json
import sys
from typing import Dict, Any, List

def analyze_trajectory_details(data: List[Dict[str, Any]]) -> None:
    """
    Analyze trajectory details in depth.
    """
    print("=" * 100)
    print("DETAILED TRAJECTORY ANALYSIS")
    print("=" * 100)
    
    for i, record in enumerate(data):
        print(f"\n{'='*20} RECORD {i + 1}: {record.get('instance_id', 'Unknown')} {'='*20}")
        
        if 'loc_trajs' not in record:
            print("No loc_trajs found.")
            continue
            
        loc_trajs = record['loc_trajs']
        if 'trajs' not in loc_trajs:
            print("No trajectories found.")
            continue
            
        trajs = loc_trajs['trajs']
        
        for j, traj in enumerate(trajs):
            print(f"\n--- TRAJECTORY {j + 1} ---")
            
            # Basic trajectory info
            if 'time' in traj:
                print(f"Execution time: {traj['time']:.2f} seconds")
            
            if 'usage' in traj:
                usage = traj['usage']
                print(f"Usage: {usage}")
            
            if 'tools' in traj:
                tools = traj['tools']
                print(f"Available tools: {len(tools)}")
                for tool in tools:
                    if isinstance(tool, dict) and 'function' in tool:
                        print(f"  - {tool['function'].get('name', 'Unknown')}")
            
            # Analyze messages
            if 'messages' in traj:
                messages = traj['messages']
                print(f"\nMessage flow ({len(messages)} messages):")
                
                system_msgs = 0
                user_msgs = 0
                assistant_msgs = 0
                tool_msgs = 0
                tool_calls_count = 0
                
                for k, msg in enumerate(messages):
                    role = msg.get('role', 'unknown')
                    
                    if role == 'system':
                        system_msgs += 1
                    elif role == 'user':
                        user_msgs += 1
                    elif role == 'assistant':
                        assistant_msgs += 1
                    elif role == 'tool':
                        tool_msgs += 1
                    
                    # Count tool calls
                    if 'tool_calls' in msg and msg['tool_calls']:
                        tool_calls_count += len(msg['tool_calls'])
                        print(f"  {k+1}. {role}: {len(msg['tool_calls'])} tool call(s)")
                        for tool_call in msg['tool_calls']:
                            if isinstance(tool_call, dict) and 'function' in tool_call:
                                func_name = tool_call['function'].get('name', 'unknown')
                                print(f"     -> {func_name}")
                    else:
                        content = msg.get('content', '')
                        if content:
                            content_preview = content[:80] + "..." if len(content) > 80 else content
                            print(f"  {k+1}. {role}: {content_preview}")
                        else:
                            print(f"  {k+1}. {role}: [no content]")
                
                print(f"\nMessage summary:")
                print(f"  System messages: {system_msgs}")
                print(f"  User messages: {user_msgs}")
                print(f"  Assistant messages: {assistant_msgs}")
                print(f"  Tool messages: {tool_msgs}")
                print(f"  Total tool calls: {tool_calls_count}")

def analyze_tool_usage_patterns(data: List[Dict[str, Any]]) -> None:
    """
    Analyze tool usage patterns across all trajectories.
    """
    print("\n" + "=" * 100)
    print("TOOL USAGE ANALYSIS")
    print("=" * 100)
    
    tool_usage = {}
    total_tool_calls = 0
    
    for record in data:
        if 'loc_trajs' not in record or 'trajs' not in record['loc_trajs']:
            continue
            
        for traj in record['loc_trajs']['trajs']:
            if 'messages' not in traj:
                continue
                
            for msg in traj['messages']:
                if 'tool_calls' in msg and msg['tool_calls']:
                    for tool_call in msg['tool_calls']:
                        if isinstance(tool_call, dict) and 'function' in tool_call:
                            func_name = tool_call['function'].get('name', 'unknown')
                            tool_usage[func_name] = tool_usage.get(func_name, 0) + 1
                            total_tool_calls += 1
    
    print(f"Total tool calls across all trajectories: {total_tool_calls}")
    print("\nTool usage frequency:")
    for tool, count in sorted(tool_usage.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_tool_calls) * 100 if total_tool_calls > 0 else 0
        print(f"  {tool}: {count} times ({percentage:.1f}%)")

def analyze_record_totals(data: List[Dict[str, Any]]) -> None:
    """
    Calculate total time, prompt tokens, and completion tokens for each record.
    """
    print("\n" + "=" * 100)
    print("RECORD TOTALS (Time and Token Usage)")
    print("=" * 100)

    for i, record in enumerate(data):
        instance_id = record.get('instance_id', 'Unknown')
        print(f"\n--- Record {i + 1}: {instance_id} ---")

        if 'loc_trajs' not in record or 'trajs' not in record['loc_trajs']:
            print("No trajectories found.")
            continue

        total_time = 0
        total_prompt_tokens = 0
        total_completion_tokens = 0
        trajectory_count = 0

        for j, traj in enumerate(record['loc_trajs']['trajs']):
            trajectory_count += 1

            # Add time
            if 'time' in traj:
                total_time += traj['time']
                print(f"  Trajectory {j + 1} time: {traj['time']:.2f} seconds")

            # Add token usage
            if 'usage' in traj:
                usage = traj['usage']
                if isinstance(usage, dict):
                    prompt_tokens = usage.get('prompt_tokens', 0)
                    completion_tokens = usage.get('completion_tokens', 0)
                    total_prompt_tokens += prompt_tokens
                    total_completion_tokens += completion_tokens
                    print(f"  Trajectory {j + 1} tokens: {prompt_tokens} prompt, {completion_tokens} completion")

        print(f"\n  TOTALS for {instance_id}:")
        print(f"    Total trajectories: {trajectory_count}")
        print(f"    Total time: {total_time:.2f} seconds")
        print(f"    Total prompt tokens: {total_prompt_tokens:,}")
        print(f"    Total completion tokens: {total_completion_tokens:,}")
        print(f"    Total tokens: {total_prompt_tokens + total_completion_tokens:,}")

def analyze_performance_metrics(data: List[Dict[str, Any]]) -> None:
    """
    Analyze performance metrics from the trajectories.
    """
    print("\n" + "=" * 100)
    print("OVERALL PERFORMANCE METRICS")
    print("=" * 100)

    times = []
    message_counts = []
    tool_call_counts = []
    all_prompt_tokens = []
    all_completion_tokens = []

    for record in data:
        if 'loc_trajs' not in record or 'trajs' not in record['loc_trajs']:
            continue

        for traj in record['loc_trajs']['trajs']:
            # Collect timing data
            if 'time' in traj:
                times.append(traj['time'])

            # Collect token usage
            if 'usage' in traj and isinstance(traj['usage'], dict):
                prompt_tokens = traj['usage'].get('prompt_tokens', 0)
                completion_tokens = traj['usage'].get('completion_tokens', 0)
                all_prompt_tokens.append(prompt_tokens)
                all_completion_tokens.append(completion_tokens)

            # Collect message counts
            if 'messages' in traj:
                message_counts.append(len(traj['messages']))

                # Count tool calls
                tool_calls = 0
                for msg in traj['messages']:
                    if 'tool_calls' in msg and msg['tool_calls']:
                        tool_calls += len(msg['tool_calls'])
                tool_call_counts.append(tool_calls)

    if times:
        print(f"Execution times:")
        print(f"  Average: {sum(times)/len(times):.2f} seconds")
        print(f"  Min: {min(times):.2f} seconds")
        print(f"  Max: {max(times):.2f} seconds")
        print(f"  Total: {sum(times):.2f} seconds")

    if all_prompt_tokens:
        print(f"\nPrompt tokens:")
        print(f"  Average: {sum(all_prompt_tokens)/len(all_prompt_tokens):,.0f} tokens")
        print(f"  Min: {min(all_prompt_tokens):,} tokens")
        print(f"  Max: {max(all_prompt_tokens):,} tokens")
        print(f"  Total: {sum(all_prompt_tokens):,} tokens")

    if all_completion_tokens:
        print(f"\nCompletion tokens:")
        print(f"  Average: {sum(all_completion_tokens)/len(all_completion_tokens):,.0f} tokens")
        print(f"  Min: {min(all_completion_tokens):,} tokens")
        print(f"  Max: {max(all_completion_tokens):,} tokens")
        print(f"  Total: {sum(all_completion_tokens):,} tokens")

    if message_counts:
        print(f"\nMessage counts per trajectory:")
        print(f"  Average: {sum(message_counts)/len(message_counts):.1f} messages")
        print(f"  Min: {min(message_counts)} messages")
        print(f"  Max: {max(message_counts)} messages")

    if tool_call_counts:
        print(f"\nTool calls per trajectory:")
        print(f"  Average: {sum(tool_call_counts)/len(tool_call_counts):.1f} tool calls")
        print(f"  Min: {min(tool_call_counts)} tool calls")
        print(f"  Max: {max(tool_call_counts)} tool calls")

def main():
    """Main function."""
    file_path = "C:\\Users\\<USER>\\OSS\\LocEval\\example_results\\experiment1\\gpt-4.1-mini-czlll-swebenchlite\\loc_trajs_example2.jsonl"
    
    # Parse the file
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line:
                    data.append(json.loads(line))
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    print(f"Loaded {len(data)} records from {file_path}")
    
    # Run analyses
    analyze_trajectory_details(data)
    analyze_record_totals(data)
    analyze_tool_usage_patterns(data)
    analyze_performance_metrics(data)

if __name__ == "__main__":
    main()
