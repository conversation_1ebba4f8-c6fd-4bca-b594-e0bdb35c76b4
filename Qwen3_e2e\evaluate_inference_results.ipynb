{"cells": [{"cell_type": "code", "execution_count": 13, "id": "cb2f772b", "metadata": {}, "outputs": [], "source": ["# Cell 1: Import and helper functions\n", "import azure.ai.ml._artifacts._artifact_utilities as artifact_utils\n", "import pandas as pd\n", "import json\n", "import numpy as np\n", "from rouge import Rouge\n", "from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction\n", "\n", "def load_vllm_results(job_name, ml_client):\n", "    \"\"\"Load vLLM inference results from job output\"\"\"\n", "    # Download from job outputs\n", "    ml_client.jobs.download(\n", "        name=job_name,\n", "        download_path=f\"/tmp/{job_name}/\",\n", "        output_name=\"datastore_dir\"\n", "    )\n", "    \n", "    # Load JSONL file\n", "    predictions = []\n", "    with open(f\"/tmp/{job_name}/generated_predictions.jsonl\", \"r\") as f:\n", "        for line in f:\n", "            predictions.append(json.loads(line))\n", "    \n", "    return pd.<PERSON><PERSON><PERSON><PERSON>(predictions)\n", "\n", "def load_vllm_results_from_blob(job_name, ml_client):\n", "    \"\"\"Load vLLM inference results from blob storage for incomplete jobs\"\"\"\n", "    import os\n", "    from azure.ai.ml import MLClient\n", "    from azure.storage.blob import BlobServiceClient\n", "    \n", "    job = ml_client.jobs.get(name=job_name)\n", "    blob_path = job.outputs.datastore_dir.path\n", "    \n", "    # Method 1: Try using artifact_utils (what the original notebook uses)\n", "    try:\n", "        import azure.ai.ml._artifacts._artifact_utilities as artifact_utils\n", "        local_path = f\"/tmp/{job_name}/\"\n", "        os.makedirs(local_path, exist_ok=True)\n", "        \n", "        artifact_utils.download_artifact_from_aml_uri(\n", "            uri=blob_path, \n", "            destination=local_path,\n", "            datastore_operation=ml_client.datastores\n", "        )\n", "        \n", "        # Find and load the JSONL file\n", "        for root, dirs, files in os.walk(local_path):\n", "            for file in files:\n", "                if file == \"generated_predictions.jsonl\":\n", "                    predictions = []\n", "                    with open(os.path.join(root, file), \"r\") as f:\n", "                        for line in f:\n", "                            predictions.append(json.loads(line))\n", "                    return pd.<PERSON><PERSON><PERSON><PERSON>(predictions)\n", "    except Exception as e:\n", "        print(f\"Method 1 failed: {e}\")\n", "    \n", "    # Method 2: Download directly using datastore\n", "    try:\n", "        # Get the datastore\n", "        datastore = ml_client.datastores.get(\"workspaceblobstore\")\n", "        \n", "        # Download using the datastore path\n", "        import re\n", "        match = re.search(r'paths/(.+)', blob_path)\n", "        if match:\n", "            blob_relative_path = match.group(1)\n", "            \n", "            # Create a temporary file\n", "            import tempfile\n", "            with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as tmp:\n", "                tmp_path = tmp.name\n", "            \n", "            # Download the specific file\n", "            full_blob_path = f\"{blob_relative_path}/generated_predictions.jsonl\"\n", "            datastore.download(src=full_blob_path, dst=tmp_path)\n", "            \n", "            # Load the file\n", "            predictions = []\n", "            with open(tmp_path, \"r\") as f:\n", "                for line in f:\n", "                    predictions.append(json.loads(line))\n", "            \n", "            os.unlink(tmp_path)  # Clean up\n", "            return pd.<PERSON><PERSON><PERSON><PERSON>(predictions)\n", "    except Exception as e:\n", "        print(f\"Method 2 failed: {e}\")\n", "    \n", "    raise FileNotFoundError(\"Could not download generated_predictions.jsonl\")\n", "\n", "def evaluate_generation_quality(df):\n", "    \"\"\"Calculate BLEU and ROUGE scores\"\"\"\n", "    rouge = Rouge()\n", "    results = {\n", "        'bleu_scores': [],\n", "        'rouge_scores': [],\n", "        'exact_matches': 0,\n", "        'total': 0\n", "    }\n", "    \n", "    for _, row in df.iterrows():\n", "        pred = row.get('predict', row.get('generated', ''))\n", "        label = row.get('label', '')\n", "        \n", "        if pred and label:\n", "            results['total'] += 1\n", "            \n", "            # Exact match\n", "            if pred.strip() == label.strip():\n", "                results['exact_matches'] += 1\n", "            \n", "            # BLEU\n", "            bleu = sentence_bleu(\n", "                [label.split()], \n", "                pred.split(), \n", "                smoothing_function=SmoothingFunction().method3\n", "            )\n", "            results['bleu_scores'].append(bleu)\n", "            \n", "            # ROUGE\n", "            try:\n", "                scores = rouge.get_scores(pred, label)[0]\n", "                results['rouge_scores'].append(scores)\n", "            except:\n", "                pass\n", "    \n", "    return results"]}, {"cell_type": "code", "execution_count": 8, "id": "cc4f6cee", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Overriding of current TracerProvider is not allowed\n", "Overriding of current LoggerProvider is not allowed\n", "Overriding of current MeterProvider is not allowed\n", "Attempting to instrument while already instrumented\n", "Attempting to instrument while already instrumented\n", "Attempting to instrument while already instrumented\n", "Attempting to instrument while already instrumented\n"]}], "source": ["# Cell 2: Connect to ML Client\n", "from azure.ai.ml import MLClient\n", "from azure.identity import DefaultAzureCredential\n", "\n", "ml_client = MLClient(\n", "    DefaultAzureCredential(),\n", "    subscription_id=\"d0c05057-7972-46ff-9bcf-3c932250155e\",\n", "    resource_group_name=\"SingularityH100\",\n", "    workspace_name=\"H100CentralUS\"\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "f1f9a5d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/SingularityH100/workspaces/H100CentralUS/datastores/workspaceblobstore/paths/xhou/upgrade_sft_inference_2025-06-06_21-33-44/\n"]}], "source": ["# Get job details\n", "job = ml_client.jobs.get(name=\"coral_ball_pmfvj2jx95\")\n", "print(job.outputs.datastore_dir.path)  # Shows the actual blob path"]}, {"cell_type": "code", "execution_count": 14, "id": "10a37fe6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total predictions: 300\n", "Columns: ['prompt', 'predict', 'label']\n", "\n", "Average prediction length: 335.04 chars\n", "Average label length: 4710.26 chars\n", "\n", "Exact match accuracy: 0.00%\n", "BLEU-4 score: 6.84\n"]}], "source": ["# Cell 3: Load and evaluate your results\n", "# Replace with your actual job name\n", "job_name = \"coral_ball_pmfvj2jx95\"  # e.g., \"hungry_street_4pgp13p0zj\"\n", "\n", "# Load results\n", "df = load_vllm_results_from_blob(job_name, ml_client)\n", "\n", "# Basic statistics\n", "print(f\"Total predictions: {len(df)}\")\n", "print(f\"Columns: {df.columns.tolist()}\")\n", "\n", "# Add length analysis\n", "df['pred_length'] = df['predict'].apply(lambda x: len(str(x)) if pd.notna(x) else 0)\n", "df['label_length'] = df['label'].apply(lambda x: len(str(x)) if pd.notna(x) else 0)\n", "\n", "print(f\"\\nAverage prediction length: {df['pred_length'].mean():.2f} chars\")\n", "print(f\"Average label length: {df['label_length'].mean():.2f} chars\")\n", "\n", "# Evaluate quality\n", "results = evaluate_generation_quality(df)\n", "\n", "# Print evaluation metrics\n", "print(f\"\\nExact match accuracy: {results['exact_matches']/results['total']*100:.2f}%\")\n", "print(f\"BLEU-4 score: {np.mean(results['bleu_scores'])*100:.2f}\")\n", "\n", "if results['rouge_scores']:\n", "    print(f\"ROUGE-1 F1: {np.mean([s['rouge-1']['f'] for s in results['rouge_scores']])*100:.2f}\")\n", "    print(f\"ROUGE-2 F1: {np.mean([s['rouge-2']['f'] for s in results['rouge_scores']])*100:.2f}\")\n", "    print(f\"ROUGE-L F1: {np.mean([s['rouge-l']['f'] for s in results['rouge_scores']])*100:.2f}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "3ffb1511", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Sample predictions:\n", "\n", "--- Example 1 ---\n", "Prompt: <|im_start|>system\n", "You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "<SYSTEM>\n", "You are an AI pr...\n", "Prediction: ```javascript\n", "UNCHANGED\n", "```...\n", "Label: 'use strict'\n", "\n", "const assert = require('assert')\n", "const context = require('../../test-helpers/context')\n", "\n", "describe('ctx.vary(field)', () => {\n", "  describe('when <PERSON><PERSON> is not set', () => {\n", "    it('should set ...\n", "\n", "--- Example 2 ---\n", "Prompt: <|im_start|>system\n", "You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "<SYSTEM>\n", "You are an AI pr...\n", "Prediction: ```java\n", "UNCHANGED\n", "```...\n", "Label: package org.junit.runner;\n", "\n", "import org.junit.runner.manipulation.Filter;\n", "\n", "/**\n", " * Extend this class to create a factory that creates {@link Filter}.\n", " */\n", "public interface FilterFactory {\n", "... /**\n", "\n", "--- Example 3 ---\n", "Prompt: <|im_start|>system\n", "You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "<SYSTEM>\n", "You are an AI pr...\n", "Prediction: ```<PERSON><PERSON>\n", "UNCHANGED\n", "```...\n", "Label: *.class\n", "*.log\n", "\n", "# sbt specific\n", ".cache\n", ".classpath\n", ".target\n", "dist/*\n", "target/\n", "lib_managed/\n", "src_managed/\n", "project/boot/\n", "project/plugins/project/\n", "\n", "# Scala-IDE specific\n", ".scala_dependencies\n", ".project\n", ".settings\n", ".ca...\n"]}], "source": ["# Cell 4: Visualizations (optional)\n", "import matplotlib.pyplot as plt\n", "\n", "# Length distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))\n", "\n", "ax1.hist(df['pred_length'], bins=30, alpha=0.7, label='Predictions')\n", "ax1.hist(df['label_length'], bins=30, alpha=0.7, label='Labels')\n", "ax1.set_xlabel('Text Length (chars)')\n", "ax1.set_ylabel('Count')\n", "ax1.set_title('Length Distribution')\n", "ax1.legend()\n", "\n", "# Length correlation\n", "ax2.scatter(df['label_length'], df['pred_length'], alpha=0.5)\n", "ax2.plot([0, df['label_length'].max()], [0, df['label_length'].max()], 'r--', label='y=x')\n", "ax2.set_xlabel('Label Length')\n", "ax2.set_ylabel('Prediction Length')\n", "ax2.set_title('Prediction vs Label Length')\n", "ax2.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Sample outputs\n", "print(\"\\nSample predictions:\")\n", "for i in range(min(3, len(df))):\n", "    print(f\"\\n--- Example {i+1} ---\")\n", "    print(f\"Prompt: {df.iloc[i]['prompt'][:100]}...\")\n", "    print(f\"Prediction: {df.iloc[i]['predict'][:200]}...\")\n", "    print(f\"Label: {df.iloc[i]['label'][:200]}...\")"]}], "metadata": {"kernelspec": {"display_name": "llama_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}