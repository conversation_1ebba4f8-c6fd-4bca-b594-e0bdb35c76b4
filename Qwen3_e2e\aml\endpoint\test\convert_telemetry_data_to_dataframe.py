import json
import pandas as pd
import os

os.makedirs("telemetry_test_set", exist_ok=True)

df = pd.read_csv("telemetry_test_set.csv")
justify = len(str(len(df)))

for i, data in df.iterrows():
    prompt = data['prompt']
    speculation = data['file_before']
    data = {
        "model": "",
        "messages": [{"role": "user", "content": prompt}],
        "speculation": speculation,
        "temperature": 0.0,
        "top_p": 1.0,
        "max_tokens": 10000,
    }
    with open(f"telemetry_test_set/example_{str(i).rjust(justify, '0')}.json", "w") as of:
        json.dump(data, of)
