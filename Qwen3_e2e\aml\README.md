# AML Utilities

## LLAMAFactory environment

Run this script to build and register the llamafactory environment. Switch out the image/environment name, tag, registry name, workspace, and resource group as needed.

```bash
az login
cd environment # put your own docker file inside
bash build_and_register_environment.sh fastapply optimized-all 1e6d60a2ac124c168aa42090bc1ecc97 securityfilter_ws supplychain_security LlamaFactoryOptimized Poirot_RnD
```

AML link: version 3 of https://ml.azure.com/environments/LlamaFactoryOptimized/version/3?wsid=/subscriptions/7b4a614f-3f5e-4896-af31-d1d20477a79c/resourcegroups/supplychain_security/providers/Microsoft.MachineLearningServices/workspaces/securityfilter_ws&tid=72f988bf-86f1-41af-91ab-2d7cd011db47

## Endpoint

### Download the checkpoint

Download checkpoint and upload it to your workspace if needed.

```bash
az login
cd environment

# Download adapter weights from data asset
python manage.py download --config configs/H100CentralUS.json --data_asset_name qwen25_10k_fa2_32kcon_1218_full --data_asset_destination /datadisk/full_models/qwen25_10k_fa2_32kcon_1218_full

# NOTE: not needed as we will try to download the model weights from huggingface hub from now on.
# Export base model weights from a container with llamafactory installed
docker run --rm -it -v $PWD/aml/model:/model 1e6d60a2ac124c168aa42090bc1ecc97.azurecr.io/fastapply:optimized-all bash
# inside the docker container...
llamafactory-cli export --model_name_or_path Qwen/Qwen2.5-Coder-32B-Instruct --template qwen --export_dir /model/checkpoint
```

### Merge adapter checkpoint with base model weights

These commands are specific to the [fastattn-builder](https://ms.portal.azure.com/#@microsoft.onmicrosoft.com/resource/subscriptions/7b4a614f-3f5e-4896-af31-d1d20477a79c/resourceGroups/zijianjin-rg/providers/Microsoft.Compute/virtualMachines/fastattn-builder/overview) VM.
On this VM, the /datadisk directory is laid out roughly like this:

```bash
├── adapters
│   ├── llama33_10k_fa2_32kcon_1218
│   └── qwen25_10k_fa2_32kcon_1218
├── huggingface # We point the huggingface cache to a disk with larger capacity to avoid filling up the OS disk.
│   └── hub
└── merges
    ├── llama33_10k_fa2_32kcon_1218
    ├── qwen25_10k_fa2_32kcon_1218
    ├── merge_llama33_10k_fa2_32kcon_1218.yaml
    └── merge_qwen25_10k_fa2_32kcon_1218.yaml
```

```bash
export HF_HOME="/datadisk/huggingface"
cd /datadisk/merges

# Example YAML file
cat merge_llama33_10k_fa2_32kcon_1218.yaml 
# model_name_or_path: meta-llama/Llama-3.3-70B-Instruct
# adapter_name_or_path: ../adapters/llama33_10k_fa2_32kcon_1218
# template: llama3
# finetuning_type: lora

# export_dir: ./llama33_10k_fa2_32kcon_1218

llamafactory-cli export merge_llama33_10k_fa2_32kcon_1218.yaml
```

### Deploy the model to an endpoint

Create a model in AML with the model/ folder, renamed like this:

```bash
model/
├── adapter (renamed from the chosen adapter checkpoint)
├── checkpoint
```

Deploy by running the following script. To host it on a different workspace or different settings, you may have to change the environment or SKU in `deployment.yml`, and the resource group + workspace name in `create_*.sh`.

```bash
az login
python manage.py model --config configs/securityfilter_ws.json --model_name qwen25_10k_fa2_32kcon_1218 --model_path /datadisk/adapters/qwen25_10k_fa2_32kcon_1218 --model_description "Finetuned Qwen 2.5 for FastApply."
python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-inference-qwen
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-inference-qwen --deployment_name qwen25_10k_fa2_32kcon_1218 --model_name qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:3 --sku Standard_NC24ads_A100_v4 --model_name_or_path Qwen/Qwen2.5-Coder-32B-Instruct --adapter_name_or_path '${AZUREML_MODEL_DIR}/model/adapter' --template qwen
```

AML link: https://ml.azure.com/endpoints/realtime/fastapply-inference/test?wsid=/subscriptions/7b4a614f-3f5e-4896-af31-d1d20477a79c/resourcegroups/supplychain_security/providers/Microsoft.MachineLearningServices/workspaces/securityfilter_ws&tid=72f988bf-86f1-41af-91ab-2d7cd011db47
