"""Dataset registry for managing available datasets."""

from typing import Dict, Optional, Any
from .hf_dataset import HuggingFaceDataset
from .base_dataset import BaseDataset


class DatasetRegistry:
    """Registry for managing available datasets."""
    
    # Mapping: hf_dataset_path -> field_mapping
    _datasets: Dict[str, Optional[Dict[str, str]]] = {
        "princeton-nlp/SWE-bench_Lite_bm25_13K": None,
        "princeton-nlp/SWE-bench": None,
        "princeton-nlp/SWE-bench_Verified": None,
        "czlll/SWE-bench_Lite": None,
        "czlll/Loc-Bench_V1": None,
        "LogicStar/SWA-Bench": None,
        # Add more datasets here as needed
    }
    
    @classmethod
    def register(cls, hf_path: str, field_mapping: Optional[Dict[str, str]] = None) -> None:
        """Register a new dataset.
        
        Args:
            hf_path: HuggingFace dataset path
            field_mapping: Optional field mapping
        """
        cls._datasets[hf_path] = field_mapping
    
    @classmethod
    def get(cls, hf_path: str, **kwargs) -> BaseDataset:
        """Get a dataset instance by HuggingFace path.
        
        Args:
            hf_path: HuggingFace dataset path
            **kwargs: Arguments to pass to dataset constructor
            
        Returns:
            HuggingFaceDataset instance
            
        Raises:
            ValueError: If dataset is not registered
        """
        if hf_path not in cls._datasets:
            available = list(cls._datasets.keys())
            raise ValueError(f"Dataset '{hf_path}' not found. Available datasets: {available}")
        
        field_mapping = cls._datasets[hf_path]
        
        # Create and return a HuggingFaceDataset instance
        return HuggingFaceDataset(
            dataset_name=hf_path,  # Use full path as name
            hf_dataset_path=hf_path,
            field_mapping=field_mapping or {},
            **kwargs
        )
    
    @classmethod
    def list_available(cls) -> list[str]:
        """List all available datasets.
        
        Returns:
            List of HuggingFace dataset paths
        """
        return list(cls._datasets.keys())
