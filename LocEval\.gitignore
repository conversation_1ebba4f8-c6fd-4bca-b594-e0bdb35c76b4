# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
dist/
build/
*.egg
.pytest_cache/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.mypy_cache/
.ruff_cache/

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
virtualenv/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject
.settings/
*.sublime-project
*.sublime-workspace

# Results and outputs
results/
outputs/
# *.jsonl
# *.json
*.csv
*.tsv
*.log
*.eval.csv
*.eval.json

# Data
data/
cache/
.cache/
*.db
*.sqlite
*.sqlite3

# Environment variables
.env
.env.local
.env.*.local
*.env

# OS
.DS_Store
Thumbs.db
desktop.ini

# Jupyter
.ipynb_checkpoints/
# *.ipynb

# Documentation
docs/_build/
docs/_static/
docs/_templates/
site/

# Testing
htmlcov/
.tox/
.nox/

# Temporary files
*.tmp
*.temp
*.bak
~*
*~
tmp/
temp/

# Model files and checkpoints
*.pt
*.pth
*.ckpt
*.safetensors
*.bin
models/
checkpoints/

# Large files
*.tar.gz
*.zip
*.tar
*.gz

# Secrets
secrets/
*.key
*.pem
*.cert
*.crt