import os
import json
import argparse
from typing import List, Dict, Any, Optional, Tu<PERSON>
from datasets import load_dataset
from tqdm import tqdm

# Import from IssueLocEval package
from IssueLocEval.datasets.hf_dataset import HuggingFaceDataset
from IssueLocEval.metrics.eval_metrics import LocalizationEvaluator
from IssueLocEval.utils.file_loader import load_file_or_data_asset, get_ml_client, load_predictions, load_trajectory_data

# Import necessary components from the existing codebase
# from auto_search_main import get_task_instruction, PromptManager, LocationToolsRequirement
# from util.prompts import general_prompt
# from util.runtime import function_calling

# TODO: to update after loc agent is all in
# def create_prompt_for_instance(instance: Dict[str, Any], args: argparse.Namespace) -> str:
#     """Create the complete prompt for a given instance"""
#     # Initialize prompt manager
#     prompt_manager = PromptManager(
#         prompt_dir=os.path.join(os.path.dirname(__file__), '..', 'util', 'prompts'),
#         agent_skills_docs=LocationToolsRequirement.documentation,
#     )
    
#     # Build messages as in auto_search_main
#     messages = []
    
#     # System message
#     if args.use_function_calling:
#         system_prompt = function_calling.SYSTEM_PROMPT
#     else:
#         system_prompt = prompt_manager.system_message
    
#     messages.append({
#         "role": "system",
#         "content": system_prompt
#     })
    
#     # Add example if requested
#     if args.use_example:
#         messages.append({
#             "role": "user",
#             "content": prompt_manager.initial_user_message
#         })
    
#     # Add task instruction
#     task_instruction = get_task_instruction(
#         instance, 
#         task=args.task,
#         include_pr=True, 
#         include_hint=True
#     )
    
#     messages.append({
#         "role": "user",
#         "content": task_instruction
#     })
    
#     # Convert messages to a single prompt string
#     prompt = ""
#     for msg in messages:
#         role = msg["role"]
#         content = msg["content"]
#         prompt += f"{role.upper()}: {content}\n\n"
    
#     return prompt.strip()

def load_common_data(args) -> Tuple[List[Dict[str, Any]], str, Dict[str, bool], Dict[str, Dict[str, Any]], Optional[Any]]:
    """Load all common data needed for both debug and full dataset creation.
    
    Returns:
        Tuple of (preprocessed_data, loc_file_path, labels, pred_map, ml_client)
    """
    # Initialize MLClient if Azure ML paths are provided
    ml_client = None
    if any(path.startswith("azureml:") for path in [args.loc_file, getattr(args, 'traj_file', '')]):
        ml_client = get_ml_client(
            args.subscription_id,
            args.resource_group,
            args.workspace
        )
    
    # Load dataset using HuggingFaceDataset
    print(f"Loading dataset: {args.dataset}")
    hf_dataset = HuggingFaceDataset(dataset_name=args.dataset, hf_dataset_path=args.dataset)
    hf_dataset.load(split=args.split)
    data = hf_dataset._data
    
    print(f"loading localization results from {args.loc_file}")
    # Resolve file paths (download from Azure ML if needed)
    loc_file_path = load_file_or_data_asset(args.loc_file, ml_client)
    
    # Use LocalizationEvaluator to label results
    evaluator = LocalizationEvaluator()
    print(f"Labeling predictions with k={args.k}")
    labels = evaluator.label_results(
        prediction_file=loc_file_path,
        data=data,
        k=args.k
    )
    
    # Load predictions using the shared utility
    print(f"Loading prediction results from: {loc_file_path}")
    pred_map = load_predictions(args.loc_file, ml_client)
    
    return data, loc_file_path, labels, pred_map, ml_client

def create_debug_dataset(args):
    """Create a debug dataset with minimal information for analysis"""
    # Load common data
    data, loc_file_path, labels, pred_map, _ = load_common_data(args)
    
    # Create debug dataset
    debug_data = []
    for instance in data:
        instance_id = instance['instance_id']
        
        # Extract only the essential prediction fields
        predictions = pred_map.get(instance_id, {})
        pred_summary = {
            'found_files': predictions.get('found_files', []),
            'found_modules': predictions.get('found_modules', []),
            'found_entities': predictions.get('found_entities', [])
        }
        
        debug_entry = {
            'instance_id': instance_id,
            'predictions': pred_summary,
            'ground_truth': {
                'gt_files': instance.get('gt_files', []),
                'gt_modules': instance.get('gt_modules', []),
                'gt_functions': instance.get('gt_functions', [])
            },
            'label': 'success' if labels.get(instance_id, False) else 'failure'
        }
        
        debug_data.append(debug_entry)
    
    # Save and print statistics
    save_dataset_with_stats(debug_data, args.output_file, "debug")

# TODO: update later
# def create_ft_dataset(args):
#     """Create fine-tuning dataset with full information"""
#     # Load common data
#     data, loc_file_path, labels, pred_map, ml_client = load_common_data(args)
    
#     # Load trajectory data
#     traj_file_path = load_predictions(args.traj_file, ml_client)
#     print(f"Loading trajectory data from: {traj_file_path}")
#     traj_map = load_trajectory_data(traj_file_path)
    
#     # Also load raw dataset for additional fields
#     bench_data = load_dataset(args.dataset, split=args.split)
#     bench_data_map = {item['instance_id']: item for item in bench_data}
    
#     # Create fine-tuning dataset
#     ft_data = []
    
#     for instance in tqdm(data, desc="Creating fine-tuning dataset"):
#         instance_id = instance['instance_id']
        
#         # Skip if we don't have trajectory data
#         if instance_id not in traj_map:
#             print(f"Warning: No trajectory data for {instance_id}")
#             continue
        
#         # Get the raw instance for creating prompt
#         raw_instance = bench_data_map.get(instance_id, {})
        
#         # Create prompt
#         prompt = create_prompt_for_instance(raw_instance, args)
        
#         # Get trajectory and results
#         traj_data = traj_map[instance_id]
#         pred_data = pred_map.get(instance_id, {})
        
#         # Build response
#         response_data = {
#             "found_files": pred_data.get("found_files", []),
#             "found_modules": pred_data.get("found_modules", []),
#             "found_entities": pred_data.get("found_entities", []),
#             "raw_output_loc": pred_data.get("raw_output_loc", []),
#             "loc_trajs": traj_data.get("loc_trajs", {})
#         }
        
#         # Create fine-tuning entry
#         ft_entry = {
#             "instance_id": instance_id,
#             "messages": [
#                 {
#                     "role": "user",
#                     "content": prompt
#                 },
#                 {
#                     "role": "assistant",
#                     "content": json.dumps(response_data, indent=2)
#                 }
#             ],
#             "label": "success" if labels.get(instance_id, False) else "failure",
#             "metadata": {
#                 "repo": raw_instance.get("repo", ""),
#                 "base_commit": raw_instance.get("base_commit", ""),
#                 "problem_statement": raw_instance.get("problem_statement", ""),
#                 "k": args.k
#             }
#         }
        
#         ft_data.append(ft_entry)
    
#     # Save and print statistics
#     save_dataset_with_stats(ft_data, args.output_file, "fine-tuning")

def save_dataset_with_stats(data: List[Dict[str, Any]], output_file: str, dataset_type: str):
    """Save dataset and print statistics"""
    print(f"Saving {dataset_type} dataset to: {output_file}")
    
    with open(output_file, 'w') as f:
        for entry in data:
            f.write(json.dumps(entry) + '\n')
    
    # Print statistics
    success_count = sum(1 for entry in data if entry.get('label') == 'success')
    failure_count = len(data) - success_count
    
    print(f"\nDataset Statistics:")
    print(f"Total instances: {len(data)}")
    print(f"Success instances: {success_count}")
    print(f"Failure instances: {failure_count}")
    if len(data) > 0:
        print(f"Success rate: {success_count/len(data)*100:.2f}%")


def main():
    """Main CLI entry point for creating labeled datasets from LocAgent results."""
    parser = argparse.ArgumentParser(
        description="Create labeled datasets from LocAgent evaluation results",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create debug dataset from local file
  python -m IssueLocEval.label_results --debug --dataset princeton-nlp/SWE-bench_Lite --loc_file ./predictions.jsonl

  # Create debug dataset from Azure ML data asset
  python -m IssueLocEval.label_results --debug --dataset princeton-nlp/SWE-bench_Lite --loc_file azureml:predictions:v1

  # Create fine-tuning dataset (when enabled)
  python -m IssueLocEval.label_results --dataset princeton-nlp/SWE-bench_Lite --loc_file ./predictions.jsonl --traj_file ./trajectories.jsonl
        """
    )
    
    # Dataset arguments
    parser.add_argument("--dataset", type=str, required=True,
                        help="HuggingFace dataset path (e.g., princeton-nlp/SWE-bench_Lite)")
    parser.add_argument("--split", type=str, default="test",
                        help="Dataset split to use (default: test)")
    
    # Input files - support both local paths and Azure ML data assets
    parser.add_argument("--loc_file", type=str, required=True,
                        help="Path to loc_output.jsonl file or Azure ML data asset (format: azureml:name:version)")
    parser.add_argument("--traj_file", type=str,
                        help="Path to loc_trajs.jsonl file or Azure ML data asset (format: azureml:name:version)")
    
    # Azure ML arguments (required only if using data assets)
    parser.add_argument("--subscription_id", type=str, default="d0c05057-7972-46ff-9bcf-3c932250155e",
                        help="Azure subscription ID (required for Azure ML data assets)")
    parser.add_argument("--resource_group", type=str, default="SingularityH100",
                        help="Resource group name (required for Azure ML data assets)")
    parser.add_argument("--workspace", type=str, default="H100CentralUS",
                        help="Workspace name (required for Azure ML data assets)")
    
    # Evaluation arguments
    parser.add_argument("--k", type=int, default=5,
                        help="Top-k predictions to consider for labeling (default: 5)")
    
    # Task configuration (for future use with create_ft_dataset)
    parser.add_argument("--task", type=str, default="auto_search",
                        choices=["auto_search", "simple_localize"],
                        help="Task type for prompt generation")
    parser.add_argument("--use_example", action="store_true",
                        help="Include examples in prompt (for fine-tuning dataset)")
    parser.add_argument("--use_function_calling", action="store_true",
                        help="Use function calling format (for fine-tuning dataset)")
    
    # Output options
    parser.add_argument("--output_file", type=str, default="debug_dataset.jsonl",
                        help="Output file for the dataset (default: debug_dataset.jsonl)")
    parser.add_argument("--debug", action="store_true",
                        help="Create debug dataset with minimal information (default mode)")
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.loc_file.startswith("azureml:") and not all([args.subscription_id, args.resource_group, args.workspace]):
        parser.error("Azure ML arguments (--subscription_id, --resource_group, --workspace) are required when using Azure ML data assets")
    
    # Create dataset based on mode
    if args.debug or not args.traj_file:
        # Debug mode (default)
        create_debug_dataset(args)
    else:
        # Fine-tuning mode (currently commented out)
        parser.error("Fine-tuning dataset creation is not yet implemented. Use --debug flag for debug dataset.")
        # create_ft_dataset(args)


if __name__ == "__main__":
    main()

