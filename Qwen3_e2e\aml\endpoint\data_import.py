import json
import os
from azure.ai.ml import MLC<PERSON>
from azure.identity import AzureCliCredential
import pandas as pd
import sys
import tempfile

def load_dataset(config_path: str, pipeline_job_id: str):
    # Step 1: Authenticate and connect to your workspace
    assert os.path.exists(config_path), f"Config file {config_path} not found."
    with open(config_path, "r") as f:
        config = json.load(f)
        subscription = config["subscription_id"]
        resource_group = config["resource_group"]
        workspace = config["workspace_name"]

    credential = AzureCliCredential()
    ml_client = MLClient(credential, subscription_id=subscription, resource_group_name=resource_group, workspace_name=workspace)

    # Step 3: Retrieve child jobs
    child_jobs = ml_client.jobs.list(parent_job_name=pipeline_job_id)

    # Step 4: Iterate over child jobs to find the one with the desired output
    output_name = "score"  # Name of the desired output
    component_name = "BatchScoring"
    child_job_with_output = None

    for job in child_jobs:
        if getattr(job, "properties", {}).get("azureml.moduleName") == component_name:
            child_job_with_output = job
            break

    if child_job_with_output:
        print(f"Found child job with name '{component_name}': {child_job_with_output.name}")
        
        # Step 5: Access the output URI for the desired output
        with tempfile.TemporaryDirectory() as local_download_path:
            ml_client.jobs.download(child_job_with_output.name, download_path=local_download_path, output_name="score")
            df = pd.read_csv(os.path.join(local_download_path, "named-outputs/score/predictions.csv"), names=["input", "output"], sep=" ")
            df["input"] = df["input"].apply(json.loads)
            df["output"] = df["output"].apply(json.loads)
            return df
    else:
        print(f"No child job found with name '{component_name}'.")

if __name__ == "__main__":
    # Example: python data_import.py configs/Instant_apply_training_new.json batchjob-50258e47-9e6a-9d5d-c047-6003f28656ec
    config, pipeline = sys.argv[1:]
    df = load_dataset(config, pipeline)
    print(df)
