import os
import json
import subprocess
import argparse
import sys
from typing import Optional, Union
from azure.ai.ml import MLClient
from azure.ai.ml.entities import ManagedOnlineEndpoint, ManagedOnlineDeployment, CodeConfiguration, ProbeSettings, OnlineRequestSettings, Model, BatchEndpoint, ModelBatchDeployment, ModelBatchDeploymentSettings, BatchRetrySettings
from azure.ai.ml.constants import BatchDeploymentOutputAction
from azure.ai.ml._artifacts._artifact_utilities import download_artifact_from_aml_uri
from azure.core.polling import LROPoller
from azure.identity import AzureCliCredential
from enum import Enum
from pydantic import BaseModel, ConfigDict
import yaml
# from git import Repo
import hashlib


class Resource(Enum):
    ENDPOINT = "endpoint"
    DEPLOYMENT = "deployment"
    BATCH_ENDPOINT = "batch-endpoint"
    BATCH_DEPLOYMENT = "batch-deployment"
    MODEL = "model"
    DOWNLOAD_DATA_ASSET = "download"


class EndpointConfig(BaseModel):
    endpoint_name: str


class CommonDeploymentConfig(EndpointConfig):
    model_config  = ConfigDict(protected_namespaces=())
    deployment_name: str
    model_name: str
    environment_name: str
    quantized: Optional[bool] = False
    tensor_parallel_size: Optional[int] = None


class OnlineEndpointDeploymentConfig(CommonDeploymentConfig):
    instance_type: str
    scoring_script_name: str = "aml_score.py"


class BatchEndpointDeploymentConfig(CommonDeploymentConfig):
    compute_name: str
    scoring_script_name: str = "aml_score_batch.py"


DeploymentConfig = Union[OnlineEndpointDeploymentConfig, BatchEndpointDeploymentConfig]


class ModelConfig(BaseModel):
    model_config  = ConfigDict(protected_namespaces=())
    model_name_or_path: Optional[str] = None
    adapter_name_or_path: Optional[str] = None
    template: Optional[str] = None


class VLLMConfig(BaseModel):
    vllm_backend: Optional[bool] = False
    static_speculation: Optional[bool] = False
    ngram_speculation: Optional[bool] = False
    assistant_speculation: Optional[bool] = False
    speculative_model: Optional[str] = None


class ScoringScriptConfig(ModelConfig, VLLMConfig):
    pass


def hash_file_contents(file_path):
    with open(file_path, "rb") as f:
        file_hash = hashlib.sha256(f.read()).hexdigest()
    return file_hash


def get_tags(env, scoring_script):
    tags = {}

    # Ensure the scoring script exists
    assert os.path.exists(scoring_script), f"Scoring script {scoring_script} not found."
    
    # Initialize a Repo object for the scoring script's directory
    repo = Repo(os.path.abspath(scoring_script), search_parent_directories=True)
    
    # Get the current commit hash
    tags["git_commit"] = repo.head.commit.hexsha
    
    # Append '-dirty' if there are uncommitted changes
    if repo.is_dirty(path=os.path.abspath(scoring_script)):
        tags["git_commit"] += "-dirty"
    
    # Add the file name and file hash to the tags
    tags["file_name"] = os.path.basename(scoring_script)
    tags["file_hash"] = hash_file_contents(scoring_script)
    
    # Add environment variables to the tags if they exist
    if "AZUREML_infer_backend" in env:
        tags["infer_backend"] = env["AZUREML_infer_backend"]
    if "AZUREML_speculative_model" in env:
        tags["speculative_model"] = env["AZUREML_speculative_model"]
    
    return tags


def get_envs(deployment_config: DeploymentConfig, scoring_script_config: ScoringScriptConfig):
    env = {
        "AZUREML_template": scoring_script_config.template,
    }
    if scoring_script_config.vllm_backend:
        env["AZUREML_infer_backend"] = "vllm"
        # This is a requirement for running SD, so we enable it for parity.
        env["AZUREML_use_v2_block_manager"] = True
    if scoring_script_config.static_speculation:
        env["AZUREML_infer_backend"] = "vllm"
        env["AZUREML_speculative_model"] = "[static]"
        env["AZUREML_static_overlap_tokens"] = 5
        env["AZUREML_num_speculative_tokens"] = 32
        env["AZUREML_use_v2_block_manager"] = True
    if scoring_script_config.ngram_speculation:
        env["AZUREML_infer_backend"] = "vllm"
        env["AZUREML_speculative_model"] = "[ngram]"
        env["AZUREML_num_speculative_tokens"] = 32
        env["AZUREML_ngram_prompt_lookup_max"] = 4
        env["AZUREML_use_v2_block_manager"] = True
    if scoring_script_config.assistant_speculation:
        env["AZUREML_infer_backend"] = "vllm"
        env["AZUREML_speculative_model"] = scoring_script_config.speculative_model
        env["AZUREML_num_speculative_tokens"] = 32
        env["AZUREML_use_v2_block_manager"] = True
    if deployment_config.quantized:
        env["AZUREML_quantization"] = "bitsandbytes"
        env["AZUREML_load_format"] = "bitsandbytes"
        env["AZUREML_infer_dtype"] = "bfloat16"
    if deployment_config.tensor_parallel_size is not None:
        env["AZUREML_tensor_parallel_size"] = deployment_config.tensor_parallel_size
    if "HF_TOKEN" in os.environ:
        env["HF_TOKEN"] = os.environ["HF_TOKEN"]
    if scoring_script_config.model_name_or_path is not None:
        env["AZUREML_model_name_or_path"] = scoring_script_config.model_name_or_path
    if scoring_script_config.adapter_name_or_path is not None:
        env["AZUREML_adapter_name_or_path"] = scoring_script_config.adapter_name_or_path
    if deployment_config.model_name is None:
        # Placeholder value that should not be used
        env["AZUREML_MODEL_DIR"] = "/tmp"
    # TODO: env["AZUREML_tensor_parallel_size"] = 8
    for k in env:
        env[k] = str(env[k])
    return env


def run_command(command):
    try:
        result = subprocess.check_output(command, shell=True, text=True)
        print(result)
    except subprocess.CalledProcessError as e:
        print(f"Error: {e.stderr}")


def wait_for_completion(waiter: LROPoller):
    try:
        waiter.wait()
    except KeyboardInterrupt:
        print()
        print("Operation cancelled. Continuation token:",
              waiter.continuation_token())
    else:
        print()
        print("Operation completed successfully. Result:", waiter.result())


def create_endpoint(ml_client: MLClient, validate_only: bool, batch: bool, endpoint_config: EndpointConfig):
    if batch:
        endpoint_cls = BatchEndpoint
        deploy = ml_client.batch_endpoints.begin_create_or_update
        auth_mode = "AADToken"
    else:
        endpoint_cls = ManagedOnlineEndpoint
        deploy = ml_client.online_endpoints.begin_create_or_update
        auth_mode = "key"
    resource = endpoint_cls(
        name=endpoint_config.endpoint_name,
        auth_mode=auth_mode,
    )

    if validate_only:
        print(yaml.dump(dict(resource.dump())))
    else:
        waiter = deploy(resource)
        print("Successfully initiated endpoint creation. Waiting for completion (Ctrl + C to quit)...")
        wait_for_completion(waiter)


def create_or_update_deployment(ml_client: MLClient, validate_only: bool, deployment_config: DeploymentConfig, scoring_script_config: ScoringScriptConfig):
    scoring_script = os.path.join(os.path.dirname(
        __file__), "src", deployment_config.scoring_script_name)
    code_configuration = CodeConfiguration(
        code=os.path.dirname(scoring_script),
        scoring_script=os.path.basename(scoring_script)
    )
    # Add environment variables
    env = get_envs(deployment_config, scoring_script_config)
    tags = get_tags(env, scoring_script)
    # Create the deployment configuration
    if isinstance(deployment_config, OnlineEndpointDeploymentConfig):
        resource = ManagedOnlineDeployment(
            name=deployment_config.deployment_name,
            endpoint_name=deployment_config.endpoint_name,
            model=deployment_config.model_name,
            environment=deployment_config.environment_name,
            instance_type=deployment_config.instance_type,
            code_configuration=code_configuration,
            environment_variables=env,
            tags=tags,
            instance_count=1,
            request_settings=OnlineRequestSettings(
                max_concurrent_requests_per_instance=100,
                request_timeout_ms=180000
            ),
            liveness_probe=ProbeSettings(
                initial_delay=2000,
                period=300,
                timeout=300
            ),
            readiness_probe=ProbeSettings(
                initial_delay=2000,
                period=300,
                timeout=300
            ),
        )
        deploy = ml_client.online_deployments.begin_create_or_update
    elif isinstance(deployment_config, BatchEndpointDeploymentConfig):
        resource = ModelBatchDeployment(
            name=deployment_config.deployment_name,
            endpoint_name=deployment_config.endpoint_name,
            model=deployment_config.model_name,
            environment=deployment_config.environment_name,
            compute=deployment_config.compute_name,
            code_configuration=code_configuration,
            tags=tags,
            settings=ModelBatchDeploymentSettings(
                environment_variables=env,
                instance_count=1,
                max_concurrency_per_instance=1,
                mini_batch_size=1,
                output_action=BatchDeploymentOutputAction.APPEND_ROW,
                output_file_name="predictions.csv",
                retry_settings=BatchRetrySettings(max_retries=1, timeout=600),
                logging_level="info",
            ),
        )
        deploy = ml_client.batch_deployments.begin_create_or_update
    else:
        raise ValueError(f"Invalid deployment configuration {deployment_config}")

    if validate_only:
        print(yaml.dump(dict(resource.dump())))
    else:
        waiter = deploy(resource)
        print("Successfully initiated deployment creation. Waiting for completion (Ctrl + C to quit)...")
        wait_for_completion(waiter)


def register_model(ml_client: MLClient, validate_only: bool, model_name: str, model_path: str, model_type: str, model_description: str):
    resource = Model(
        name=model_name,
        description=model_description,
        type=model_type,
        path=model_path,
    )

    if validate_only:
        print(yaml.dump(resource._to_dict()))
    else:
        result = ml_client.models.create_or_update(resource)
        print("Successfully registered model. Result:", result)


def download_data_asset(ml_client: MLClient, validate_only: bool, data_asset_name: str, data_asset_version: str, destination: str):
    resource = ml_client.data.get(data_asset_name, version=data_asset_version)
    if validate_only:
        d = resource._to_dict()
        del d["creation_context"]
        print(yaml.dump(dict(d)))
    else:
        download_artifact_from_aml_uri(
            uri=resource.path, destination=destination, datastore_operation=ml_client.datastores)


def main():
    parser = argparse.ArgumentParser(description="Manage Azure ML Deployments")
    parser.add_argument("resource", type=Resource, choices=list(
        Resource), help="The resource to act on.")
    parser.add_argument("--validate_only", action="store_true",
                        help="Whether to only validate the configuration.")
    # AML workspace config
    parser.add_argument("--config", help="Azure ML Config.")
    parser.add_argument("--subscription", help="Azure Subscription.")
    parser.add_argument("--resource_group", help="Azure Resource Group.")
    parser.add_argument("--workspace", help="Azure ML Workspace.")
    # Endpoint and deployment settings
    parser.add_argument("--endpoint_name", help="The name of the endpoint.")
    parser.add_argument("--deployment_name",
                        help="The name of the deployment.")
    parser.add_argument("--model_name", help="The name of the model.")
    parser.add_argument("--environment_name",
                        help="The name of the environment.")
    parser.add_argument("--sku", choices=["Standard_NC24ads_A100_v4", "Standard_NC48ads_A100_v4",
                        "Standard_ND96amsr_A100_v4"], help="The type of instance to use for online deployment.")
    parser.add_argument("--cluster_name", help="The name of the instance to use for batch deployment.")
    parser.add_argument("--model_name_or_path",
                        help="The name or path of the model.")
    parser.add_argument("--adapter_name_or_path",
                        help="The name or path of the adapter.")
    parser.add_argument(
        "--template", help="The template to use for the deployment.")
    # Model registration settings
    parser.add_argument(
        "--model_path", help="The path to register for a model.")
    parser.add_argument("--model_description",
                        help="The description of the model.")
    parser.add_argument("--model_type", default="custom_model",
                        help="The type of the registered model.")
    # Data asset download settings
    parser.add_argument("--data_asset_name",
                        help="The name of the data asset.")
    parser.add_argument("--data_asset_destination",
                        help="The destination to download the data asset.")
    parser.add_argument("--data_asset_version", type=str,
                        default="1", help="The version of the data asset.")
    # Static speculation settings
    vllm_group = parser.add_mutually_exclusive_group()
    vllm_group.add_argument("--vllm_backend", action="store_true",
                            help="Whether to use VLLM as the inference backend.")
    vllm_group.add_argument("--static_speculation", action="store_true",
                            help="Whether to use static speculation.")
    vllm_group.add_argument("--ngram_speculation", action="store_true",
                            help="Whether to use ngram speculation.")
    vllm_group.add_argument("--assistant_speculation", action="store_true",
                            help="Whether to use assistant model speculation.")
    parser.add_argument("--speculative_model",
                        help="The model to use for speculation.")
    parser.add_argument("--quantized", action="store_true",
                        help="Whether to load a quantized model.")
    parser.add_argument("--tensor_parallel_size", type=int,
                        help="How many GPUs to use for tensor parallelism.")

    args = parser.parse_args()
    assert args.config or (
        args.subscription and args.resource_group and args.workspace), "Either --config or --subscription, --resource_group, and --workspace must be provided."
    if args.config:
        assert os.path.exists(args.config), f"Config file {args.config} not found."
        assert args.subscription is None and args.resource_group is None and args.workspace is None, "Cannot provide both --config and --subscription, --resource_group, and --workspace."
        with open(args.config, "r") as f:
            config = json.load(f)
            args.subscription = config["subscription_id"]
            args.resource_group = config["resource_group"]
            args.workspace = config["workspace_name"]

    credential = AzureCliCredential()
    ml_client = MLClient(credential, subscription_id=args.subscription,
                         resource_group_name=args.resource_group, workspace_name=args.workspace)

    if args.resource in [Resource.ENDPOINT, Resource.BATCH_ENDPOINT]:
        endpoint_config = EndpointConfig(endpoint_name=args.endpoint_name)
        create_endpoint(ml_client, args.validate_only, args.resource == Resource.BATCH_ENDPOINT, endpoint_config)
    elif args.resource == Resource.MODEL:
        register_model(ml_client, args.validate_only, args.model_name,
                       args.model_path, args.model_type, args.model_description)
    elif args.resource == Resource.DOWNLOAD_DATA_ASSET:
        download_data_asset(ml_client, args.validate_only, args.data_asset_name,
                            args.data_asset_version, args.data_asset_destination)
    elif args.resource in [Resource.DEPLOYMENT, Resource.BATCH_DEPLOYMENT]:
        if args.resource == Resource.BATCH_DEPLOYMENT:
            deployment_config = BatchEndpointDeploymentConfig(
                endpoint_name=args.endpoint_name, deployment_name=args.deployment_name, model_name=args.model_name, environment_name=args.environment_name, compute_name=args.cluster_name, quantized=args.quantized, tensor_parallel_size=args.tensor_parallel_size)
        else:
            deployment_config = OnlineEndpointDeploymentConfig(
                endpoint_name=args.endpoint_name, deployment_name=args.deployment_name, model_name=args.model_name, environment_name=args.environment_name, instance_type=args.sku, quantized=args.quantized, tensor_parallel_size=args.tensor_parallel_size)
        scoring_script_config = ScoringScriptConfig(
            model_name_or_path=args.model_name_or_path, adapter_name_or_path=args.adapter_name_or_path, template=args.template, vllm_backend=args.vllm_backend, static_speculation=args.static_speculation, ngram_speculation=args.ngram_speculation, assistant_speculation=args.assistant_speculation, speculative_model=args.speculative_model)
        create_or_update_deployment(
            ml_client, args.validate_only, deployment_config, scoring_script_config)
    else:
        raise ValueError(f"Resource {args.resource} not supported.")


if __name__ == "__main__":
    main()
