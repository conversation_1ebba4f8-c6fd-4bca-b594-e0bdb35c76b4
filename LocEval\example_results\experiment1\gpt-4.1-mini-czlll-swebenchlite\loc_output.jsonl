{"instance_id": "astropy__astropy-12907", "found_files": ["astropy/modeling/separable.py", "astropy/modeling/compound.py", "compound.py"], "found_modules": ["astropy/modeling/compound.py:CompoundModel"], "found_entities": ["astropy/modeling/separable.py:separable", "astropy/modeling.compound.py:compound", "astropy/modeling/separable.py:separability_matrix", "astropy/modeling/compound.py:CompoundModel.__init__", "astropy/modeling/compound.py:CompoundModel._separability_matrix"], "raw_output_loc": ["FILES:\n- astropy/modeling/separable.py\n- astropy/modeling/compound.py\n\nMODULES:\n- astropy/modeling/separable.py:separable\n- astropy/modeling.compound.py:compound\n\nFUNCTIONS:\n- astropy/modeling/separable.py:separability_matrix\n- astropy/modeling/compound.py:CompoundModel.__init__\n- astropy/modeling/compound.py:CompoundModel._separability_matrix (if exists, or other relevant internal methods handling separability or input/output shapes)"], "meta_data": {}}
{"instance_id": "astropy__astropy-14182", "found_files": ["astropy/io/ascii/rd.py", "astropy/io/ascii/ui.py", "astropy/io/ascii/core.py"], "found_modules": ["astropy/io/ascii/rd.py:RST"], "found_entities": ["astropy/io/ascii/rd.py:RST.__init__", "astropy/io/ascii/rd.py:RST.write", "astropy/io/ascii/ui.py:get_writer", "astropy/io/ascii/core.py:_get_writer"], "raw_output_loc": ["FILES:\n- astropy/io/ascii/rd.py\n- astropy/io/ascii/ui.py\n- astropy/io/ascii/core.py\n\nMODULES:\n- astropy/io/ascii/rd.py:RST\n\nFUNCTIONS:\n- astropy/io/ascii/rd.py:RST.__init__\n- astropy/io/ascii/rd.py:RST.write\n- astropy/io/ascii/ui.py:get_writer\n- astropy/io/ascii/core.py:_get_writer"], "meta_data": {}}
{"instance_id": "astropy__astropy-14365", "found_files": ["astropy/io/ascii/qdp.py"], "found_modules": ["astropy/io/ascii/qdp.py:QDP"], "found_entities": ["astropy/io/ascii/qdp.py:QDP._read_table", "astropy/io/ascii/qdp.py:QDP._parse_qdp_line"], "raw_output_loc": ["FILES:\n- astropy/io/ascii/qdp.py\n\nMODULES:\n- astropy/io/ascii/qdp.py:QDP\n\nFUNCTIONS:\n- astropy/io/ascii/qdp.py:QDP._read_table\n- astropy/io/ascii/qdp.py:QDP._parse_qdp_line"], "meta_data": {}}
{"instance_id": "astropy__astropy-14995", "found_files": ["astropy/nddata/nddata.py"], "found_modules": ["astropy/nddata/nddata.py:NDDataRef"], "found_entities": ["astropy/nddata/nddata.py:NDDataRef._arith_method", "astropy/nddata/nddata.py:NDDataRef.multiply"], "raw_output_loc": ["FILES:\n- astropy/nddata/nddata.py\n\nMODULES:\n- astropy/nddata/nddata.py:NDDataRef\n\nFUNCTIONS:\n- astropy/nddata/nddata.py:NDDataRef._arith_method\n- astropy/nddata/nddata.py:NDDataRef.multiply"], "meta_data": {}}
{"instance_id": "astropy__astropy-6938", "found_files": ["astropy/io/fits/fitsrec.py"], "found_modules": ["astropy/io/fits/fitsrec.py:FITS_rec"], "found_entities": ["astropy/io/fits/fitsrec.py:FITS_rec._format_field_output"], "raw_output_loc": ["FILES:\n- astropy/io/fits/fitsrec.py\n\nMODULES:\n- astropy/io/fits/fitsrec.py:FITS_rec\n\nFUNCTIONS:\n- astropy/io/fits/fitsrec.py:FITS_rec._format_field_output"], "meta_data": {}}
{"instance_id": "astropy__astropy-7746", "found_files": ["astropy/wcs/wcs.py"], "found_modules": ["astropy/wcs/wcs.py:WCS"], "found_entities": ["astropy/wcs/wcs.py:WCS.wcs_pix2world", "astropy/wcs/wcs.py:WCS._array_converter"], "raw_output_loc": ["FILES:\n- astropy/wcs/wcs.py\n\nMODULES:\n- astropy/wcs/wcs.py:WCS\n\nFUNCTIONS:\n- astropy/wcs/wcs.py:WCS.wcs_pix2world\n- astropy/wcs/wcs.py:WCS._array_converter"], "meta_data": {}}
{"instance_id": "django__django-10924", "found_files": ["django/db/models/fields/files.py", "django/db/migrations/serializer.py", "django/db/models/fields/__init__.py"], "found_modules": ["django/db/models/fields/files.py:FilePathField", "django/db/migrations/serializer.py:Serializer"], "found_entities": ["django/db/models/fields/files.py:FilePathField.__init__", "django/db/models/fields/files.py:FilePathField.deconstruct", "django/db/migrations/serializer.py:Serializer.serialize", "django/db/migrations/serializer.py:Serializer._serialize_value"], "raw_output_loc": ["FILES:\n- django/db/models/fields/files.py\n- django/db/migrations/serializer.py\n- django/db/models/fields/__init__.py\n\nMODULES:\n- django/db/models/fields/files.py:FilePathField\n- django/db/migrations/serializer.py:Serializer\n- django/db/models/fields/__init__.py:*\n\nFUNCTIONS:\n- django/db/models/fields/files.py:FilePathField.__init__\n- django/db/models/fields/files.py:FilePathField.deconstruct\n- django/db/migrations/serializer.py:Serializer.serialize\n- django/db/migrations/serializer.py:Serializer._serialize_value"], "meta_data": {}}
{"instance_id": "django__django-11001", "found_files": ["django/db/models/sql/compiler.py"], "found_modules": ["django/db/models/sql/compiler.py:SQLCompiler"], "found_entities": ["django/db/models/sql/compiler.py:SQLCompiler.get_order_by"], "raw_output_loc": ["FILES:\n- django/db/models/sql/compiler.py\n\nMODULES:\n- django/db/models/sql/compiler.py:SQLCompiler\n\nFUNCTIONS:\n- django/db/models/sql/compiler.py:SQLCompiler.get_order_by"], "meta_data": {}}
{"instance_id": "django__django-11019", "found_files": ["django/forms/widgets.py"], "found_modules": ["django/forms/widgets.py:Media"], "found_entities": ["django/forms/widgets.py:Media.__add__", "django/forms/widgets.py:Media._combine", "django/forms/widgets.py:Media._detect_order_conflicts"], "raw_output_loc": ["FILES:\n- django/forms/widgets.py\n\nMODULES:\n- django/forms/widgets.py:Media\n\nFUNCTIONS:\n- django/forms/widgets.py:Media.__add__\n- django/forms/widgets.py:Media._combine\n- django/forms/widgets.py:Media._detect_order_conflicts"], "meta_data": {}}
{"instance_id": "django__django-11039", "found_files": ["django/core/management/commands/sqlmigrate.py", "tests/migrations/test_commands.py"], "found_modules": ["django/core/management/commands/sqlmigrate.py:Command", "tests/migrations/test_commands.py:MigrationCommandsTests"], "found_entities": ["django/core/management/commands/sqlmigrate.py:Command.handle", "tests/migrations/test_commands.py:MigrationCommandsTests.test_sqlmigrate_non_atomic_transaction_behavior"], "raw_output_loc": ["FILES:\n- django/core/management/commands/sqlmigrate.py\n- tests/migrations/test_commands.py\n\nMODULES:\n- django/core/management/commands/sqlmigrate.py:Command\n- tests/migrations/test_commands.py:MigrationCommandsTests\n\nFUNCTIONS:\n- django/core/management/commands/sqlmigrate.py:Command.handle\n- tests/migrations/test_commands.py:MigrationCommandsTests.test_sqlmigrate_non_atomic_transaction_behavior"], "meta_data": {}}
