#!/bin/bash

# 获取命令行参数
image_name="$1"
tag="$2"
registry="$3"
resource_group="$4"
subscription="$5"

# 检查参数是否为空
if [ -z "$image_name" ] || [ -z "$tag" ] || [ -z "$registry" ] || [ -z "$resource_group" ] || [ -z "$subscription" ]; then
    echo "Usage: $0 <image_name> <tag> <registry> <resource_group> <subscription>"
    exit 1
fi

# 出现错误时立即退出脚本
set -e

# 获取脚本所在目录并打印
script_dir=$(dirname "$0")
echo "Script directory: $script_dir"

# 确定Dockerfile路径并打印
dockerfile_dir=$(cd "$script_dir" && pwd)
echo "Dockerfile directory: $dockerfile_dir"

# 切换到Dockerfile目录
cd "$dockerfile_dir"

# 确认Dockerfile存在，若不存在则打印错误信息退出
dockerfile_path="${dockerfile_dir}/Dockerfile"
if [ ! -f "$dockerfile_path" ]; then
    echo "Dockerfile not found at ${dockerfile_path}"
    exit 1
fi

echo "Dockerfile path: $dockerfile_path"

# 登录 Azure并打印过程
echo "Logging into Azure..."
az login


# 正在构建Docker镜像并打印
echo "Building Docker image..."
docker build -f "$dockerfile_path" -t "$registry.azurecr.io/$image_name:$tag" .

# 切换到指定Azure 订阅
# 这里应确保调用脚本有传递 subscription 参数，避免硬编码
# echo "Setting the subscription to $subscription..."
az account set -s "$subscription"

# 登录 Azure 容器注册表并打印
echo "Logging into Azure Container Registry..."
az acr login -n "$registry" -g "$resource_group"

# 推送 Docker 镜像到 Azure 容器注册表并打印
echo "Pushing Docker image to Azure Container Registry..."
docker push "$registry.azurecr.io/$image_name:$tag"

# 成功推送后打印消息
echo "Docker image has been pushed successfully: $registry.azurecr.io/$image_name:$tag"