# Qwen3 FineTuning E2E Pipeline
This folder documents the end-to-end fine-tuning pipeline for the Qwen3 model using Azure Machine Learning. The process involves setting up the environment, executing the training job, running inference, and evaluating the results. Below are the key steps and modifications made to ensure compatibility with the Qwen3 model.

## Environment Setup
One should be able to run the submit_env.py script to set up the remote environment docker image for fine-tuning the Qwen3 model. This has not been tested because the current environment is already set up and runs with the updated submit_singularity.py script (see below). 

## Fine-Tuning Script Modifications
The fine-tuning script is the submit_singularity.py file. To run the fine-tuning job, you need to change a few things in the script:
1. **Code Paths**: Ensure that the paths to the training code is correctly set at line 103.
2. **Output Paths**: line 117.
3. **Hugging Face Token**: Set your Hugging Face token at line 125.
4. **Public SSH Key**: Set your public SSH key at line 155.
5. [optional] **Display Name**: Set the display name for the job at line 100.
6. **Dataset**: Ensure that the dataset and its information is correctly specified in the `dataset_info.json` file.

<details>
<summary><small>Previous implementation details (deprecated)</small></summary>

The modifications on the submit_singularity.py file from the original script are documented in the [submit_singularity_modification.md](submit_singularity_modification.md) file. Key changes include:
- **Environment Setup Workaround**: Explicitly installing the required versions of PEFT and Transformers to ensure compatibility with Qwen3. An updated requirements.txt file is provided to include the additional packages needed.
- **Training Job Configuration**: Adjusting the training job parameters, including code paths, output paths, and environment variables.
- **Supported Datasets**: The script supports specific dataset formats, such as OPENAI format, which is compatible with the Qwen3 model.

If you built the container image using the provided Dockerfile, you can run the fine-tuning job without needing to modify the versions of PEFT and Transformers, as the Dockerfile already includes the necessary installations.

The working version of the submit_singularity.py script is available in the [submit_singularity.py](submit_singularity.py) file.
</details>

## Running the Inference Job
The inference script is the submit_infer.py file, which has been modified to ensure compatibility with the Qwen3 model and to address environment setup issues. Key changes include:
- **Environment Setup Workaround**: Similar to the fine-tuning script, the inference script includes commands to install the correct versions of PEFT and Transformers.
- **Inference Job Configuration**: Adjustments to the inference job parameters, including code paths, output paths, and environment variables.
- **Test Dataset**: You can choose test dataset from /data.

The working version of the submit_infer.py script is available in the [submit_infer.py](submit_infer.py) file.

## Running Evaluation
An example notebook is provided in the evaluate_inference_results.ipynb file, which demonstrates how to evaluate the inference results with the workstream from downloading the inference results to evaluating them using the provided evaluation script.