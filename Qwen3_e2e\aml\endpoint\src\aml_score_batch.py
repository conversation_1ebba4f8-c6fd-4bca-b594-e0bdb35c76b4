import os
import json
import pandas as pd
from inference import init_model, run_example


def init():
    init_model(os.environ)


def retry(raw_data: list[str]):
    init()
    return run(raw_data)


def run(raw_data_files: list[str]) -> list[str]:
    inputs = []
    outputs = []
    for raw_data_file in raw_data_files:
        with open(raw_data_file) as f:
            raw_data = f.read()
        inputs.append(raw_data)
        output = run_example(raw_data)
        outputs.append(output)
    return pd.DataFrame({"input": inputs, "output": outputs})
