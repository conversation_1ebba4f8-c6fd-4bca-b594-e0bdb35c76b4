"""LLM client implementations for various providers."""

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from openai import OpenAI, AzureOpenAI


class BaseLLMClient(ABC):
    """Abstract base class for LLM clients."""
    
    @abstractmethod
    def generate(self, prompt: str, temperature: float = 0.1, 
                max_tokens: int = 500, **kwargs) -> str:
        """Generate text from prompt.
        
        Args:
            prompt: Input prompt
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional provider-specific arguments
            
        Returns:
            Generated text
        """
        pass


class OpenAIClient(BaseLLMClient):
    """OpenAI API client."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4"):
        """Initialize OpenAI client.
        
        Args:
            api_key: OpenAI API key (uses env var if not provided)
            model: Model name
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key not provided")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
    
    def generate(self, prompt: str, temperature: float = 0.1, 
                max_tokens: int = 500, **kwargs) -> str:
        """Generate text using OpenAI API."""
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "You are an expert software engineer who can predict issue locations based on descriptions."},
                {"role": "user", "content": prompt}
            ],
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        return response.choices[0].message.content


class AzureOpenAIClient(BaseLLMClient):
    """Azure OpenAI API client."""
    
    def __init__(self, endpoint: Optional[str] = None, api_key: Optional[str] = None,
                 api_version: Optional[str] = None, deployment: Optional[str] = None):
        """Initialize Azure OpenAI client.
        
        Args:
            endpoint: Azure endpoint (uses env var if not provided)
            api_key: Azure API key (uses env var if not provided)
            api_version: API version (uses env var if not provided)
            deployment: Deployment name (uses env var if not provided)
        """
        self.endpoint = endpoint or os.getenv("AZURE_OPENAI_ENDPOINT")
        self.api_key = api_key or os.getenv("AZURE_OPENAI_API_KEY")
        self.api_version = api_version or os.getenv("AZURE_OPENAI_API_VERSION")
        self.deployment = deployment or os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4.1-mini")  # Updated default
        
        if not all([self.endpoint, self.api_key, self.api_version]):
            raise ValueError("Azure OpenAI endpoint, API key, and API version must be provided")
        
        self.client = AzureOpenAI(
            azure_endpoint=self.endpoint,
            api_key=self.api_key,
            api_version=self.api_version
        )
    
    def generate(self, prompt: str, temperature: float = 0.1, 
                max_tokens: int = 500, **kwargs) -> str:
        """Generate text using Azure OpenAI API."""
        # response = self.client.chat.completions.create(
        #     model=self.deployment,
        #     messages=[
        #         {"role": "system", "content": "You are an expert software engineer who can predict issue locations based on descriptions."},
        #         {"role": "user", "content": prompt}
        #     ],
        #     temperature=temperature,
        #     max_tokens=max_tokens,
        #     **kwargs
        # )
        # return response.choices[0].message.content
        messages = [
                {"role": "system", "content": "You are an expert software engineer who can predict issue locations based on descriptions."},
                {"role": "user", "content": prompt}
            ]
        kwargs = {
            "model": self.deployment,
            "messages": messages,
        }

        # Use appropriate parameter name based on model
        if self.deployment and "o4" in self.deployment.lower():
            kwargs["max_completion_tokens"] = max_tokens
            kwargs["temperature"] = 1.0
        else:
            kwargs["max_tokens"] = max_tokens
            kwargs["temperature"] = temperature

        response = self.client.chat.completions.create(**kwargs)

        return response.choices[0].message.content


class AnthropicClient(BaseLLMClient):
    """Anthropic Claude API client."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "claude-3-opus-20240229"):
        """Initialize Anthropic client.
        
        Args:
            api_key: Anthropic API key (uses env var if not provided)
            model: Model name
        """
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            raise ValueError("Anthropic API key not provided")
        
        try:
            import anthropic
            self.client = anthropic.Anthropic(api_key=self.api_key)
            self.model = model
        except ImportError:
            raise ImportError("anthropic package not installed. Run: pip install anthropic")
    
    def generate(self, prompt: str, temperature: float = 0.1, 
                max_tokens: int = 500, **kwargs) -> str:
        """Generate text using Anthropic API."""
        message = self.client.messages.create(
            model=self.model,
            max_tokens=max_tokens,
            temperature=temperature,
            system="You are an expert software engineer who can predict issue locations based on descriptions.",
            messages=[
                {"role": "user", "content": prompt}
            ],
            **kwargs
        )
        return message.content[0].text


class LLMClientFactory:
    """Factory for creating LLM clients."""
    
    # Model registry with client type and configuration
    _model_registry = {
        # OpenAI models
        "gpt-4": {"client": "openai", "model": "gpt-4"},
        "gpt-4-turbo": {"client": "openai", "model": "gpt-4-turbo"},
        "gpt-3.5-turbo": {"client": "openai", "model": "gpt-3.5-turbo"},
        
        # Azure models (using deployment names)
        "gpt-4.1-mini": {"client": "azure", "deployment": "gpt-4.1-mini"},
        "gpt-4.1-swc": {"client": "azure", "deployment": "gpt-4.1-swc"},
        "o4-mini": {"client": "azure", "deployment": "o4-mini"},
        "gpt-4-azure": {"client": "azure", "deployment": "gpt-4"},
        "gpt-35-turbo-azure": {"client": "azure", "deployment": "gpt-35-turbo"},
        
        # Anthropic models
        "claude-3-opus": {"client": "anthropic", "model": "claude-3-opus-20240229"},
        "claude-3-sonnet": {"client": "anthropic", "model": "claude-3-sonnet-20240229"},
        "claude-3-haiku": {"client": "anthropic", "model": "claude-3-haiku-20240307"},
    }
    
    @classmethod
    def create(cls, model_name: str, config: Optional[Dict[str, Any]] = None) -> BaseLLMClient:
        """Create an LLM client based on model name.
        
        Args:
            model_name: Name of the model (must be in registry)
            config: Optional configuration dictionary
            
        Returns:
            LLM client instance
        """
        if model_name not in cls._model_registry:
            available = list(cls._model_registry.keys())
            raise ValueError(f"Model '{model_name}' not found. Available models: {available}")
        
        model_info = cls._model_registry[model_name]
        config = config or {}
        
        # Create appropriate client based on type
        client_type = model_info["client"]
        
        if client_type == "openai":
            return OpenAIClient(
                api_key=config.get("api_key"),
                model=model_info["model"]
            )
        
        elif client_type == "azure":
            return AzureOpenAIClient(
                endpoint=config.get("endpoint"),
                api_key=config.get("api_key"),
                api_version=config.get("api_version"),
                deployment=model_info.get("deployment", model_name)
            )
        
        elif client_type == "anthropic":
            return AnthropicClient(
                api_key=config.get("api_key"),
                model=model_info["model"]
            )
        
        else:
            raise ValueError(f"Unknown client type: {client_type}")
    
    @classmethod
    def list_models(cls) -> List[str]:
        """List all available models."""
        return list(cls._model_registry.keys())
    
    @classmethod
    def register_model(cls, name: str, client_type: str, **kwargs) -> None:
        """Register a new model.
        
        Args:
            name: Model name to use in CLI
            client_type: Client type ('openai', 'azure', 'anthropic')
            **kwargs: Additional model configuration
        """
        cls._model_registry[name] = {"client": client_type, **kwargs}
