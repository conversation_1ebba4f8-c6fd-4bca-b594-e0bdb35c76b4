# Attach to mlclient
from azure.ai.ml import <PERSON><PERSON><PERSON>
from azure.identity import AzureCliCredential
import os
from awq import AutoAWQForCausalLM
from transformers import AutoTokenizer
import sys

if __name__ == "__main__":
    model_path = sys.argv[1]
    quant_path = sys.argv[2]
    quant_config = { "zero_point": True, "q_group_size": 128, "w_bit": 4, "version": "GEMM" }

    # Navigate through nested directories
    if len(os.listdir(model_path)) == 1:
        model_path = os.path.join(model_path, os.listdir(model_path)[0])
        print("Navigate", model_path)
    # Load model
    model = AutoAWQForCausalLM.from_pretrained(
        model_path, **{"low_cpu_mem_usage": True, "use_cache": False}
    )
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

    # Quantize
    model.quantize(tokenizer, quant_config=quant_config)

    # Save quantized model
    os.makedirs(os.path.dirname(quant_path), exist_ok=True)
    model.save_quantized(quant_path)
    tokenizer.save_pretrained(quant_path)

    print(f'Model is quantized and saved at "{quant_path}"')

