import json
import pandas as pd
import os

os.makedirs("pr_data", exist_ok=True)

datas = []
with open("pr_data_ia_vs_gpt4o_dec_16_data_llm2step.jsonl") as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        data = json.loads(line)
        prompt = data['instruction']
        speculation = data['file_content_before_pr']
        expected_output = data['output']
        data = {
            "model": "",
            "messages": [{"role": "user", "content": prompt}],
            "speculation": speculation,
            "temperature": 0.0,
            "top_p": 1.0,
            "max_tokens": 10000,
        }
        with open(f"pr_data/example_{str(i).rjust(len(str(len(lines))), '0')}.json", "w") as of:
            json.dump(data, of)
