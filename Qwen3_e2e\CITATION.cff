cff-version: 1.2.0
date-released: 2024-03
message: "If you use this software, please cite it as below."
authors:
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON>"
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON>"
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON>"
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON>"
- family-names: "<PERSON><PERSON>"
  given-names: "<PERSON><PERSON><PERSON>"
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON>"
- family-names: "<PERSON>"
  given-names: "<PERSON><PERSON><PERSON>"
title: "LlamaFactory: Unified Efficient Fine-Tuning of 100+ Language Models"
url: "https://arxiv.org/abs/2403.13372"
preferred-citation:
  type: conference-paper
  conference:
    name: "Proceedings of the 62nd Annual Meeting of the Association for Computational Linguistics (Volume 3: System Demonstrations)"
  authors:
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON>"
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON>"
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON>"
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON>"
    - family-names: "<PERSON><PERSON>"
      given-names: "<PERSON><PERSON><PERSON>"
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON>"
    - family-names: "<PERSON>"
      given-names: "<PERSON><PERSON><PERSON>"
  title: "LlamaFactory: Unified Efficient Fine-Tuning of 100+ Language Models"
  url: "https://arxiv.org/abs/2403.13372"
  year: 2024
  publisher: "Association for Computational Linguistics"
  address: "Bangkok, Thailand"
