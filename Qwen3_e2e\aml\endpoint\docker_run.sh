docker run \
    -v "/home/<USER>/.cache/huggingface:/root/.cache/huggingface" \
    -v "$PWD/src:/endpoint" -w /endpoint \
    -e 'AZUREML_template=qwen' \
    -e 'AZUREML_model_name_or_path=Qwen/Qwen2.5-Coder-0.5B-Instruct' \
    -e 'AZUREML_MODEL_DIR=/endpoint' \
    -e 'AZUREML_infer_backend=vllm' \
    -e 'AZUREML_speculative_model=[static]' \
    -e 'AZUREML_static_overlap_tokens=5' \
    -e 'AZUREML_num_speculative_tokens=32' \
    -e 'AZUREML_use_v2_block_manager=True' \
    --network=host -p 8000:8000 \
    --rm \
    --gpus all \
    -it 1e6d60a2ac124c168aa42090bc1ecc97.azurecr.io/fastapply:vllm-sd bash -c 'python aml_score.py'
