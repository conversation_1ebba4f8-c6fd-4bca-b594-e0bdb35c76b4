{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d2546043", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "b1d6d1c1", "metadata": {}, "outputs": [], "source": ["summary_stats = pd.read_csv('./results/registered_datasets_stats.csv')\n", "# switch rows and columns\n", "summary_stats = summary_stats.set_index('dataset').T\n", "# remove the first row (which is just the dataset names)\n", "summary_stats = summary_stats.iloc[:-2]\n", "summary_stats"]}, {"cell_type": "markdown", "id": "42aeedfe", "metadata": {}, "source": ["# load data"]}, {"cell_type": "code", "execution_count": null, "id": "c4b9c0e0", "metadata": {}, "outputs": [], "source": ["# Specify experiments and load corresponding results\n", "experiments = ['gpt-4_1-mini_czlll_SWE-bench_Lite_simple_filtered', \n", "               'gpt-4_1-mini_princeton-nlp_SWE-bench_Verified_simple_filtered', \n", "               'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered',\n", "               'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered',\n", "               'gpt-4_1-swc_czlll_SWE-bench_Lite_simple_filtered',\n", "               'gpt-4_1-swc_princeton-nlp_SWE-bench_Verified_simple_filtered',\n", "               'gpt-4_1-mini_LogicStar_SWA-Bench_simple_filtered_after_20240630',\n", "                'gpt-4_1-swc_LogicStar_SWA-Bench_simple_filtered_after_20240630',\n", "\n", "               ]\n", "\n", "# map experiment names to their corresponding datasets and models\n", "experiment_mapping = {\n", "    'gpt-4_1-mini_czlll_SWE-bench_Lite_simple_filtered': ('SWE-bench_Lite', 'gpt-4_1-mini'),\n", "    'gpt-4_1-mini_princeton-nlp_SWE-bench_Verified_simple_filtered': ('SWE-bench_Verified', 'gpt-4_1-mini'),\n", "    'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered': ('Loc-Bench_V1', 'gpt-4_1-mini'),\n", "    'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered': ('Loc-Bench_V1', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_czlll_SWE-bench_Lite_simple_filtered': ('SWE-bench_Lite', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_princeton-nlp_SWE-bench_Verified_simple_filtered': ('SWE-bench_Verified', 'gpt-4_1-swc')\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "db8abcc0", "metadata": {}, "outputs": [], "source": ["# for each unique dataset, load results of different models and store in a dictionary\n", "results = {}\n", "for experiment in experiments:\n", "    dataset, model = experiment_mapping[experiment]\n", "    if dataset not in results:\n", "        results[dataset] = {}\n", "    # load the results\n", "    df = pd.read_csv(f'./results/experiments/{experiment}/loc_output.eval.csv', header=[0, 1])\n", "    # store the results in the dictionary\n", "    results[dataset][model] = df"]}, {"cell_type": "markdown", "id": "e47b6bb6", "metadata": {}, "source": ["# Accuracy and etc."]}, {"cell_type": "code", "execution_count": null, "id": "50337b8b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import json\n", "from tabulate import tabulate\n", "\n", "def create_comprehensive_table(models_dict, dataset_name, metric_type='acc'):\n", "    \"\"\"\n", "    Create a comprehensive table with accuracy metrics, token usage, time, and cost.\n", "    \n", "    Args:\n", "        models_dict: Dictionary of {model_name: dataframe}\n", "        dataset_name: Name of the dataset\n", "        metric_type: Type of metric ('acc', 'precision', 'recall', 'ndcg', 'map')\n", "    \"\"\"\n", "    # Define which k values to show\n", "    k_values = [1, 3, 5, 10]\n", "    granularities = ['file', 'function']  # Simplified to just file and function\n", "    \n", "    print(f\"\\n{'='*120}\")\n", "    print(f\"{dataset_name} - Comprehensive Results\".center(120))\n", "    print(f\"{'='*120}\")\n", "    \n", "    for granularity in granularities:\n", "        print(f\"\\n{granularity.upper()} Level\")\n", "        print(\"-\" * 120)\n", "        \n", "        # Prepare data\n", "        table_data = []\n", "        \n", "        for model_name, df in models_dict.items():\n", "            row = [model_name]\n", "            \n", "            # Add metric values\n", "            for k in k_values:\n", "                metric_name = f'{metric_type}@{k}'\n", "                if (granularity, metric_name) in df.columns:\n", "                    value = df[(granularity, metric_name)].iloc[0]\n", "                    row.append(f\"{value:.3f}\")\n", "                else:\n", "                    row.append(\"N/A\")\n", "            \n", "            # Add usage (tokens), time, and cost\n", "            avg_prompt_tokens = df[('metrics', 'avg_prompt_tokens')].iloc[0] if ('metrics', 'avg_prompt_tokens') in df.columns else np.nan\n", "            avg_completion_tokens = df[('metrics', 'avg_completion_tokens')].iloc[0] if ('metrics', 'avg_completion_tokens') in df.columns else np.nan\n", "            avg_time = df[('metrics', 'avg_time')].iloc[0] if ('metrics', 'avg_time') in df.columns else np.nan\n", "            # avg_cost = df[('metrics', 'avg_usage_cost')].iloc[0] if ('metrics', 'avg_usage_cost') in df.columns else np.nan\n", "            \n", "            # Format the values\n", "            prompt_tokens_str = f\"{avg_prompt_tokens:,.0f}\" if not pd.isna(avg_prompt_tokens) else \"N/A\"\n", "            completion_tokens_str = f\"{avg_completion_tokens:,.0f}\" if not pd.isna(avg_completion_tokens) else \"N/A\"\n", "            time_str = f\"{avg_time:.2f}\" if not pd.isna(avg_time) else \"N/A\"\n", "            # cost_str = f\"${avg_cost:.4f}\" if not pd.isna(avg_cost) else \"N/A\"\n", "\n", "            row.extend([prompt_tokens_str, completion_tokens_str, time_str])\n", "            table_data.append(row)\n", "        \n", "        # Create headers\n", "        headers = ['Model'] + [f'{metric_type}@{k}' for k in k_values] + ['Avg Prompt Tokens', 'Avg Completion Tokens', 'Avg Time (s)']\n", "\n", "        # Print table\n", "        print(tabulate(table_data, headers=headers, tablefmt='simple', numalign='right'))\n", "\n", "def create_latex_comprehensive_table(models_dict, dataset_name, metric_type='acc'):\n", "    \"\"\"\n", "    Create a LaTeX version of the comprehensive table.\n", "    \n", "    Args:\n", "        models_dict: Dictionary of {model_name: dataframe}\n", "        dataset_name: Name of the dataset\n", "        metric_type: Type of metric ('acc', 'precision', 'recall', 'ndcg', 'map')\n", "    \"\"\"\n", "    k_values = [1, 3, 5, 10]\n", "    granularities = ['file', 'function']\n", "    \n", "    print(f\"\\n% {dataset_name} - Comprehensive Results\")\n", "    \n", "    for granularity in granularities:\n", "        print(f\"\\n% {granularity.upper()} Level\")\n", "        print(\"\\\\begin{table}[h]\")\n", "        print(\"\\\\centering\")\n", "        print(f\"\\\\caption{{{dataset_name} - {granularity.capitalize()} Level Results}}\")\n", "        print(\"\\\\begin{tabular}{lrrrrrrr}\")\n", "        print(\"\\\\toprule\")\n", "        \n", "        # Headers\n", "        header = \"Model\"\n", "        for k in k_values:\n", "            header += f\" & ${metric_type}@{k}$\"\n", "        header += \" & Avg Prompt Tokens & Avg Completion Tokens & Time (s) \\\\\\\\\"\n", "        print(header)\n", "        print(\"\\\\midrule\")\n", "        \n", "        # Data rows\n", "        for model_name, df in models_dict.items():\n", "            latex_model_name = model_name.replace('_', '\\\\_')\n", "            row = [latex_model_name]\n", "            \n", "            # Add metric values\n", "            for k in k_values:\n", "                metric_name = f'{metric_type}@{k}'\n", "                if (granularity, metric_name) in df.columns:\n", "                    value = df[(granularity, metric_name)].iloc[0]\n", "                    row.append(f\"{value:.3f}\")\n", "                else:\n", "                    row.append(\"--\")\n", "            \n", "            # Add usage, time, and cost\n", "            avg_prompt_tokens = df[('metrics', 'avg_prompt_tokens')].iloc[0] if ('metrics', 'avg_prompt_tokens') in df.columns else np.nan\n", "            avg_completion_tokens = df[('metrics', 'avg_completion_tokens')].iloc[0] if ('metrics', 'avg_completion_tokens') in df.columns else np.nan\n", "            avg_time = df[('metrics', 'avg_time')].iloc[0] if ('metrics', 'avg_time') in df.columns else np.nan\n", "\n", "            prompt_tokens_str = f\"{avg_prompt_tokens:,.0f}\" if not pd.isna(avg_prompt_tokens) else \"--\"\n", "            completion_tokens_str = f\"{avg_completion_tokens:,.0f}\" if not pd.isna(avg_completion_tokens) else \"--\"\n", "            time_str = f\"{avg_time:.2f}\" if not pd.isna(avg_time) else \"--\"\n", "\n", "            row.extend([prompt_tokens_str, completion_tokens_str, time_str])\n", "\n", "            print(\" & \".join(row) + \" \\\\\\\\\")\n", "        \n", "        print(\"\\\\bottomrule\")\n", "        print(\"\\\\end{tabular}\")\n", "        print(f\"\\\\label{{tab:{dataset_name.lower().replace(' ', '_').replace('-', '_')}_{granularity}_{metric_type}}}\")\n", "        print(\"\\\\end{table}\")\n", "\n", "# Updated data loading section to include token count\n", "def load_results_with_usage(experiments, experiment_mapping):\n", "    \"\"\"\n", "    Load results with usage statistics from loc_trajs.jsonl files.\n", "    \"\"\"\n", "    results = {}\n", "    \n", "    for experiment in experiments:\n", "        dataset, model = experiment_mapping[experiment]\n", "        if dataset not in results:\n", "            results[dataset] = {}\n", "        \n", "        # Load the evaluation results\n", "        df = pd.read_csv(f'./results/experiments/{experiment}/loc_output.eval.csv', header=[0, 1])\n", "        \n", "        # Load the corresponding loc_trajs.jsonl file\n", "        loc_trajs_path = f'./results/experiments/{experiment}/loc_trajs.jsonl'\n", "        \n", "        # Initialize lists to store usage and time values\n", "        # usage_costs = []\n", "        prompt_tokens = []\n", "        completion_tokens = []\n", "        times = []\n", "        \n", "        try:\n", "            with open(loc_trajs_path, 'r') as f:\n", "                for line in f:\n", "                    if not line.strip():\n", "                        continue\n", "                    instance_data = json.loads(line.strip())\n", "                    \n", "                    # Extract cost\n", "                    # if 'usage' in instance_data and 'cost' in instance_data['usage']:\n", "                    #     usage_costs.append(instance_data['usage']['cost'])\n", "                    \n", "                    # Extract total tokens\n", "                    if 'usage' in instance_data and 'prompt_tokens' in instance_data['usage']:\n", "                        prompt_tokens.append(instance_data['usage']['prompt_tokens'])\n", "                    \n", "                    if 'usage' in instance_data and 'completion_tokens' in instance_data['usage']:\n", "                        completion_tokens.append(instance_data['usage']['completion_tokens'])\n", "\n", "                    # Extract time\n", "                    if 'time' in instance_data:\n", "                        times.append(instance_data['time'])\n", "                        \n", "        except FileNotFoundError:\n", "            print(f\"Warning: loc_trajs.jsonl not found for {experiment}\")\n", "            # usage_costs = [np.nan]\n", "            prompt_tokens = [np.nan]\n", "            completion_tokens = [np.nan]\n", "            times = [np.nan]\n", "        except Exception as e:\n", "            print(f\"Error processing {experiment}: {e}\")\n", "            prompt_tokens = [np.nan]\n", "            completion_tokens = [np.nan]\n", "            times = [np.nan]\n", "        \n", "        # Calculate averages\n", "        avg_prompt_tokens = np.mean(prompt_tokens) if prompt_tokens else np.nan\n", "        avg_completion_tokens = np.mean(completion_tokens) if completion_tokens else np.nan\n", "        avg_time = np.mean(times) if times else np.nan\n", "        \n", "        # Add the averages as new columns to the dataframe\n", "        df[('metrics', 'avg_prompt_tokens')] = avg_prompt_tokens\n", "        df[('metrics', 'avg_completion_tokens')] = avg_completion_tokens\n", "        df[('metrics', 'avg_time')] = avg_time\n", "        \n", "        # Store the results in the dictionary\n", "        results[dataset][model] = df\n", "        \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "16ef3766", "metadata": {}, "outputs": [], "source": ["# Usage example:\n", "# First, load the data with the updated function\n", "experiments = ['gpt-4_1-mini_czlll_SWE-bench_Lite_simple_filtered', \n", "               'gpt-4_1-mini_princeton-nlp_SWE-bench_Verified_simple_filtered', \n", "               'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered',\n", "               'gpt-4_1-mini_LogicStar_SWA-Bench_simple_filtered',\n", "               'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered',\n", "               'gpt-4_1-swc_czlll_SWE-bench_Lite_simple_filtered',\n", "               'gpt-4_1-swc_princeton-nlp_SWE-bench_Verified_simple_filtered',\n", "               'gpt-4_1-swc_LogicStar_SWA-Bench_simple_filtered']\n", "\n", "\n", "experiment_mapping = {\n", "    'gpt-4_1-mini_czlll_SWE-bench_Lite_simple_filtered': ('SWE-bench_Lite', 'gpt-4_1-mini'),\n", "    'gpt-4_1-mini_princeton-nlp_SWE-bench_Verified_simple_filtered': ('SWE-bench_Verified', 'gpt-4_1-mini'),\n", "    'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered': ('Loc-Bench_V1', 'gpt-4_1-mini'),\n", "    'gpt-4_1-mini_LogicStar_SWA-Bench_simple_filtered': ('SWA-Bench', 'gpt-4_1-mini'),\n", "    'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered': ('Loc-Bench_V1', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_czlll_SWE-bench_Lite_simple_filtered': ('SWE-bench_Lite', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_princeton-nlp_SWE-bench_Verified_simple_filtered': ('SWE-bench_Verified', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_LogicStar_SWA-Bench_simple_filtered': ('SWA-Bench', 'gpt-4_1-swc')\n", "}\n", "\n", "# Load results with usage data\n", "results = load_results_with_usage(experiments, experiment_mapping)\n", "\n", "# Generate comprehensive tables for all datasets\n", "print(\"\\n\" + \"=\"*120)\n", "print(\"COMPREHENSIVE RESULTS - ALL DATASETS\".center(120))\n", "print(\"=\"*120)\n", "\n", "for dataset, models in results.items():\n", "    create_comprehensive_table(models, dataset, metric_type='acc')\n", "\n", "# Generate LaTeX tables\n", "print(\"\\n\\n% LaTeX Output\")\n", "print(\"% Include \\\\usepackage{booktabs} in your preamble\")\n", "\n", "for dataset, models in results.items():\n", "    create_latex_comprehensive_table(models, dataset, metric_type='acc')"]}, {"cell_type": "code", "execution_count": null, "id": "c9b4b9de", "metadata": {}, "outputs": [], "source": ["# Usage example:\n", "# First, load the data with the updated function\n", "experiments = ['gpt-4_1-mini_LogicStar_SWA-Bench_simple_filtered_after_20240630',\n", "               'gpt-4_1-swc_LogicStar_SWA-Bench_simple_filtered_after_20240630',\n", "               'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered_after_20240630',\n", "               'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered_after_20240630']\n", "\n", "\n", "experiment_mapping = {\n", "    'gpt-4_1-mini_LogicStar_SWA-Bench_simple_filtered_after_20240630': ('SWA-Bench', 'gpt-4_1-mini'),\n", "    'gpt-4_1-swc_LogicStar_SWA-Bench_simple_filtered_after_20240630': ('SWA-Bench', 'gpt-4_1-swc'),\n", "    'gpt-4_1-swc_czlll_Loc-Bench_V1_simple_filtered_after_20240630': ('Loc-Bench_V1', 'gpt-4_1-swc'),\n", "    'gpt-4_1-mini_czlll_Loc-Bench_V1_simple_filtered_after_20240630': ('Loc-Bench_V1', 'gpt-4_1-mini')\n", "}\n", "\n", "# Load results with usage data\n", "results = load_results_with_usage(experiments, experiment_mapping)\n", "\n", "# Generate comprehensive tables for all datasets\n", "print(\"\\n\" + \"=\"*120)\n", "print(\"COMPREHENSIVE RESULTS - ALL DATASETS\".center(120))\n", "print(\"=\"*120)\n", "\n", "for dataset, models in results.items():\n", "    create_comprehensive_table(models, dataset, metric_type='acc')\n", "\n", "# Generate LaTeX tables\n", "print(\"\\n\\n% LaTeX Output\")\n", "print(\"% Include \\\\usepackage{booktabs} in your preamble\")\n", "\n", "for dataset, models in results.items():\n", "    create_latex_comprehensive_table(models, dataset, metric_type='acc')"]}, {"cell_type": "markdown", "id": "e8e5566d", "metadata": {}, "source": ["# Visualize Trajs"]}, {"cell_type": "code", "execution_count": 2, "id": "06a9c645", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "TRAJECTORY VISUALIZATION\n", "================================================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\loceval\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📥 Loading trajectories from source...\n", "💾 Cached 273 trajectories\n", "✅ Saved visualization to: c:\\Users\\<USER>\\Documents\\Repos\\OSS\\LocEval\\debug\\astropy__astropy-12907_azure.html\n"]}], "source": ["# Trajectory Visualization\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"TRAJECTORY VISUALIZATION\")\n", "print(\"=\"*80)\n", "\n", "import sys\n", "sys.path.append('./IssueLocEval')\n", "\n", "from IssueLocEval.utils.traj_visualizer import TrajectoryVisualizer, visualize_trajectory_from_azure\n", "from IssueLocEval.utils.file_loader import load_trajectory_data, get_ml_client\n", "\n", "INSTANCE_ID = \"astropy__astropy-12907\"  # Replace with your instance ID\n", "\n", "# Option 1: Load from local file\n", "\"\"\"\n", "EXPERIMENT = \"gpt-4_1-mini_czlll_SWE-bench_Lite_simple_filtered\"  # Replace with your experiment\n", "# Local visualization from experiment results\n", "print(f\"\\n📊 Visualizing trajectory for instance: {INSTANCE_ID}\")\n", "print(f\"📁 From experiment: {EXPERIMENT}\")\n", "\n", "trajectory_file = f'./results/experiments/{EXPERIMENT}/loc_trajs.jsonl'\n", "viz = TrajectoryVisualizer(trajectory_path=trajectory_file)\n", "\n", "try:\n", "    viz.load_trajectory(instance_id=INSTANCE_ID)\n", "    viz.visualize(\n", "        show_full_content=False,\n", "        max_content_length=500,\n", "        save_to_file=f\"{INSTANCE_ID.replace('/', '_')}.html\"\n", "    )\n", "    print(f\"✅ Visualization saved to debug/{INSTANCE_ID.replace('/', '_')}.html\")\n", "except ValueError as e:\n", "    print(f\"❌ Error: {e}\")\n", "    print(f\"   Available instances can be found in: {trajectory_file}\")\n", "\"\"\"\n", "\n", "# Option 2: Load from Azure ML (uncomment and configure if needed)\n", "\n", "# Azure ML configuration\n", "SUBSCRIPTION_ID = \"d0c05057-7972-46ff-9bcf-3c932250155e\"\n", "RESOURCE_GROUP = \"SingularityH100\"\n", "WORKSPACE = \"H100CentralUS\"\n", "TRAJECTORY_PATH = \"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/SingularityH100/workspaces/H100CentralUS/datastores/workspaceblobstore/paths/LocalUpload/1833884ca3ad291a39b355b6ff437326/czSWEBenchLite/GPT4-1-mini/loc_trajs.jsonl\"  # or full URI\n", "\n", "# Debug dataset path (local file)\n", "DEBUG_DATASET_PATH = \"debug/swebenchlite_gpt41mini_debug_dataset.json\"  # Adjust filename as needed\n", "\n", "# Visualize from Azure ML\n", "viz = visualize_trajectory_from_azure(\n", "    instance_id=INSTANCE_ID,\n", "    trajectory_path=TRAJECTORY_PATH,\n", "    subscription_id=SUBSCRIPTION_ID,\n", "    resource_group=RESOURCE_GROUP,\n", "    workspace=WORKSPACE,\n", "    save_to_file=f\"{INSTANCE_ID.replace('/', '_')}_azure.html\",\n", "    debug_dataset_path=DEBUG_DATASET_PATH,  # Add this parameter\n", "    use_cache=True  # Enable caching\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8682a2f7", "metadata": {}, "outputs": [], "source": ["# clear cache\n"]}], "metadata": {"kernelspec": {"display_name": "loceval", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}