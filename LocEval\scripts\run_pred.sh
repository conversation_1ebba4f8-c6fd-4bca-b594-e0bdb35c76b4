#!/bin/bash

# Get access token using PowerShell for JSON parsing
echo "Getting Azure access token..."
accessToken=$(az account get-access-token --scope https://cognitiveservices.azure.com/.default --query accessToken -o tsv)

if [ -z "$accessToken" ]; then
    echo "❌ Failed to get access token"
    exit 1
else
    echo "✅ Token obtained successfully"
fi

# Set Azure OpenAI environment variables
export AZURE_OPENAI_ENDPOINT="https://mass-swc.openai.azure.com/"
export AZURE_OPENAI_API_KEY="$accessToken"
export AZURE_OPENAI_API_VERSION="2025-01-01-preview"
export AZURE_OPENAI_DEPLOYMENT="gpt-4.1-mini"

# Verify environment variables are set
echo "=== Environment Variables Check ==="
echo "AZURE_OPENAI_ENDPOINT: $AZURE_OPENAI_ENDPOINT"
echo "AZURE_OPENAI_API_VERSION: $AZURE_OPENAI_API_VERSION"
echo "AZURE_OPENAI_DEPLOYMENT: $AZURE_OPENAI_DEPLOYMENT"
if [ -n "$AZURE_OPENAI_API_KEY" ]; then
    echo "AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY:0:20}..."
else
    echo "AZURE_OPENAI_API_KEY: NOT SET"
fi
echo "=== End Check ==="

# Run the command
python -m IssueLocEval.main run \
    --dataset czlll/SWE-bench_Lite \
    --method direct_prediction \
    --model gpt-4.1-mini \
    --limit 10 \
    --evaluate \
    --temperature 0.7