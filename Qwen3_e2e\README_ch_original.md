# FastApply README

## 如何提交 Command Job 到计算集群

1. **配置环境**:
    - UAI
    - Hugging<PERSON>ace<PERSON>ey
    - How to create a docker env
    Run this script to build and register the llamafactory environment. Switch out the image/environment name, tag, registry name, workspace, and resource group as needed.

```bash
az login
cd environment # put your own docker file inside
bash build_and_register_environment.sh fastapply optimized-all 1e6d60a2ac124c168aa42090bc1ecc97 securityfilter_ws supplychain_security LlamaFactoryOptimized Poirot_RnD
```

2. **如何指定input output**:
    - 创建一个提交脚本，例如 `submit_job.sh`。
    - 在脚本中指定计算资源、任务参数等。

3. **提交任务**:
    - 使用命令行工具提交任务，例如：
      ```bash
      bash submit_job.sh
      ```

## 一个完整的例子

以下是一个完整的提交任务的例子：

```bash
#!/bin/bash

# 设置计算资源
#SBATCH --job-name=example_job
#SBATCH --output=output.log
#SBATCH --error=error.log
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=01:00:00

# 加载必要的模块
module load python/3.8

# 运行任务
python your_script.py --arg1 value1 --arg2 value2
```

## LoRA Fine-tune LLaMA 3.1 8B

1. **准备数据**:
    - 收集并预处理训练数据。

2. **配置模型**:
    - 下载并配置 LLaMA 3.1 8B 模型。

3. **编写训练脚本**:
    - 创建一个训练脚本，例如 `train_lora_llama.py`
    - 在脚本中设置模型参数和训练配置。

4. **运行训练**:
    - 使用命令行工具运行训练脚本，例如：
      ```bash
      python train_lora_llama.py --data_path /path/to/data --output_dir /path/to/output
      ```

以上是一个简单的 README 示例，详细内容可以根据实际需求进行扩展和修改。