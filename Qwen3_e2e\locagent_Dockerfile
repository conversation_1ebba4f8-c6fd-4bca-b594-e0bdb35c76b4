# Base image with PyTorch 2.6.0 and CUDA 12.4
ARG BASE_IMAGE=hiyouga/pytorch:th2.6.0-cu124-flashattn2.7.4-cxx11abi0-devel
FROM ${BASE_IMAGE}

# Build arguments
ARG PIP_INDEX=https://pypi.org/simple
ARG EXTRAS=metrics
ARG INSTALL_FLASHATTN=true
ARG HTTP_PROXY=""

# Environment variables
ENV MAX_JOBS=16
ENV FLASH_ATTENTION_FORCE_BUILD=TRUE
ENV VLLM_WORKER_MULTIPROC_METHOD=spawn
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_OPTIONS=""
ENV PIP_ROOT_USER_ACTION=ignore
ENV http_proxy="${HTTP_PROXY}"
ENV https_proxy="${HTTP_PROXY}"

# Use bash shell
SHELL ["/bin/bash", "-c"]

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    openssh-client \
    openssh-server \
    && rm -rf /var/lib/apt/lists/*

# Clone LLaMA-Factory repository
RUN if [ ! -d "/app/LLaMA-Factory" ]; then \
        git clone https://github.com/hiyouga/LLaMA-Factory.git /app/LLaMA-Factory; \
    else \
        cd /app/LLaMA-Factory && git pull origin main; \
    fi

# Switch to LLaMA-Factory directory
WORKDIR /app/LLaMA-Factory

# Configure pip and upgrade base packages
RUN pip config set global.index-url "${PIP_INDEX}" && \
    pip config set global.extra-index-url "${PIP_INDEX}" && \
    pip install --no-cache-dir --upgrade pip packaging wheel setuptools

# Install LLaMA-Factory requirements first (this installs PyTorch compatible with the base image)
RUN pip install --no-cache-dir -r requirements.txt

# Copy and install additional requirements (ensuring no conflicts)
# COPY requirements_locagent.txt /tmp/requirements_locagent.txt
# RUN pip install --no-cache-dir -r /tmp/requirements_locagent.txt

# Install AzureML packages (compatible versions)
RUN pip install --no-cache-dir \
    azureml-core==1.58.0 \
    azureml-dataset-runtime==1.58.0 \
    azureml-defaults==1.58.0 \
    azure-ml==0.0.1 \
    azure-ml-component==0.9.18.post2 \
    azureml-contrib-services==1.58.0 \
    torch-tb-profiler~=0.4.0 \
    azureml-inference-server-http \
    azure-ai-ml

# Install LLaMA Factory with selected extras
RUN pip install -e ".[torch, metrics, deepspeed, liger-kernel, vllm]" --no-build-isolation

# Conditionally install flash-attention
RUN if [ "${INSTALL_FLASHATTN}" == "true" ]; then \
        pip uninstall -y ninja && \
        pip install --no-cache-dir ninja && \
        pip install --no-cache-dir flash-attn --no-build-isolation; \
    fi

# Expose ports
ENV GRADIO_SERVER_PORT=7860
EXPOSE 7860

ENV API_PORT=8000
EXPOSE 8000

# Clean up proxy settings
ENV http_proxy=
ENV https_proxy=

# Clean up pip configuration
RUN pip config unset global.index-url && \
    pip config unset global.extra-index-url

# Clean up temporary files
RUN rm -f /tmp/requirements_locagent.txt