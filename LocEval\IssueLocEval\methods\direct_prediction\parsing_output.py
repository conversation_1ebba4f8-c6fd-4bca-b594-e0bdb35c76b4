"""Utilities for parsing model outputs."""

import re
from typing import Dict, Any, List, Set


def parse_location_prediction(prediction: str, instance_id: str) -> Dict[str, Any]:
    """Parse location prediction from model output.
    
    Args:
        prediction: Raw model prediction text
        instance_id: Instance identifier
        
    Returns:
        Parsed prediction dictionary matching expected format
    """
    result = {
        "instance_id": instance_id,
        "found_files": [],
        "found_modules": [],
        "found_entities": [],
        "raw_output_loc": [prediction],
        "meta_data": {}
    }
    
    # First, try to parse structured format
    if "FILES:" in prediction and "CLASSES:" in prediction and "FUNCTIONS:" in prediction:
        # Parse structured format
        lines = prediction.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            
            # Check for section headers
            if line == "FILES:":
                current_section = "files"
                continue
            elif line == "CLASSES:":
                current_section = "classes"
                continue
            elif line == "FUNCTIONS:":
                current_section = "functions"
                continue
            
            # Parse items (handle both - and * bullet points)
            if line.startswith(('- ', '* ', '• ')) and len(line) > 2:
                item = line[2:].strip()
                
                # Remove any trailing comments (anything after # or //)
                if '#' in item:
                    item = item.split('#')[0].strip()
                if '//' in item:
                    item = item.split('//')[0].strip()
                
                if current_section == "files" and item.endswith('.py'):
                    if item not in result["found_files"]:
                        result["found_files"].append(item)
                
                elif current_section == "classes" and ':' in item:
                    # Validate format: file.py:ClassName
                    parts = item.split(':', 1)
                    if len(parts) == 2 and parts[0].endswith('.py') and parts[1]:
                        if item not in result["found_modules"]:
                            result["found_modules"].append(item)
                
                elif current_section == "functions" and ':' in item:
                    # Validate format: file.py:function or file.py:Class.method
                    parts = item.split(':', 1)
                    if len(parts) == 2 and parts[0].endswith('.py') and parts[1]:
                        if item not in result["found_entities"]:
                            result["found_entities"].append(item)
                        
                        # Also extract the class if it's a method
                        if '.' in parts[1]:
                            class_name = parts[1].split('.')[0]
                            module_path = f"{parts[0]}:{class_name}"
                            if module_path not in result["found_modules"]:
                                result["found_modules"].append(module_path)
    
    # If structured format wasn't found or didn't parse well, fall back to regex parsing
    if not (result["found_files"] or result["found_modules"] or result["found_entities"]):
        # Extract file paths
        file_pattern = r'\b[\w\-/]+(?:/[\w\-]+)*\.py\b'
        found_files = re.findall(file_pattern, prediction)
        result["found_files"] = list(dict.fromkeys(found_files))
        
        # Look for explicit file:entity patterns
        explicit_pattern = r'([\w\-/\.]+\.py):([\w\.]+)'
        explicit_matches = re.findall(explicit_pattern, prediction)
        
        for file_path, entity in explicit_matches:
            if '.' in entity:
                # It's a class.method pattern
                class_name = entity.split('.')[0]
                if class_name and class_name[0].isupper():
                    result["found_modules"].append(f"{file_path}:{class_name}")
                result["found_entities"].append(f"{file_path}:{entity}")
            else:
                # It's just a class or function
                if entity and entity[0].isupper():  # Likely a class
                    result["found_modules"].append(f"{file_path}:{entity}")
                else:  # Likely a function
                    result["found_entities"].append(f"{file_path}:{entity}")
        
        # If no explicit patterns found, try to extract from natural language
        if not result["found_entities"]:
            # Look for function names in backticks
            backtick_pattern = r'`(\w+(?:\.\w+)*)`'
            backtick_matches = re.findall(backtick_pattern, prediction)
            
            # Look for "function X" or "method Y" mentions
            func_patterns = [
                r'(?:function|method)\s+(?:named\s+)?[`"]?(\w+)[`"]?',
                r'`(\w+)`\s+(?:function|method)',
            ]
            
            for pattern in func_patterns:
                matches = re.findall(pattern, prediction, re.IGNORECASE)
                backtick_matches.extend(matches)
            
            # Associate found functions with files
            for file in result["found_files"]:
                for func in set(backtick_matches):
                    if func and len(func) > 2 and not func.isdigit():
                        func_path = f"{file}:{func}"
                        if func_path not in result["found_entities"]:
                            result["found_entities"].append(func_path)
    
    # Remove duplicates while preserving order
    result["found_files"] = list(dict.fromkeys(result["found_files"]))
    result["found_modules"] = list(dict.fromkeys(result["found_modules"]))
    result["found_entities"] = list(dict.fromkeys(result["found_entities"]))
    
    return result