"""Base classes for datasets, methods, and metrics."""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json


class BaseDataset(ABC):
    """Abstract base class for datasets."""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """Initialize dataset.
        
        Args:
            name: Name of the dataset
            config: Optional configuration dictionary
        """
        self.name = name
        self._data = None
    
    @abstractmethod
    def load(self, split: str = "test", **kwargs) -> List[Dict[str, Any]]:
        """Load dataset.
        
        Args:
            split: Dataset split to load
            **kwargs: Additional arguments
            
        Returns:
            List of dataset instances
        """
        pass
    
    # @abstractmethod
    # def preprocess(self, data: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
    #     """Preprocess dataset.
        
    #     Args:
    #         data: Raw dataset instances
    #         **kwargs: Additional preprocessing arguments
            
    #     Returns:
    #         Preprocessed dataset instances
    #     """
    #     pass
    
    @abstractmethod
    def _get_stats(self) -> Dict[str, Any]:
        """Get dataset statistics.
        
        Returns:
            Dictionary containing dataset statistics
        """
        pass
    
    def filter(self, data: List[Dict[str, Any]], 
               filter_func: Optional[callable] = None) -> List[Dict[str, Any]]:
        """Filter dataset instances.
        
        Args:
            data: Dataset instances
            filter_func: Optional filter function
            
        Returns:
            Filtered dataset instances
        """
        if filter_func:
            return [item for item in data if filter_func(item)]
        return data