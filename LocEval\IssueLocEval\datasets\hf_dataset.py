"""Generic HuggingFace dataset implementation."""

from typing import List, Dict, Any, Optional, Callable
from datasets import load_dataset
from .base_dataset import BaseDataset
import collections
import logging
import pdb  # Importing pdb for debugging
from datetime import datetime
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Tuple
import re
from transformers import AutoTokenizer


class HuggingFaceDataset(BaseDataset):
    """Generic dataset loader for HuggingFace datasets."""
    
    def __init__(
        self, 
        dataset_name: str,
        hf_dataset_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        tokenizer_name: str = "gpt2"  # Default tokenizer
    ):
        """Initialize HuggingFace dataset.
        
        Args:
            dataset_name: Internal name for the dataset
            hf_dataset_path: HuggingFace dataset path (e.g., 'princeton-nlp/SWE-bench_Lite')
            field_mapping: Mapping from standard fields to dataset-specific fields
            config: Optional configuration dictionary
        """
        super().__init__(dataset_name)
        self.hf_dataset_path = hf_dataset_path
        self.field_mapping = field_mapping or {}
        self._stats = None
        self.tokenizer_name = tokenizer_name
        self._tokenizer = None
        
        # Default field mapping (can be overridden)
        self.default_field_mapping = {
            "instance_id": "instance_id",
            "problem_statement": "problem_statement",
            "repo": "repo",
            "patch": "patch",
            "created_at": "created_at",
            "base_commit": "base_commit",
            "hints_text": "hints_text"
        }
        
        # Merge with provided mapping
        self.field_mapping = {**self.default_field_mapping, **self.field_mapping}
    
    @property
    def tokenizer(self):
        """Lazy load tokenizer."""
        if self._tokenizer is None:
            try:
                self._tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_name)
            except Exception as e:
                print(f"Warning: Could not load tokenizer {self.tokenizer_name}, falling back to gpt2: {e}")
                self._tokenizer = AutoTokenizer.from_pretrained("gpt2")
        return self._tokenizer
    
    def load(self, split: str = "test", limit: Optional[int] = None, **kwargs) -> List[Dict[str, Any]]:
        """Load dataset from HuggingFace.
        
        Args:
            split: Dataset split to load
            limit: Optional limit on number of instances
            **kwargs: Additional arguments for load_dataset
            
        Returns:
            List of dataset instances with standardized field names
        """
        print(f"Loading {self.hf_dataset_path} ({split} split)...")
        dataset = load_dataset(self.hf_dataset_path, split=split, **kwargs)

        # Convert to list of dictionaries with standardized fields
        data = []
        items = dataset if limit is None else dataset.select(range(min(limit, len(dataset))))
        
        for item in items:
            # Map fields to standard names
            standardized_item = {}
            for standard_field, dataset_field in self.field_mapping.items():
                if dataset_field in item:
                    standardized_item[standard_field] = item[dataset_field]
                elif standard_field in item:  # Fallback to standard field name
                    standardized_item[standard_field] = item[standard_field]
            
            # Include any extra fields not in mapping
            for key, value in item.items():
                if key not in standardized_item.values():
                    standardized_item[key] = value
            
            data.append(standardized_item)
        
        self._data = data

        # parse ground truth if available
        self._parse_ground_truth()

        self._get_stats()  # Update statistics after loading data

        print(f"Loaded {len(self._data)} instances from {self.hf_dataset_path} ({split} split)")

    
    def _get_stats(self) -> Dict[str, Any]:
        """Get dataset statistics.
        
        Returns:
            Dictionary containing dataset statistics
        """
        if not self._data:
            raise ValueError("Dataset not loaded. Call load() first.")
        
        if self._stats is None:
            # Count tokens for all problem statements
            total_problem_tokens = 0
            total_patch_tokens = 0
            for item in self._data:
                problem_text = item.get("problem_statement", "")
                if problem_text:
                    try:
                        tokens = self.tokenizer.encode(problem_text, truncation=True, max_length=1024)
                        total_problem_tokens += len(tokens)
                    except Exception as e:
                        # Fallback to character count if tokenization fails
                        print(f"Warning: Tokenization failed for instance {item.get('instance_id', 'unknown')}: {e}")
                        total_problem_tokens += len(problem_text) // 4  # Rough estimate: 4 chars per token
                patch_text = item.get("patch", "")
                if patch_text:
                    try:
                        tokens = self.tokenizer.encode(patch_text, truncation=True, max_length=1024)
                        total_patch_tokens += len(tokens)
                    except Exception as e:
                        # Fallback to character count if tokenization fails
                        print(f"Warning: Tokenization failed for patch in instance {item.get('instance_id', 'unknown')}: {e}")
                        total_patch_tokens += len(patch_text) // 4
            
            self._stats = {
                "total_instances": len(self._data),
                "dataset_name": self.name,
                "hf_path": self.hf_dataset_path,
                "unique_repos": len(set(item.get("repo", "") for item in self._data if "repo" in item)),
                "avg_problem_length_tokens": total_problem_tokens / len(self._data) if self._data else 0,
                "avg_patch_length_tokens": total_patch_tokens / len(self._data) if self._data else 0,
                "most_recent_instance": self._get_most_recent_date(),
            }
            
            # Count instances by repo if available
            if any("repo" in item for item in self._data):
                repo_counts = {}
                for item in self._data:
                    repo = item.get("repo", "unknown")
                    repo_counts[repo] = repo_counts.get(repo, 0) + 1
                self._stats["repo_distribution"] = repo_counts
                self._stats["avg_instances_per_repo"] = (
                    sum(repo_counts.values()) / len(repo_counts) if repo_counts else 0
                )
                self._stats["median_instances_per_repo"] = (
                    pd.Series(repo_counts.values()).median() if repo_counts else 0
                )
                self._stats["std_instances_per_repo"] = (
                    pd.Series(repo_counts.values()).std() if repo_counts else 0
                )
                self._stats["25th_percentile_instances_per_repo"] = (
                    pd.Series(repo_counts.values()).quantile(0.25) if repo_counts else 0
                )
                self._stats["75th_percentile_instances_per_repo"] = (
                    pd.Series(repo_counts.values()).quantile(0.75) if repo_counts else 0
                )
                self._stats["max_instances_per_repo"] = max(repo_counts.values(), default=0)
                self._stats["min_instances_per_repo"] = min(repo_counts.values(), default=0)

            # number of ground truth entities
            if any("gt_files" in item for item in self._data):
                gt_files_count = sum(len(item.get("gt_files", [])) for item in self._data)
                gt_modules_count = sum(len(item.get("gt_modules", [])) for item in self._data)
                gt_functions_count = sum(len(item.get("gt_functions", [])) for item in self._data)

                self._stats["avg_gt_files_per_instance"] = gt_files_count / len(self._data) if self._data else 0
                self._stats["avg_gt_modules_per_instance"] = gt_modules_count / len(self._data) if self._data else 0
                self._stats["avg_gt_functions_per_instance"] = gt_functions_count / len(self._data) if self._data else 0
            
            # Count unique languages in ground truth if available
            if any("gt_languages" in item for item in self._data):
                unique_languages = set()
                for item in self._data:
                    unique_languages.update(item.get("gt_languages", []))
                self._stats["unique_gt_languages"] = len(unique_languages)
                self._stats["gt_language_distribution"] = {
                    lang: sum(lang in item.get("gt_languages", []) for item in self._data)
                    for lang in unique_languages
                }

            # get the top 5 most common languages
            if unique_languages:
                # Sort by frequency (descending) and take top 5
                top_languages = sorted(
                    self._stats["gt_language_distribution"].items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
                self._stats["top_languages"] = top_languages


    def _parse_ground_truth(self) -> Dict[str, List[str]]:
        """Load ground truth from HuggingFace dataset."""
        INIT_METHOD = '.__init__'

        for instance in self._data:
            gt_files = []
            gt_modules = []
            gt_functions = []
            # Extract locations from either edit_functions or patch
            if 'edit_functions' in instance:
                edit_functions_list = instance.get('edit_functions', [])
                if not isinstance(edit_functions_list, list):
                    logging.warning(f"Instance {instance.get('instance_id', 'Unknown')} has malformed 'edit_functions' (not a list): {edit_functions_list}")
                    edit_functions_list = []

                for func in edit_functions_list:
                    if not isinstance(func, str) or ':' not in func:
                        logging.warning(f"Instance {instance.get('instance_id', 'Unknown')} has malformed 'edit_functions' entry (not a string): {func}")
                        continue
                    
                    file_path = func.split(':')[0]
                    gt_files.append(file_path)

                    module_name = func.split(':')[-1].split('.')[0]
                    gt_modules.append(f"{file_path}:{module_name}")

                    mname = func.split(':')[-1]
                    if mname.endswith(INIT_METHOD):
                        mname = mname[:-len(INIT_METHOD)]
                    gt_functions.append(f"{file_path}:{mname}")

            elif 'patch' in instance and instance.get('patch'):
                # Extract from patch
                # pdb.set_trace()  # Debugging breakpoint
                gt_files, gt_modules, gt_functions = self._extract_locations_from_patch(instance['patch'])
            
            # Process file extensions directly (no language mapping)
            gt_languages = []
            for file_path in gt_files:
                # Extract file extension directly
                if '.' in file_path:
                    file_extension = '.' + file_path.split('.')[-1]
                    gt_languages.append(file_extension)
                else:
                    # Check for special files without extensions (like Dockerfile, Makefile)
                    filename = file_path.split('/')[-1]
                    if filename in ['Dockerfile', 'Makefile', 'dockerfile', 'makefile']:
                        gt_languages.append(filename.lower())
                    else:
                        gt_languages.append('no-extension')

            # Store ground truth back to instance as new fields
            instance['gt_languages'] = list(set(gt_languages))
            instance['gt_files'] = list(set(gt_files))
            instance['gt_modules'] = list(set(gt_modules))
            instance['gt_functions'] = list(set(gt_functions))

        

    def _extract_locations_from_patch(self, patch: str) -> Tuple[List[str], List[str], List[str]]:
        """Extract file, module, and function locations from a patch.
        
        Args:
            patch: Git patch string
            
        Returns:
            Tuple of (files, modules, functions) lists
        """
        
        files = []
        modules = []
        functions = []
        
        lines = patch.split('\n')
        current_file = None
        
        # Pattern to detect function context from @@ lines
        hunk_pattern = r'@@ -\d+,?\d* \+\d+,?\d* @@ (.+)'
        
        for line in lines:
            # Track current file from diff header
            if line.startswith('diff --git'):
                match = re.search(r'b/(.*?)(?:\s|$)', line)
                if match:
                    current_file = match.group(1)
                    if current_file not in files:
                        files.append(current_file)

            # Extract function/class context from hunk headers
            elif line.startswith('@@') and current_file:
                match = re.match(hunk_pattern, line)
                if match:
                    context = match.group(1).strip()
                    
                    # Look for class context
                    class_match = re.search(r'class\s+(\w+)', context)
                    class_name = class_match.group(1) if class_match else None
                    
                    # Look for function definitions in context
                    func_match = re.search(r'def\s+(\w+)\s*\(([^)]*)\)', context)
                    if func_match:
                        func_name = func_match.group(1)
                        params = func_match.group(2).strip()
                        
                        # Check if this looks like a method
                        is_likely_method = (
                            params.startswith(('self', 'cls')) or
                            func_name.startswith('__') and func_name.endswith('__') or
                            func_name.startswith('_')
                        )
                        
                        # Build the function path
                        if class_name:
                            # We have class context
                            module_path = f"{current_file}:{class_name}"
                            func_path = f"{current_file}:{class_name}.{func_name}"
                            
                            if module_path not in modules:
                                modules.append(module_path)
                        elif is_likely_method:
                            # Looks like a method but no class context
                            module_path = f"{current_file}:unknown"
                            func_path = f"{current_file}:unknown.{func_name}"
                            
                            if module_path not in modules:
                                modules.append(module_path)
                        else:
                            # Standalone function
                            func_path = f"{current_file}:{func_name}"
                        
                        if func_path not in functions:
                            functions.append(func_path)
                    
                    # If we only have a class in the context (no function)
                    elif class_name:
                        module_path = f"{current_file}:{class_name}"
                        if module_path not in modules:
                            modules.append(module_path)

        return files, modules, functions

    def filter_out_simple(self, data: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """Filter out instances where ground truth files are mentioned in the problem statement.
        
        This helps identify "simple" instances where the problem statement already contains
        the answer (file locations).
        
        Args:
            data: Optional list of dataset instances. If None, uses self._data
            
        Returns:
            Filtered list of instances excluding those with GT files in problem statement
        """
        if data is None:
            if self._data is None:
                raise ValueError("No data available. Call load() first.")
            data = self._data
        
        filtered_data = []
        filtered_count = 0
        
        for instance in data:
            problem_statement = instance.get('problem_statement', '').lower()
            gt_files = instance.get('gt_files', [])
            
            # Check if any ground truth file appears in the problem statement
            file_mentioned = False
            for gt_file in gt_files:
                # Check for various ways the file might be mentioned
                file_name = gt_file.lower()
                
                # Check for exact path
                if file_name in problem_statement:
                    file_mentioned = True
                    break
                
                # Check for just the filename (without path)
                base_name = file_name.split('/')[-1]
                if base_name in problem_statement:
                    file_mentioned = True
                    break
                
                # Check for file without extension
                name_without_ext = base_name.rsplit('.', 1)[0]
                if name_without_ext in problem_statement and len(name_without_ext) > 3:  # Avoid short matches
                    file_mentioned = True
                    break
            
            if not file_mentioned:
                filtered_data.append(instance)
            else:
                filtered_count += 1
                if filtered_count <= 3:  # Log first few filtered instances
                    print(f"Filtered out instance {instance.get('instance_id', 'unknown')}: "
                        f"GT files {gt_files[:2]}... mentioned in problem statement")
        
        print(f"Filtered out {filtered_count} 'simple' instances where GT files were mentioned in problem statement")
        print(f"Remaining instances: {len(filtered_data)}")
        
        return filtered_data
    
    def filter_out_old(self, cutoff_date: Union[str, datetime], 
                   data: Optional[List[Dict[str, Any]]] = None,
                   keep_after: bool = True,
                   date_format: Optional[str] = None) -> List[Dict[str, Any]]:
        """Filter instances by creation date.
        
        Args:
            cutoff_date: The cutoff date. Can be:
                - String in ISO format (e.g., '2023-01-01')
                - datetime object
            data: Optional list of dataset instances. If None, uses self._data
            keep_after: If True, keep instances after date. If False, keep instances before date.
            date_format: Optional date format string for parsing. If None, tries common formats
            
        Returns:
            Filtered list of instances based on date criteria
        """
        if data is None:
            if self._data is None:
                raise ValueError("No data available. Call load() first.")
            data = self._data
    
        # Convert cutoff_date to datetime if it's a string
        if isinstance(cutoff_date, str):
            try:
                cutoff_dt = pd.to_datetime(cutoff_date)
                # Make timezone-naive for comparison
                if cutoff_dt.tzinfo is not None:
                    cutoff_dt = cutoff_dt.tz_localize(None)
            except:
                raise ValueError(f"Could not parse cutoff date: {cutoff_date}")
        elif isinstance(cutoff_date, datetime):
            cutoff_dt = cutoff_date
            # Make timezone-naive for comparison
            if cutoff_dt.tzinfo is not None:
                cutoff_dt = cutoff_dt.replace(tzinfo=None)
        else:
            raise ValueError(f"cutoff_date must be string or datetime, got {type(cutoff_date)}")
        
        filtered_data = []
        filtered_count = 0
        parse_errors = 0
        
        for instance in data:
            created_at = instance.get('created_at', None)
            
            if created_at is None:
                # Skip instances without created_at field
                continue
            
            try:
                # Parse the created_at field
                if isinstance(created_at, (int, float)):
                    # Assume Unix timestamp
                    if created_at > 1e10:  # Likely milliseconds
                        instance_dt = pd.to_datetime(created_at, unit='ms')
                    else:  # Likely seconds
                        instance_dt = pd.to_datetime(created_at, unit='s')
                elif isinstance(created_at, str):
                    # Try to parse string date
                    if date_format:
                        instance_dt = datetime.strptime(created_at, date_format)
                    else:
                        # Let pandas try to figure it out
                        instance_dt = pd.to_datetime(created_at)
                else:
                    # Already a datetime object or similar
                    instance_dt = pd.to_datetime(created_at)
                
                # Remove timezone info for comparison if present
                if instance_dt.tzinfo is not None:
                    instance_dt = instance_dt.tz_localize(None)
                
                # Apply filter based on keep_after flag
                if (keep_after and instance_dt > cutoff_dt) or (not keep_after and instance_dt <= cutoff_dt):
                    filtered_data.append(instance)
                else:
                    filtered_count += 1
                    if filtered_count <= 3:  # Log first few filtered instances
                        action = "after" if keep_after else "on or before"
                        print(f"Filtered out {instance.get('instance_id', 'unknown')}: "
                              f"created at {instance_dt.strftime('%Y-%m-%d')} "
                              f"({'before' if keep_after else 'after'} {cutoff_dt.strftime('%Y-%m-%d')})")
                          
            except Exception as e:
                parse_errors += 1
                if parse_errors <= 3:
                    print(f"Warning: Could not parse date for instance "
                          f"{instance.get('instance_id', 'unknown')}: {created_at} ({e})")
        
        action = "before" if keep_after else "after"
        print(f"\nFiltered out {filtered_count} instances created {action} {cutoff_dt.strftime('%Y-%m-%d')}")
        if parse_errors > 0:
            print(f"Warning: {parse_errors} instances had unparseable dates")
        print(f"Remaining instances: {len(filtered_data)}")
        
        return filtered_data
    
    def _get_most_recent_date(self) -> str:
        """Get the most recent creation date from all instances."""
        if not self._data:
            return "1970-01-01"
        
        most_recent_dt = None
        
        for item in self._data:
            created_at = item.get('created_at', None)
            if created_at is None:
                continue
            
            try:
                # Parse the created_at field using the same logic as filter_out_old
                if isinstance(created_at, (int, float)):
                    # Assume Unix timestamp
                    if created_at > 1e10:  # Likely milliseconds
                        instance_dt = pd.to_datetime(created_at, unit='ms')
                    else:  # Likely seconds
                        instance_dt = pd.to_datetime(created_at, unit='s')
                elif isinstance(created_at, str):
                    # Let pandas try to figure it out
                    instance_dt = pd.to_datetime(created_at)
                else:
                    # Already a datetime object or similar
                    instance_dt = pd.to_datetime(created_at)
                
                # Remove timezone info for comparison if present
                if instance_dt.tzinfo is not None:
                    instance_dt = instance_dt.tz_localize(None)
                
                # Keep track of the most recent date
                if most_recent_dt is None or instance_dt > most_recent_dt:
                    most_recent_dt = instance_dt
                    
            except Exception as e:
                # Skip instances with unparseable dates
                continue
    
        # Return formatted date string or default
        if most_recent_dt is not None:
            return most_recent_dt.strftime('%Y-%m-%d')
        else:
            return "1970-01-01"


if __name__ == "__main__":
    import pdb  # Importing pdb for debugging
    dataset = HuggingFaceDataset("princeton-nlp/SWE-bench_Lite_bm25_13K", 
                                 hf_dataset_path="princeton-nlp/SWE-bench_Lite_bm25_13K")
    dataset.load()
    pdb.set_trace()  # Debugging breakpoint
    print(dataset._stats)