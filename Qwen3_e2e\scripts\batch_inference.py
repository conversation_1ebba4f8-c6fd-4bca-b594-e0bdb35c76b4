from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import command
from azure.ai.ml import Input, Output
from azure.ai.ml.entities import ResourceConfiguration
from azure.ai.ml import MLClient
from datetime import datetime
from argparse import ArgumentParser
from azure.ai.ml.entities import JupyterLabJobService, VsCodeJobService, TensorBoardJobService, SshJobService
import os
os.environ["DISABLE_VERSION_CHECK"] = "1"
import webbrowser
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from azure.ai.ml import MLClient, command, Input
from azure.ai.ml.constants import AssetTypes, InputOutputModes
from aml.endpoint.manage import download_data_asset
from vllm_infer import vllm_infer
from vllm import LLM, SamplingParams
from transformers import AutoTokenizer
import pandas as pd
print("Current working directory:", os.getcwd())


if __name__=='__main__':
 
    parser = ArgumentParser()
    parser.add_argument('--cluster', type=str, default="h100centralusvc")
    parser.add_argument('--resource_group', type=str, default="SingularityH100")
    parser.add_argument('--workspace', type=str, default="H100CentralUS")
    parser.add_argument('--subscription_id', type=str, default="d0c05057-7972-46ff-9bcf-3c932250155e")
    parser.add_argument('--dataset_name', type=str, default="test_apply_2step")
    parser.add_argument('--model_data_asset_name', type=str, default="qwen_7b_ep3")
    parser.add_argument('--data_asset_version', type=str, default="1")
    parser.add_argument('--data_asset_local_path', type=str, default=" ./data_asset_download/qwen_7b_ep3/")
    parser.add_argument('--output_path', type=str, default="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/SingularityH100/workspaces/H100CentralUS/datastores/workspaceblobstore/paths/alex/")
 
    args = parser.parse_args()
 
    resource_group = args.resource_group
    workspace = args.workspace
    subscription_id = args.subscription_id
    cluster_name = args.cluster
 
    try:
        credential = DefaultAzureCredential()
        # Check if given credential can get token successfully.
        credential.get_token("https://management.azure.com/.default")
    except Exception as ex:
        print('Failed to get token with DefaultAzureCredential, fall back to InteractiveBrowserCredential', ex)
        # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work
        credential = InteractiveBrowserCredential()
 
    # get a handle to the workspace
    ml_client = MLClient(
        subscription_id=subscription_id,
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential,
    )
    
    if not os.path.exists(args.data_asset_local_path):
        os.makedirs(args.data_asset_local_path)
        download_data_asset(
            ml_client=ml_client,
            validate_only=False,
            data_asset_name=args.model_data_asset_name,
            data_asset_version=args.data_asset_version,
            destination=args.data_asset_local_path)
    else:
        print(f"Directory {args.data_asset_local_path} already exists. Skipping download.")
    print(f"Directory {os.path.abspath(args.data_asset_local_path)} does not exist. Creating directory.")
    # outputs={
    #         "datastore_dir": Output(
    #             type="uri_folder",
    #             path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/alex/qwen25_coder_7b_ep3/",
    #             # path="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/AML-NorwayEast/workspaces/AML-NorwayEast/datastores/workspaceblobstore/paths/ryangabriel/",
    #             # path="azureml://datastores/workspaceblobstore/paths/tutorial-datasets/places2/train/",
    #             mode="rw_mount",
    #         ),
    # }

    # Check if the dataset_info.json file exists
    dataset_info_path = 'data/dataset_info.json'
    if not os.path.exists(dataset_info_path):
        raise FileNotFoundError(f"The dataset_info.json file was not found at {dataset_info_path}. Please ensure the file exists.")
    
    vllm_infer(
        model_name_or_path=args.data_asset_local_path,
        template="qwen",
        dataset=args.dataset_name,
        cutoff_len=32000,
        max_new_tokens=32000,
        save_name="generated_predictions_2step.jsonl"
    )
    
    # # Initialize the tokenizer
    # tokenizer = AutoTokenizer.from_pretrained("/home/<USER>/cloudfiles/code/Users/<USER>/FastApply/data_asset_download")

    # # Pass the default decoding hyperparameters of Qwen2.5-7B-Instruct
    # # max_tokens is for the maximum length for generation.
    # sampling_params = SamplingParams(temperature=0.1, top_p=0.8, repetition_penalty=1.05, max_tokens=16000)

    # # Input the model name or path. Can be GPTQ or AWQ models.
    # llm = LLM(model="/home/<USER>/cloudfiles/code/Users/<USER>/FastApply/data_asset_download")
    # df = pd.read_json("/home/<USER>/cloudfiles/code/Users/<USER>/FastApply/data/test_apply.json")
    # # Prepare your prompts
    # results = []
    # for index, row in df.iterrows():
    #     prompt = row["prompt"]
    #     messages = [
    #         {"role": "user", "content": prompt}
    #     ]
    #     text = tokenizer.apply_chat_template(
    #         messages,
    #         tokenize=False,
    #         add_generation_prompt=True
    #     )

    #     # generate outputs
    #     outputs = llm.generate([text], sampling_params)

    #     # Extract the generated text
    #     for output in outputs:
    #         generated_text = output.outputs[0].text
    #         results.append(generated_text)

    # # Add the results as a new column to the dataframe
    # df['qwen7ep1_res'] = results
    # # Save the updated dataframe to a new JSON file
    # df.to_json("test_apply_qwen7ep1.json", orient='records', lines=True)
