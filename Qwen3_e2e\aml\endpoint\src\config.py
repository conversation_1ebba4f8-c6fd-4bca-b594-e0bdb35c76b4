def set_configuration_if_present(config_src, config, key, type_converter=None, default=None):
    value = config_src.get(f"AZUREML_{key}", None)
    if value is not None:
        if type_converter is not None:
            value = type_converter(value)
        else:
            value = value.replace("${AZUREML_MODEL_DIR}",
                                  config_src.get("AZUREML_MODEL_DIR", "${AZUREML_MODEL_DIR}"))
        print(f"Setting config: {key}={value}")
        config[key] = value
    elif default is not None:
        print(f"Setting config to default: {key}={default}")
        config[key] = default
    else:
        print(f"Skipping config: {key}")


def str2bool(item: str):
    return item == "True"
