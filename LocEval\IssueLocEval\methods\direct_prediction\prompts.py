"""Prompt templates for the DirectPredictionMethod."""

from typing import Dict

# Type hint for clarity
PROMPTS: Dict[str, str] = {
    "default": """Given the following issue description from the {repo} repository, identify the specific locations (files, classes, and functions/methods) that need to be examined or modified to address this issue.

Issue Description:
{problem}

Please provide your answer in the following structured format:

FILES:
- path/to/file1.py
- path/to/file2.py

MODULES:
- path/to/file1.py:ModuleName1
- path/to/file2.py:ModuleName2

FUNCTIONS:
- path/to/file1.py:function_name
- path/to/file1.py:ModuleName1.method_name
- path/to/file2.py:ModuleName2.method_name

Important:
1. List actual file paths relative to the repository root (include the repository name if it's part of the path structure)
2. For modules, use format: "file_path.py:ModuleName"
3. For functions/methods, use format: "file_path.py:function_name" or "file_path.py:ModuleName.method_name"
4. List locations that you think are relevant to solving the issue
5. Be specific - avoid listing entire directories or vague locations
6. Use the same path format as shown in repository file structure""",

    "concise": """Repository: {repo}
Issue: {problem}

List the files, classes, and functions that need to be modified to fix this issue.

Format:
FILES: file1.py, file2.py
MODULES: file1.py:Module1, file2.py:Module2
FUNCTIONS: file1.py:func1, file1.py:Module1.method1""",

    "cot": """You are analyzing an issue in the {repo} repository. Your task is to identify the precise code locations that need to be examined or modified.

## Issue Description:
{problem}

## Instructions:
Please analyze this issue, think step by step, and provide:

1. **FILES** - List all files that need to be modified
   - Include the full path from the repository root
   - List files that are likely relevant

2. **MODULES** - List all modules that need to be modified
   - Format: filepath.py:ModuleName

3. **FUNCTIONS** - List all functions/methods that need to be modified
   - Format: filepath.py:function_name or filepath.py:ModuleName.method_name
   - Include constructors as ModuleName.__init__

## Output Format:
FILES:
- path/to/file.py

MODULES:
- path/to/file.py:ModuleName

FUNCTIONS:
- path/to/file.py:function_or_method""",
}

# Optional: Add a function to validate prompts have required placeholders
def validate_prompt(prompt: str) -> bool:
    """Validate that a prompt has the required placeholders."""
    required_placeholders = ['{repo}', '{problem}']
    return all(placeholder in prompt for placeholder in required_placeholders)

# Optional: Validate all prompts on import
for name, prompt in PROMPTS.items():
    if not validate_prompt(prompt):
        raise ValueError(f"Prompt '{name}' missing required placeholders")