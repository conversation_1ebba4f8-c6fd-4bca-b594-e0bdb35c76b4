{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"gather": {"logged": 1737499768447}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting mltable\n", "  Downloading mltable-1.6.1-py3-none-any.whl.metadata (4.8 kB)\n", "Collecting azure-ai-ml\n", "  Downloading azure_ai_ml-1.24.0-py3-none-any.whl.metadata (35 kB)\n", "Collecting azure-identity\n", "  Using cached azure_identity-1.19.0-py3-none-any.whl.metadata (80 kB)\n", "Collecting azureml-dataprep<5.2.0a,>=5.1.0a (from azureml-dataprep[parquet]<5.2.0a,>=5.1.0a->mltable)\n", "  Using cached azureml_dataprep-5.1.6-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting pyyaml<7.0.0,>=5.1.0 (from mltable)\n", "  Downloading PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)\n", "Collecting jsonschema<5.0.0,>=4.0.0 (from mltable)\n", "  Downloading jsonschema-4.23.0-py3-none-any.whl.metadata (7.9 kB)\n", "Collecting msrest>=0.6.18 (from mltable)\n", "  Using cached msrest-0.7.1-py3-none-any.whl.metadata (21 kB)\n", "Collecting azure-core!=1.22.0,<2.0.0,>=1.8.0 (from mltable)\n", "  Using cached azure_core-1.32.0-py3-none-any.whl.metadata (39 kB)\n", "Collecting azure-mgmt-core<2.0.0,>=1.3.0 (from mltable)\n", "  Using cached azure_mgmt_core-1.5.0-py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.7.3 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from mltable) (2.9.0.post0)\n", "Collecting cryptography!=1.9,!=2.0.*,!=2.1.*,!=2.2.* (from mltable)\n", "  Downloading cryptography-44.0.0-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (5.7 kB)\n", "Collecting PyJWT<3.0.0 (from mltable)\n", "  Downloading PyJWT-2.10.1-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: pytz in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from mltable) (2024.2)\n", "Collecting marshmallow>=3.5 (from azure-ai-ml)\n", "  Downloading marshmallow-3.25.1-py3-none-any.whl.metadata (7.3 kB)\n", "Requirement already satisfied: tqdm in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from azure-ai-ml) (4.67.1)\n", "Collecting strictyaml (from azure-ai-ml)\n", "  Using cached strictyaml-1.7.3-py3-none-any.whl.metadata (11 kB)\n", "Collecting colorama (from azure-ai-ml)\n", "  Downloading colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)\n", "Collecting azure-storage-blob>=12.10.0 (from azure-ai-ml)\n", "  Downloading azure_storage_blob-12.24.1-py3-none-any.whl.metadata (26 kB)\n", "Collecting azure-storage-file-share (from azure-ai-ml)\n", "  Downloading azure_storage_file_share-12.20.1-py3-none-any.whl.metadata (49 kB)\n", "Collecting azure-storage-file-datalake>=12.2.0 (from azure-ai-ml)\n", "  Downloading azure_storage_file_datalake-12.18.1-py3-none-any.whl.metadata (16 kB)\n", "Collecting pydash>=6.0.0 (from azure-ai-ml)\n", "  Downloading pydash-8.0.5-py3-none-any.whl.metadata (4.5 kB)\n", "Collecting isodate (from azure-ai-ml)\n", "  Using cached isodate-0.7.2-py3-none-any.whl.metadata (11 kB)\n", "Collecting azure-common>=1.1 (from azure-ai-ml)\n", "  Using cached azure_common-1.1.28-py2.py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: typing-extensions in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from azure-ai-ml) (4.12.2)\n", "Collecting azure-monitor-opentelemetry (from azure-ai-ml)\n", "  Downloading azure_monitor_opentelemetry-1.6.4-py3-none-any.whl.metadata (21 kB)\n", "Collecting msal>=1.30.0 (from azure-identity)\n", "  Using cached msal-1.31.1-py3-none-any.whl.metadata (11 kB)\n", "Collecting msal-extensions>=1.2.0 (from azure-identity)\n", "  Using cached msal_extensions-1.2.0-py3-none-any.whl.metadata (7.6 kB)\n", "Requirement already satisfied: requests>=2.21.0 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from azure-core!=1.22.0,<2.0.0,>=1.8.0->mltable) (2.32.3)\n", "Requirement already satisfied: six>=1.11.0 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from azure-core!=1.22.0,<2.0.0,>=1.8.0->mltable) (1.17.0)\n", "Collecting azureml-dataprep-native<42.0.0,>=41.0.0 (from azureml-dataprep<5.2.0a,>=5.1.0a->azureml-dataprep[parquet]<5.2.0a,>=5.1.0a->mltable)\n", "  Downloading azureml_dataprep_native-41.0.0-cp310-cp310-manylinux1_x86_64.whl.metadata (1.3 kB)\n", "Collecting azureml-dataprep-rslex~=2.22.2dev0 (from azureml-dataprep<5.2.0a,>=5.1.0a->azureml-dataprep[parquet]<5.2.0a,>=5.1.0a->mltable)\n", "  Downloading azureml_dataprep_rslex-2.22.5-cp310-cp310-manylinux1_x86_64.whl.metadata (1.6 kB)\n", "Collecting cloudpickle<3.0.0,>=1.1.0 (from azureml-dataprep<5.2.0a,>=5.1.0a->azureml-dataprep[parquet]<5.2.0a,>=5.1.0a->mltable)\n", "  Downloading cloudpickle-2.2.1-py3-none-any.whl.metadata (6.9 kB)\n", "Collecting pyarrow>=0.17.0 (from azureml-dataprep[parquet]<5.2.0a,>=5.1.0a->mltable)\n", "  Downloading pyarrow-19.0.0-cp310-cp310-manylinux_2_28_x86_64.whl.metadata (3.3 kB)\n", "Collecting cffi>=1.12 (from cryptography!=1.9,!=2.0.*,!=2.1.*,!=2.2.*->mltable)\n", "  Downloading cffi-1.17.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting attrs>=22.2.0 (from jsonschema<5.0.0,>=4.0.0->mltable)\n", "  Downloading attrs-24.3.0-py3-none-any.whl.metadata (11 kB)\n", "Collecting jsonschema-specifications>=2023.03.6 (from jsonschema<5.0.0,>=4.0.0->mltable)\n", "  Downloading jsonschema_specifications-2024.10.1-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting referencing>=0.28.4 (from jsonschema<5.0.0,>=4.0.0->mltable)\n", "  Downloading referencing-0.36.1-py3-none-any.whl.metadata (2.8 kB)\n", "Collecting rpds-py>=0.7.1 (from jsonschema<5.0.0,>=4.0.0->mltable)\n", "  Downloading rpds_py-0.22.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.2 kB)\n", "Requirement already satisfied: packaging>=17.0 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from marshmallow>=3.5->azure-ai-ml) (24.2)\n", "Collecting portalocker<3,>=1.4 (from msal-extensions>=1.2.0->azure-identity)\n", "  Using cached portalocker-2.10.1-py3-none-any.whl.metadata (8.5 kB)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from msrest>=0.6.18->mltable) (2024.12.14)\n", "Collecting requests-oauthlib>=0.5.0 (from msrest>=0.6.18->mltable)\n", "  Using cached requests_oauthlib-2.0.0-py2.py3-none-any.whl.metadata (11 kB)\n", "Collecting azure-core-tracing-opentelemetry~=1.0.0b11 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading azure_core_tracing_opentelemetry-1.0.0b11-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting azure-monitor-opentelemetry-exporter~=1.0.0b31 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading azure_monitor_opentelemetry_exporter-1.0.0b33-py2.py3-none-any.whl.metadata (32 kB)\n", "Collecting opentelemetry-instrumentation-django~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_django-0.50b0-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting opentelemetry-instrumentation-fastapi~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.50b0-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting opentelemetry-instrumentation-flask~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_flask-0.50b0-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation-psycopg2~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_psycopg2-0.50b0-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting opentelemetry-instrumentation-requests~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_requests-0.50b0-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting opentelemetry-instrumentation-urllib~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_urllib-0.50b0-py3-none-any.whl.metadata (3.3 kB)\n", "Collecting opentelemetry-instrumentation-urllib3~=0.49b0 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_urllib3-0.50b0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting opentelemetry-resource-detector-azure~=0.1.4 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_resource_detector_azure-0.1.5-py3-none-any.whl.metadata (5.3 kB)\n", "Collecting opentelemetry-sdk~=1.28 (from azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_sdk-1.29.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-api<2.0.0,>=1.12.0 (from azure-core-tracing-opentelemetry~=1.0.0b11->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_api-1.29.0-py3-none-any.whl.metadata (1.4 kB)\n", "Collecting fixedint==0.1.6 (from azure-monitor-opentelemetry-exporter~=1.0.0b31->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading fixedint-0.1.6-py3-none-any.whl.metadata (4.8 kB)\n", "Collecting psutil~=5.9 (from azure-monitor-opentelemetry-exporter~=1.0.0b31->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading psutil-5.9.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)\n", "Collecting pycparser (from cffi>=1.12->cryptography!=1.9,!=2.0.*,!=2.1.*,!=2.2.*->mltable)\n", "  Downloading pycparser-2.22-py3-none-any.whl.metadata (943 bytes)\n", "Collecting opentelemetry-instrumentation-wsgi==0.50b0 (from opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_wsgi-0.50b0-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation==0.50b0 (from opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation-0.50b0-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting opentelemetry-semantic-conventions==0.50b0 (from opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_semantic_conventions-0.50b0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting opentelemetry-util-http==0.50b0 (from opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_util_http-0.50b0-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting wrapt<2.0.0,>=1.0.0 (from opentelemetry-instrumentation==0.50b0->opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading wrapt-1.17.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.4 kB)\n", "Collecting deprecated>=1.2.6 (from opentelemetry-semantic-conventions==0.50b0->opentelemetry-instrumentation-django~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Using cached Deprecated-1.2.15-py2.py3-none-any.whl.metadata (5.5 kB)\n", "Requirement already satisfied: importlib-metadata<=8.5.0,>=6.0 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from opentelemetry-api<2.0.0,>=1.12.0->azure-core-tracing-opentelemetry~=1.0.0b11->azure-monitor-opentelemetry->azure-ai-ml) (8.5.0)\n", "Collecting opentelemetry-instrumentation-asgi==0.50b0 (from opentelemetry-instrumentation-fastapi~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_asgi-0.50b0-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.50b0->opentelemetry-instrumentation-fastapi~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting opentelemetry-instrumentation-dbapi==0.50b0 (from opentelemetry-instrumentation-psycopg2~=0.49b0->azure-monitor-opentelemetry->azure-ai-ml)\n", "  Downloading opentelemetry_instrumentation_dbapi-0.50b0-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from requests>=2.21.0->azure-core!=1.22.0,<2.0.0,>=1.8.0->mltable) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from requests>=2.21.0->azure-core!=1.22.0,<2.0.0,>=1.8.0->mltable) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from requests>=2.21.0->azure-core!=1.22.0,<2.0.0,>=1.8.0->mltable) (2.3.0)\n", "Collecting oauthlib>=3.0.0 (from requests-oauthlib>=0.5.0->msrest>=0.6.18->mltable)\n", "  Using cached oauthlib-3.2.2-py3-none-any.whl.metadata (7.5 kB)\n", "Requirement already satisfied: zipp>=3.20 in /home/<USER>/anaconda3/envs/vllm/lib/python3.10/site-packages (from importlib-metadata<=8.5.0,>=6.0->opentelemetry-api<2.0.0,>=1.12.0->azure-core-tracing-opentelemetry~=1.0.0b11->azure-monitor-opentelemetry->azure-ai-ml) (3.21.0)\n", "Downloading mltable-1.6.1-py3-none-any.whl (189 kB)\n", "Downloading azure_ai_ml-1.24.0-py3-none-any.whl (12.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.3/12.3 MB\u001b[0m \u001b[31m93.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached azure_identity-1.19.0-py3-none-any.whl (187 kB)\n", "Using cached azure_common-1.1.28-py2.py3-none-any.whl (14 kB)\n", "Using cached azure_core-1.32.0-py3-none-any.whl (198 kB)\n", "Using cached azure_mgmt_core-1.5.0-py3-none-any.whl (30 kB)\n", "Downloading azure_storage_blob-12.24.1-py3-none-any.whl (408 kB)\n", "Downloading azure_storage_file_datalake-12.18.1-py3-none-any.whl (258 kB)\n", "Downloading azureml_dataprep-5.1.6-py3-none-any.whl (252 kB)\n", "Downloading cryptography-44.0.0-cp39-abi3-manylinux_2_28_x86_64.whl (4.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.2/4.2 MB\u001b[0m \u001b[31m100.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached isodate-0.7.2-py3-none-any.whl (22 kB)\n", "Downloading jsonschema-4.23.0-py3-none-any.whl (88 kB)\n", "Downloading marshmallow-3.25.1-py3-none-any.whl (49 kB)\n", "Using cached msal-1.31.1-py3-none-any.whl (113 kB)\n", "Using cached msal_extensions-1.2.0-py3-none-any.whl (19 kB)\n", "Using cached msrest-0.7.1-py3-none-any.whl (85 kB)\n", "Downloading pydash-8.0.5-py3-none-any.whl (102 kB)\n", "Downloading PyJWT-2.10.1-py3-none-any.whl (22 kB)\n", "Downloading PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (751 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m751.2/751.2 kB\u001b[0m \u001b[31m34.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading azure_monitor_opentelemetry-1.6.4-py3-none-any.whl (23 kB)\n", "Downloading azure_storage_file_share-12.20.1-py3-none-any.whl (286 kB)\n", "Downloading colorama-0.4.6-py2.py3-none-any.whl (25 kB)\n", "Using cached strictyaml-1.7.3-py3-none-any.whl (123 kB)\n", "Downloading attrs-24.3.0-py3-none-any.whl (63 kB)\n", "Downloading azure_core_tracing_opentelemetry-1.0.0b11-py3-none-any.whl (10 kB)\n", "Downloading azure_monitor_opentelemetry_exporter-1.0.0b33-py2.py3-none-any.whl (151 kB)\n", "Downloading fixedint-0.1.6-py3-none-any.whl (12 kB)\n", "Downloading azureml_dataprep_native-41.0.0-cp310-cp310-manylinux1_x86_64.whl (187 kB)\n", "Downloading azureml_dataprep_rslex-2.22.5-cp310-cp310-manylinux1_x86_64.whl (24.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.8/24.8 MB\u001b[0m \u001b[31m104.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading cffi-1.17.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (446 kB)\n", "Downloading cloudpickle-2.2.1-py3-none-any.whl (25 kB)\n", "Downloading jsonschema_specifications-2024.10.1-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_instrumentation_django-0.50b0-py3-none-any.whl (19 kB)\n", "Downloading opentelemetry_instrumentation-0.50b0-py3-none-any.whl (30 kB)\n", "Downloading opentelemetry_instrumentation_wsgi-0.50b0-py3-none-any.whl (13 kB)\n", "Downloading opentelemetry_semantic_conventions-0.50b0-py3-none-any.whl (166 kB)\n", "Downloading opentelemetry_api-1.29.0-py3-none-any.whl (64 kB)\n", "Downloading opentelemetry_util_http-0.50b0-py3-none-any.whl (6.9 kB)\n", "Downloading opentelemetry_instrumentation_fastapi-0.50b0-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.50b0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_instrumentation_flask-0.50b0-py3-none-any.whl (14 kB)\n", "Downloading opentelemetry_instrumentation_psycopg2-0.50b0-py3-none-any.whl (10 kB)\n", "Downloading opentelemetry_instrumentation_dbapi-0.50b0-py3-none-any.whl (11 kB)\n", "Downloading opentelemetry_instrumentation_requests-0.50b0-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation_urllib-0.50b0-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation_urllib3-0.50b0-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_resource_detector_azure-0.1.5-py3-none-any.whl (14 kB)\n", "Downloading opentelemetry_sdk-1.29.0-py3-none-any.whl (118 kB)\n", "Using cached portalocker-2.10.1-py3-none-any.whl (18 kB)\n", "Downloading pyarrow-19.0.0-cp310-cp310-manylinux_2_28_x86_64.whl (42.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.1/42.1 MB\u001b[0m \u001b[31m92.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n", "\u001b[?25hDownloading referencing-0.36.1-py3-none-any.whl (26 kB)\n", "Using cached requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)\n", "Downloading rpds_py-0.22.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (381 kB)\n", "Using cached oauthlib-3.2.2-py3-none-any.whl (151 kB)\n", "Downloading psutil-5.9.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (288 kB)\n", "Downloading wrapt-1.17.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (82 kB)\n", "Downloading pycparser-2.22-py3-none-any.whl (117 kB)\n", "Downloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Using cached Deprecated-1.2.15-py2.py3-none-any.whl (9.9 kB)\n", "Installing collected packages: fixedint, azureml-dataprep-rslex, azureml-dataprep-native, azure-common, wrapt, rpds-py, pyyaml, PyJWT, pydash, pycparser, pyarrow, psutil, portalocker, opentelemetry-util-http, oauthlib, marshmallow, isodate, colorama, cloudpickle, attrs, asgiref, strictyaml, requests-oauthlib, referencing, deprecated, cffi, azure-core, opentelemetry-api, msrest, jsonschema-specifications, cryptography, azure-mgmt-core, opentelemetry-semantic-conventions, jsonschema, azure-storage-file-share, azure-storage-blob, azure-core-tracing-opentelemetry, opentelemetry-sdk, opentelemetry-instrumentation, msal, azure-storage-file-datalake, opentelemetry-resource-detector-azure, opentelemetry-instrumentation-wsgi, opentelemetry-instrumentation-urllib3, opentelemetry-instrumentation-urllib, opentelemetry-instrumentation-requests, opentelemetry-instrumentation-dbapi, opentelemetry-instrumentation-asgi, msal-extensions, azure-monitor-opentelemetry-exporter, opentelemetry-instrumentation-psycopg2, opentelemetry-instrumentation-flask, opentelemetry-instrumentation-fastapi, opentelemetry-instrumentation-django, azure-identity, azureml-dataprep, azure-monitor-opentelemetry, azure-ai-ml, mltable\n", "  Attempting uninstall: psutil\n", "    Found existing installation: psutil 6.1.1\n", "    Uninstalling psutil-6.1.1:\n", "      Successfully uninstalled psutil-6.1.1\n", "Successfully installed PyJWT-2.10.1 asgiref-3.8.1 attrs-24.3.0 azure-ai-ml-1.24.0 azure-common-1.1.28 azure-core-1.32.0 azure-core-tracing-opentelemetry-1.0.0b11 azure-identity-1.19.0 azure-mgmt-core-1.5.0 azure-monitor-opentelemetry-1.6.4 azure-monitor-opentelemetry-exporter-1.0.0b33 azure-storage-blob-12.24.1 azure-storage-file-datalake-12.18.1 azure-storage-file-share-12.20.1 azureml-dataprep-5.1.6 azureml-dataprep-native-41.0.0 azureml-dataprep-rslex-2.22.5 cffi-1.17.1 cloudpickle-2.2.1 colorama-0.4.6 cryptography-44.0.0 deprecated-1.2.15 fixedint-0.1.6 isodate-0.7.2 jsonschema-4.23.0 jsonschema-specifications-2024.10.1 marshmallow-3.25.1 mltable-1.6.1 msal-1.31.1 msal-extensions-1.2.0 msrest-0.7.1 oauthlib-3.2.2 opentelemetry-api-1.29.0 opentelemetry-instrumentation-0.50b0 opentelemetry-instrumentation-asgi-0.50b0 opentelemetry-instrumentation-dbapi-0.50b0 opentelemetry-instrumentation-django-0.50b0 opentelemetry-instrumentation-fastapi-0.50b0 opentelemetry-instrumentation-flask-0.50b0 opentelemetry-instrumentation-psycopg2-0.50b0 opentelemetry-instrumentation-requests-0.50b0 opentelemetry-instrumentation-urllib-0.50b0 opentelemetry-instrumentation-urllib3-0.50b0 opentelemetry-instrumentation-wsgi-0.50b0 opentelemetry-resource-detector-azure-0.1.5 opentelemetry-sdk-1.29.0 opentelemetry-semantic-conventions-0.50b0 opentelemetry-util-http-0.50b0 portalocker-2.10.1 psutil-5.9.8 pyarrow-19.0.0 pycparser-2.22 pydash-8.0.5 pyyaml-6.0.2 referencing-0.36.1 requests-oauthlib-2.0.0 rpds-py-0.22.3 strictyaml-1.7.3 wrapt-1.17.2\n"]}], "source": ["# Install deps\n", "!pip install mltable azure-ai-ml azure-identity"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"gather": {"logged": 1737584588562}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Found the config file in: configs/Instant_apply_training_new.json\n"]}], "source": ["# Attach to mlclient\n", "import mltable\n", "from azure.ai.ml import MLClient\n", "from azure.identity import AzureCliCredential\n", "\n", "ml_client = MLClient.from_config(path=\"configs/Instant_apply_training_new.json\", credential=AzureCliCredential())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"gather": {"logged": 1737584588584}, "jupyter": {"outputs_hidden": false, "source_hidden": false}, "nteract": {"transient": {"deleting": false}}}, "outputs": [], "source": ["import azure.ai.ml._artifacts._artifact_utilities as artifact_utils\n", "import pandas as pd\n", "import json\n", "\n", "def convert(df):\n", "    df[\"input\"] = df[\"input\"].apply(json.loads)\n", "    df[\"output\"] = df[\"output\"].apply(json.loads)\n", "    df[\"elapsed\"] = df[\"output\"].apply(lambda d: d.get(\"elapsed\"))\n", "    df[\"output_text\"] = df[\"output\"].apply(lambda d: d[\"choices\"][0][\"message\"][\"content\"])\n", "    df[\"char_len\"] = df[\"output_text\"].apply(len)\n", "    print(\"Errored:\", df[\"output\"].apply(lambda d: \"error\" in d).sum())\n", "def load(name):\n", "    artifact_utils.download_artifact_from_aml_uri(uri = ml_client.data.get(name, version=\"1\").path, destination = f\"/tmp/{name}/\", datastore_operation=ml_client.datastores)\n", "    df = pd.read_csv(f\"/tmp/{name}/predictions.csv\", sep=\" \")\n", "    convert(df)\n", "    return df"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"gather": {"logged": 1737584588615}, "jupyter": {"outputs_hidden": false, "source_hidden": false}, "nteract": {"transient": {"deleting": false}}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Errored: 0\n", "Errored: 0\n", "Exact same between SD and non-SD: 0.9888888888888889\n", "mean(Baseline/SD): 5.911005833229049\n", "sum(Baseline)/sum(SD): 3.056867111904163\n", "Mean SD elapsed: 6.7923016899161865 seconds\n"]}], "source": ["non_sd_df = load(\"llama-full-non_sd-8xa100-output\")\n", "sd_df = load(\"llama-full-sd-8xa100-output\")\n", "\n", "orig_sd_df = sd_df\n", "non_sd_df = non_sd_df[non_sd_df[\"char_len\"]<100*1000]\n", "sd_df = sd_df[sd_df[\"char_len\"]<100*1000]\n", "\n", "print(\"Exact same between SD and non-SD:\", (sd_df[\"output_text\"] == non_sd_df[\"output_text\"]).mean())\n", "print(\"mean(Baseline/SD):\", (non_sd_df[\"elapsed\"]/ sd_df[\"elapsed\"]).dropna().mean())\n", "print(\"sum(Baseline)/sum(SD):\", non_sd_df[\"elapsed\"].sum()/ sd_df[\"elapsed\"].sum())\n", "print(\"Mean SD elapsed:\", sd_df[\"elapsed\"].mean(), \"seconds\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"gather": {"logged": 1737584588689}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4010485/1107375363.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sd_df[\"output_text\"] = sd_df[\"output\"].apply(lambda d: d[\"choices\"][0][\"message\"][\"content\"])\n", "/tmp/ipykernel_4010485/1107375363.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sd_df[\"char_len\"] = sd_df[\"output_text\"].apply(len)\n"]}, {"data": {"text/plain": ["<Axes: title={'center': 'llama3.3 non-speculative'}, xlabel='char_len', ylabel='elapsed'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.style.use('ggplot')\n", "sd_df[\"output_text\"] = sd_df[\"output\"].apply(lambda d: d[\"choices\"][0][\"message\"][\"content\"])\n", "non_sd_df[\"output_text\"] = non_sd_df[\"output\"].apply(lambda d: d[\"choices\"][0][\"message\"][\"content\"])\n", "sd_df[\"char_len\"] = sd_df[\"output_text\"].apply(len)\n", "non_sd_df[\"char_len\"] = non_sd_df[\"output_text\"].apply(len)\n", "sd_df[sd_df[\"char_len\"]<100000].plot.scatter(y=\"elapsed\", x=\"char_len\",alpha = 0.2, title=\"llama3.3 speculative\")\n", "non_sd_df[non_sd_df[\"char_len\"]<100000].plot.scatter(y=\"elapsed\", x=\"char_len\",alpha = 0.2, title=\"llama3.3 non-speculative\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"gather": {"logged": 1737584588793}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Errored: 0\n", "Exact same between SD and non-SD: 0.9361111111111111\n", "mean(SD/SDQuant): 0.9477684158017606\n", "sum(SD)/sum(SDQuant): 0.9949320910166671\n", "Mean SD elapsed: 6.7923016899161865 seconds\n", "Mean SDQuant elapsed: 6.8268997967243195 seconds\n"]}], "source": ["df = load(\"llama-full-sd-4b-8xa100-output\")\n", "df = df[orig_sd_df[\"char_len\"]<100*1000]\n", "\n", "print(\"Exact same between SD and non-SD:\", (sd_df[\"output_text\"] == df[\"output_text\"]).mean())\n", "print(\"mean(SD/SDQuant):\", (sd_df[\"elapsed\"]/ df[\"elapsed\"]).dropna().mean())\n", "print(\"sum(SD)/sum(SDQuant):\", sd_df[\"elapsed\"].sum()/ df[\"elapsed\"].sum())\n", "print(\"Mean SD elapsed:\", sd_df[\"elapsed\"].mean(), \"seconds\")\n", "print(\"Mean SDQuant elapsed:\", df[\"elapsed\"].mean(), \"seconds\")"]}], "metadata": {"kernel_info": {"name": "python38-azureml"}, "kernelspec": {"display_name": "vllm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}, "microsoft": {"host": {"AzureML": {"notebookHasBeenCompleted": true}}, "ms_spell_check": {"ms_spell_check_language": "en"}}, "nteract": {"version": "nteract-front-end@1.0.0"}}, "nbformat": 4, "nbformat_minor": 2}