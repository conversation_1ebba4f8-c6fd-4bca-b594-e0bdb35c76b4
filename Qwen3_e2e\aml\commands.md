# 01-08-2024

Deploy qwen and llama baselines:

```bash
# Qwen2.5 base
python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-inference-qwen-base
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-inference-qwen-base --deployment_name qwen-base --environment_name LlamaFactoryOptimized:3 --sku Standard_NC24ads_A100_v4 --model_name_or_path Qwen/Qwen2.5-Coder-32B-Instruct --template qwen

# Qwen2.5 merge
python manage.py model --config configs/securityfilter_ws.json --model_name merged_qwen25_10k_fa2_32kcon_1218 --model_path /datadisk/merges/qwen25_10k_fa2_32kcon_1218 --model_description "Finetuned adapter merged with Qwen2.5 for FastApply."
python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-lora-merged
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-lora-merged --deployment_name qwen-lora-merged --model_name merged_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:3 --sku Standard_NC24ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen

# Qwen2.5 full
python manage.py download --data_asset_name qwen25_10k_fa2_32kcon_1218_full --data_asset_destination /datadisk/full_models/qwen25_10k_fa2_32kcon_1218 --config configs/H100CentralUS.json
python manage.py model --config configs/securityfilter_ws.json --model_name full_qwen25_10k_fa2_32kcon_1218 --model_path /datadisk/full_models/qwen25_10k_fa2_32kcon_1218 --model_description "Full finetuned Qwen2.5 for FastApply."
python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:3 --sku Standard_NC24ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen
```

```bash
# LLAMA3.3 base
python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-inference-llama-base
!! python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-inference-llama-base --deployment_name llama-base --environment_name LlamaFactoryOptimized:3 --sku Standard_ND96amsr_A100_v4 --model_name_or_path meta-llama/Llama-3.3-70B-Instruct --template llama

# LLAMA3.3 merge
!! python manage.py model --config configs/Instant_apply_training_new.json --model_name merged_llama33_10k_fa2_32kcon_1218 --model_path /datadisk/merges/llama33_10k_fa2_32kcon_1218 --model_description "Finetuned adapter merged with LLAMA 3.3 for FastApply."
!! python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-lora-merged
!! python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-lora-merged --deployment_name llama-lora-merged --model_name merged_llama33_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:3 --sku Standard_ND96amsr_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama33_10k_fa2_32kcon_1218' --template llama3

# LLAMA3.3 full
python manage.py download --data_asset_name llama3_30k_fa2_32kcon_1218_full --data_asset_destination /datadisk/full_models/llama3_30k_fa2_32kcon_1218 --config configs/H100CentralUS.json
python manage.py model --config configs/Instant_apply_training_new.json --model_name full_llama33_10k_fa2_32kcon_1218 --model_path /datadisk/full_models/llama3_30k_fa2_32kcon_1218 --model_description "Full finetuned LLAMA 3.3 for FastApply."
python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full
python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:3 --environment_name LlamaFactoryOptimized:3 --sku Standard_ND96amsr_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3
```

```bash
# LLAMA3.1 base
# Make sure HF_TOKEN is defined!
python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama31-base
python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama31-base --deployment_name llama-31-base --environment_name LlamaFactoryOptimized:3 --sku Standard_ND96amsr_A100_v4 --model_name_or_path meta-llama/Llama-3.1-70B-Instruct --template llama3
```

```bash
# Updated environment
docker build --progress=plain -t 1e6d60a2ac124c168aa42090bc1ecc97.azurecr.io/fastapply:vllm-sd -f aml/environment/Dockerfile . 2>&1 | tee /datadisk/docker-build2.log
docker push 1e6d60a2ac124c168aa42090bc1ecc97.azurecr.io/fastapply:vllm-sd
az ml environment create -i "1e6d60a2ac124c168aa42090bc1ecc97.azurecr.io/fastapply:vllm-sd" -n "LlamaFactoryOptimized" -w "securityfilter_ws" -g "supplychain_security" --description "Environment for FastApply training and inference."
# version 7

python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-vllm2
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-vllm2 --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --sku Standard_NC48ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --vllm_backend

python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-staticsd
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-staticsd --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --sku Standard_NC24ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --static_speculation

python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-ngramsd
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-ngramsd --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --sku Standard_NC24ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --ngram_speculation

python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-modelsd
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-qwen-full-modelsd --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --sku Standard_NC24ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --model_speculation --speculative_model 'Qwen/Qwen2.5-Coder-3B-Instruct'

# LLAMA full SD
az ml environment create -i "33d8199eb3c44b54a1b565d116da3403.azurecr.io/fastapply:vllm-sd" -n "LlamaFactoryOptimized" -w "Instant_apply_training_new" -g "zijianjin-rg" --description "Environment for FastApply training and inference."
# version 4

python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full-vllm
python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full-vllm --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:3 --environment_name LlamaFactoryOptimized:4 --sku Standard_ND96amsr_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --vllm_backend

python manage.py endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full-staticsd
python manage.py deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-llama-full-staticsd --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:3 --environment_name LlamaFactoryOptimized:4 --sku Standard_ND96amsr_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --static_speculation

azcopy copy --recursive 'https://h100centralus3428857399.blob.core.windows.net/azureml-blobstore-ffd3dac5-55ff-4991-91d7-89714ac4b778/alexjin/full/llama3_30k_fa2_32kcon_1218' 'https://supplychainsecurity.blob.core.windows.net/azureml-blobstore-1e6d60a2-ac12-4c16-8aa4-2090bc1ecc97/alexjin/full/llama3_30k_fa2_32kcon_1218'
python manage.py model --config configs/securityfilter_ws.json --model_name full_llama33_10k_fa2_32kcon_1218 --model_path 'https://supplychainsecurity.blob.core.windows.net/azureml-blobstore-1e6d60a2-ac12-4c16-8aa4-2090bc1ecc97/alexjin/full/llama3_30k_fa2_32kcon_1218' --model_description "Full finetuned LLAMA 3.3 for FastApply."
python manage.py endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-llama-full-vllm
python manage.py deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-llama-full-vllm --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --sku Standard_NC48ads_A100_v4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --vllm_backend
```

```bash
python manage.py batch-endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply-llama-full-qwen-vllm
python manage.py batch-deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-llama-full-qwen-vllm --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --cluster_name gpu-cluster-a100 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --vllm_backend
python manage.py batch-deployment --config configs/securityfilter_ws.json --endpoint_name fastapply-llama-full-qwen-vllm --deployment_name qwen-base --environment_name LlamaFactoryOptimized:7 --cluster_name gpu-cluster-a100 --model_name_or_path 'Qwen/Qwen2.5-Coder-32B-Instruct' --template qwen --vllm_backend

python manage.py batch-endpoint --config configs/securityfilter_ws.json --endpoint_name fastapply
python manage.py batch-deployment --config configs/securityfilter_ws.json --endpoint_name fastapply --deployment_name qwen-full-vllm --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --cluster_name a100-big --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --vllm_backend
python manage.py batch-deployment --config configs/securityfilter_ws.json --endpoint_name fastapply --deployment_name qwen-full-staticsd --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:7 --cluster_name a100-big --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --static_speculation

python manage.py batch-endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply
python manage.py batch-deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:4 --environment_name LlamaFactoryOptimized:7 --cluster_name a100 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --vllm_backend --quantized
python manage.py batch-deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply --deployment_name llama-full-staticsd --model_name full_llama33_10k_fa2_32kcon_1218:4 --environment_name LlamaFactoryOptimized:7 --cluster_name a100 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --static_speculation --quantized

python manage.py batch-deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply --deployment_name qwen-full --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:4 --cluster_name a100 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --vllm_backend
python manage.py batch-deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply --deployment_name qwen-full-staticsd --model_name full_qwen25_10k_fa2_32kcon_1218:1 --environment_name LlamaFactoryOptimized:4 --cluster_name a100 --model_name_or_path '${AZUREML_MODEL_DIR}/qwen25_10k_fa2_32kcon_1218' --template qwen --static_speculation

python manage.py batch-job --config configs/Instant_apply_training_new.json --endpoint_name fastapply --deployment_name qwen-full --data_asset_name pr_data --data_asset_version 1

python manage.py batch-endpoint --config configs/Instant_apply_training_new.json --endpoint_name fastapply-4-a100
python manage.py batch-deployment --config configs/Instant_apply_training_new.json --endpoint_name fastapply-4-a100 --deployment_name llama-full --model_name full_llama33_10k_fa2_32kcon_1218:4 --environment_name LlamaFactoryOptimized:7 --cluster_name a100-4 --model_name_or_path '${AZUREML_MODEL_DIR}/llama3_30k_fa2_32kcon_1218' --template llama3 --vllm_backend --quantized

az ml component create --file component.yaml -w Instant_apply_training_new -g zijianjin-rg
# created jobs in portal
```
