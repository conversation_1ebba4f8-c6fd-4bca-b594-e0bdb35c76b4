from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import command
from azure.ai.ml import Input, Output
from azure.ai.ml.entities import ResourceConfiguration
from azure.ai.ml import MLClient
from datetime import datetime
from argparse import ArgumentParser
from azure.ai.ml.entities import JupyterLabJobService, VsCodeJobService, TensorBoardJobService, SshJobService
import os
import webbrowser

from azure.ai.ml import MLClient, command, Input
from azure.ai.ml.constants import AssetTypes, InputOutputModes


# read in the file path as string
file_path = "./tokens/.hf.txt"

with open(file_path, "r") as file:
    hf_token = file.read().strip()


if __name__=='__main__':
 
    parser = ArgumentParser()
    parser.add_argument('--cluster', type=str, default="h100centralusvc")
    parser.add_argument('--resource_group', type=str, default="SingularityH100")
    parser.add_argument('--workspace', type=str, default="H100CentralUS")
    parser.add_argument('--subscription_id', type=str, default="d0c05057-7972-46ff-9bcf-3c932250155e")
 
    args = parser.parse_args()
 
    resource_group = args.resource_group
    workspace = args.workspace
    subscription_id = args.subscription_id
    cluster_name = args.cluster
 
    try:
        credential = DefaultAzureCredential()
        # Check if given credential can get token successfully.
        credential.get_token("https://management.azure.com/.default")
    except Exception as ex:
        print('Failed to get token with DefaultAzureCredential, fall back to InteractiveBrowserCredential', ex)
        # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work
        credential = InteractiveBrowserCredential()
 
    # get a handle to the workspace
    ml_client = MLClient(
        subscription_id=subscription_id,
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential,
    )
    # data_asset = ml_client.data.get("Instant200k", version="1")
    cpu_cluster = None
    gpu_cluster = args.cluster
    vc_cluster = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.MachineLearningServices/virtualclusters/{cluster_name}"
 
    # Get the current date and time
    now = datetime.now()
    timestamp = now.strftime("%Y-%m-%d_%H-%M-%S")
    command_str = """
    RANK=${OMPI_COMM_WORLD_RANK:-${RANK:-0}}
    export DISABLE_VERSION_CHECK=1
    pip install --upgrade r requirements.txt
    pip install --upgrade peft==0.15.1

    echo "Current transformers version:"
    python -c "import transformers; print(transformers.__version__)"
    # Force reinstall transformers to ensure we get 4.51.0
    pip install --force-reinstall transformers==4.51.0
    echo "After update:"
    python -c "import transformers; print(transformers.__version__)"
    python -c "from transformers import AutoConfig; print('Qwen3 test:', 'qwen3' in AutoConfig)"
    echo "Rank $RANK: Starting training..."
        
    python3 \
    src/train.py \
    --stage sft \
    --do_train \
    --model_name_or_path Qwen/Qwen3-4B \
    --trust_remote_code \
    --dataset alpaca_en_demo \
    --cutoff_len 8000 \
    --max_samples 70000 \
    --template qwen \
    --finetuning_type full \
    --lora_target all \
    --output_dir  ${{outputs.datastore_dir}} \
    --overwrite_cache \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 1 \
    --lr_scheduler_type cosine \
    --logging_steps 100 \
    --save_steps 500 \
    --learning_rate 1e-5 \
    --num_train_epochs 1.0 \
    --plot_loss \
    --bf16 \
    --flash_attn fa2 \
    --enable_liger_kernel \
    --deepspeed examples/deepspeed/ds_z3_config.json
    """
    # command_str = """
    # sleep infinity
    # """
   
    disply_name = "qwen3-4b-xh"
    training_job = command(
        # local path where the code is stored
        code=".",
        # describe the command to run the python script, with all its parameters
        # use the syntax below to inject parameter values from code
        command=command_str,
        inputs={
        #   "data": Input(path=data_asset.id,
        #       type=AssetTypes.URI_FILE,
        #       mode=InputOutputModes.RO_MOUNT
        #       )
        },
        outputs={
            "datastore_dir": Output(
                type="uri_folder",
                # path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/alex/upgrade_sft_8k/",
                path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/yuhu/xtab_{timestamp}/",
                # path="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/AML-NorwayEast/workspaces/AML-NorwayEast/datastores/workspaceblobstore/paths/ryangabriel/",
                # path="azureml://datastores/workspaceblobstore/paths/tutorial-datasets/places2/train/",
                mode="rw_mount",
            ),
        },
        environment="azureml://registries/zijian/environments/LlamaFactoryOptimized/versions/8",
        environment_variables= {
            "HF_TOKEN": hf_token,
            "_AZUREML_SINGULARITY_JOB_UAI":"/subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourceGroups/SingularityH100/providers/Microsoft.ManagedIdentity/userAssignedIdentities/singularityh100"
        },
        compute=vc_cluster,
        resources={
            "instance_count": 1,
            "instance_type": "Singularity.ND96_H100_v5",
            "properties": {
                "singularity": {
                    "interactive": True,
                    "imageVersion": '',
                    "slaTier": "Premium",
                    "priority": "high",
                    "tensorboardLogDirectory": "/scratch/tensorboard_logs",
                    "enableAzmlInt": False,
                }
            },
        },
        services={
        "My_jupyterlab": JupyterLabJobService(
            # nodes="all" # For distributed jobs, use the `nodes` property to pick which node you want to enable interactive services on. If `nodes` are not selected, by default, interactive applications are only enabled on the head node. Values are "all", or compute node index (for ex. "0", "1" etc.)
        ),
        "My_vscode": VsCodeJobService(
            # nodes="all"
        ),
        "My_tensorboard": TensorBoardJobService(
            # nodes="all",
            log_dir="output/tblogs"  # relative path of Tensorboard logs (same as in your training script)         
        ),
        "My_ssh": SshJobService(
            ssh_public_keys="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDE0nI+zXB9QPBVaeNztf+v7e7b+bjhbAHw3RHh5aD+yZo8f8CWnnjajkpsDPeOOolwFJGhUX+Zi02Zn1KIL2jZTSGI3/WHGAko66LbRVPn4VGROYjv90SwPBA0AUcCvBEuP5V1dYEe+LRPgrM9WoaANnqGIpVZbXXKPbVS6ZyblPkTwIcN5jRGjauHMnx4NPzjJzWJV2V1yNJZ1MiWuuQWMv8WygjZmO5bV6R2QCxSsOupiwHee3T7qBtMrr/U4134trGHHGtHipWcg4xgxNGoJ9T/emGrt4FnSDZueH1qmC/gqubBm/VSM9d9He7QYqcQPepIZxrlayR6mHVtiuDyWzm0oI2w35GG3oA8y2dmPH9FTvuuSCN/eYxMPnvwHl/XwtamFrZz63pC6hbSmynFEFRX2jQVci+0kxpDhW1EjuwrskXq3LSbhLIhd1U1G1+q3wlxPxijqVJxkqS7MKvtzduvr+dX36oNVrSUyHfe9RJiQhArY1KMWgq45GPHaHU0t/izMZbCuwW9A4THZOSLFH0bTVJDEuXm5YKmz9OiKT/AOEUzBEeOABTtJdjUBGAIdQT0iLqohHuT9pJ+HrE15M7dlo6cSzb0ZIuVmFtKKcUBPLdbE0rA2u8Cgbbg/zw2Zqkov3lyYDZDFVP5cMix0yeSqV6Z18HgJiKRFReviQ== <EMAIL>",
            # nodes="all"  
        ),
        },
        # compute="gpu-compute2",
        distribution={
        "type": "PyTorch",
        # set process count to the number of gpus on the node
        # NC6 has only 1, A100 has 8
        "process_count_per_instance": 8,
    },
    # set instance count to the number of nodes you want to use
    # instance_count=1,
    display_name=f'{disply_name}_{timestamp}',
    description="llama factory finetuning",
)
 
    # submit the job
    returned_job = ml_client.jobs.create_or_update(
        training_job,
        # Project's name
        experiment_name="qwen3-exp-alpaca",
    )
 
    # get a URL for the status of the job
    print("The url to see your live job running is returned by the sdk:")
    print(returned_job.studio_url)
    # open the browser with this url
    # webbrowser.open(returned_job.studio_url)
 
    # print the pipeline run id
    print(
        f"The pipeline details can be access programmatically using identifier: {returned_job.name}"
    )
    # saving it for later in this notebook
    small_scale_run_id = returned_job.name