import urllib.request
import json
import os
import ssl

def allowSelfSignedHttps(allowed):
    # bypass the server certificate verification on client side
    if allowed and not os.environ.get('PYTHONHTTPSVERIFY', '') and getattr(ssl, '_create_unverified_context', None):
        ssl._create_default_https_context = ssl._create_unverified_context

allowSelfSignedHttps(True) # this line is needed if you use self-signed certificate in your scoring service.

# Request data goes here
# The example below assumes JSON formatting which may be updated
# depending on the format your endpoint expects.
# More information can be found here:
# https://docs.microsoft.com/azure/machine-learning/how-to-deploy-advanced-entry-script
datas = []
with open("maas_data_1000.jsonl") as f:
    for line in f:
        datas.append(json.loads(line))
import tqdm
import time
with open("maas_responses_1000.jsonl", "w") as f:
    for data in tqdm.tqdm(datas):
        begin = time.time()
        body = str.encode(json.dumps({"model": "", "messages": data["messages"][:-1]}))

        url = 'https://fastapply-inference.westus2.inference.ml.azure.com/score'
        # Replace this with the primary/secondary key, AMLToken, or Microsoft Entra ID token for the endpoint
        api_key = os.environ["AZUREML_TOKEN"]
        if not api_key:
            raise Exception("A key should be provided to invoke the endpoint")


        headers = {'Content-Type':'application/json', 'Authorization':('Bearer '+ api_key)}

        req = urllib.request.Request(url, body, headers)

        try:
            response = urllib.request.urlopen(req)

            result = json.loads(response.read().decode('utf-8'))
        except urllib.error.HTTPError as error:
            result = {"code": error.code, "body": error.read().decode('utf-8'), "info": dict(error.info())}
        end = time.time()
        result = {
            "input": data,
            "output": result,
            "elapsed": end - begin,
        }
        f.write(json.dumps(result) + "\n")
