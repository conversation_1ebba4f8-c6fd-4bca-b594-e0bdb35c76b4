import json
import os
from IPython.display import HTM<PERSON>, display
from datetime import datetime
import textwrap
from .file_loader import (
    load_jsonl_as_dict,
    load_file_or_data_asset,
    load_trajectory_data,
)
from .paths import DEBUG_DIR
from azure.ai.ml import M<PERSON><PERSON>
from typing import Optional, Dict, Any


class TrajectoryVisualizer:
    def __init__(
        self,
        trajectory_path=None,
        ml_client: Optional[MLClient] = None,
        use_cache: bool = True,
        debug_dataset_path: Optional[str] = None,  # Add this parameter
    ):
        """
        Initialize the visualizer.

        Args:
            trajectory_path: Path to trajectory file (local path, Azure ML data asset reference, or URI)
            ml_client: Optional MLClient instance for Azure ML operations
            use_cache: Whether to cache loaded trajectories (default: True)
            debug_dataset_path: Optional path to debug dataset JSON file
        """
        self.trajectory_path = trajectory_path
        self.ml_client = ml_client
        self.current_trajectory = None
        self.use_cache = use_cache
        self._cached_trajectories: Optional[Dict[str, Any]] = None
        self.debug_dataset_path = debug_dataset_path
        self._debug_data: Optional[Dict[str, Any]] = None

    def load_all_trajectories(self) -> Dict[str, Any]:
        """
        Load all trajectories from the file/data asset. Uses cache if enabled.

        Returns:
            Dictionary of all trajectories
        """
        if self.use_cache and self._cached_trajectories is not None:
            print("📦 Using cached trajectories")
            return self._cached_trajectories

        print("📥 Loading trajectories from source...")
        trajectories = load_trajectory_data(self.trajectory_path, self.ml_client)

        if self.use_cache:
            self._cached_trajectories = trajectories
            print(f"💾 Cached {len(trajectories)} trajectories")

        return trajectories

    def clear_cache(self):
        """Clear the cached trajectories."""
        self._cached_trajectories = None
        print("🗑️ Cache cleared")

    def load_trajectory(self, instance_id=None, trajectory_data=None):
        """
        Load a trajectory by instance ID and file path, or direct data.

        Args:
            instance_id: ID to look up in trajectory file
            trajectory_data: Pre-loaded trajectory dictionary
        """
        if trajectory_data:
            self.current_trajectory = trajectory_data
        elif self.trajectory_path and instance_id:
            # Load all trajectories (will use cache if enabled)
            trajectories = self.load_all_trajectories()

            if instance_id not in trajectories:
                raise ValueError(
                    f"Instance ID {instance_id} not found in trajectory file."
                )

            # Extract the trajectory from the data structure
            traj = trajectories[instance_id]["loc_trajs"]["trajs"][0]
            self.current_trajectory = traj

            # Store instance_id and raw_output_loc for later use
            self.current_instance_id = instance_id
            raw_output = trajectories[instance_id].get("raw_output_loc", "")
            # Handle case where raw_output_loc is a list with one string element
            if isinstance(raw_output, list) and len(raw_output) == 1:
                self.current_raw_output = raw_output[0]
            else:
                self.current_raw_output = raw_output
        else:
            raise ValueError(
                "Must provide either trajectory_data, or trajectory_path with instance_id"
            )

        # Load debug data if available
        if self.debug_dataset_path and hasattr(self, "current_instance_id"):
            self._load_debug_data()

        return self

    def _load_debug_data(self):
        """Load debug dataset if path is provided."""
        if self.debug_dataset_path and self._debug_data is None:
            try:
                with open(self.debug_dataset_path, "r", encoding="utf-8") as f:
                    debug_instances = json.load(f)
                    # Convert list to dict for easier lookup
                    self._debug_data = {
                        item["instance_id"]: item for item in debug_instances
                    }
            except Exception as e:
                print(f"Warning: Could not load debug dataset: {e}")
                self._debug_data = {}

    def get_available_instances(self) -> list:
        """Get list of available instance IDs."""
        trajectories = self.load_all_trajectories()
        return list(trajectories.keys())

    @classmethod
    def from_azure_ml(
        cls,
        trajectory_path: str,
        subscription_id: str,
        resource_group: str,
        workspace: str,
        use_cache: bool = True,
    ):
        """
        Create a TrajectoryVisualizer instance with Azure ML client configured.

        Args:
            trajectory_path: Azure ML data asset reference or URI
            subscription_id: Azure subscription ID
            resource_group: Resource group name
            workspace: Workspace name
            use_cache: Whether to cache loaded trajectories (default: True)

        Returns:
            TrajectoryVisualizer instance
        """
        from .file_loader import get_ml_client

        ml_client = get_ml_client(subscription_id, resource_group, workspace)
        return cls(
            trajectory_path=trajectory_path, ml_client=ml_client, use_cache=use_cache
        )

    def visualize(
        self, show_full_content=False, max_content_length=500, save_to_file=None
    ):
        """
        Create an interactive HTML visualization of the trajectory.

        Args:
            show_full_content: If False, truncate long content
            max_content_length: Maximum characters to show for truncated content
            save_to_file: Filename (will be saved to debug/ directory) or full path
        """
        if not self.current_trajectory:
            raise ValueError("No trajectory loaded. Call load_trajectory() first.")

        messages = self.current_trajectory["messages"]

        # CSS styles - add new styles for debug info
        styles = """
        <style>
            .traj-container {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                background: #f5f5f5;
                padding: 20px;
            }
            .message-block {
                margin: 15px 0;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .user-message {
                background: #e3f2fd;
                border-left: 4px solid #2196F3;
            }
            .assistant-message {
                background: #f3e5f5;
                border-left: 4px solid #9C27B0;
            }
            .tool-call {
                background: #fff3e0;
                border-left: 4px solid #FF9800;
            }
            .tool-response {
                background: #e8f5e9;
                border-left: 4px solid #4CAF50;
            }
            .message-header {
                padding: 12px 16px;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
            }
            .message-content {
                padding: 0 16px 16px 16px;
                white-space: pre-wrap;
                font-size: 14px;
                line-height: 1.5;
            }
            .collapsible {
                background: #f0f0f0;
                padding: 8px 12px;
                margin: 8px 0;
                border-radius: 4px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .collapsible:hover {
                background: #e0e0e0;
            }
            .collapsed-content {
                display: none;
                padding: 12px;
                background: white;
                border-radius: 4px;
                margin-top: 8px;
                border: 1px solid #ddd;
            }
            .tool-args {
                background: #f5f5f5;
                padding: 8px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                overflow-x: auto;
            }
            .metadata {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .step-number {
                background: #666;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
            }
            .toggle-btn {
                background: #2196F3;
                color: white;
                border: none;
                padding: 4px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            }
            .toggle-btn:hover {
                background: #1976D2;
            }
            code {
                background: #f5f5f5;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: monospace;
            }
            .observation-preview {
                background: #fafafa;
                padding: 8px;
                border-radius: 4px;
                margin-top: 8px;
                font-size: 13px;
                color: #666;
            }
            .debug-info {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .label-success {
                background: #4CAF50;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            .label-failure {
                background: #f44336;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            .predictions-list, .ground-truth-list {
                margin: 10px 0;
                padding-left: 20px;
            }
            .raw-output-section {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-top: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .tools-section {
                background: white;
                padding: 16px;
                border-radius: 8px;
                margin-top: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .tool-definition {
                background: #f9f9f9;
                padding: 12px;
                margin: 8px 0;
                border-radius: 6px;
                border-left: 3px solid #2196F3;
            }
        </style>
        """

        # JavaScript for interactivity
        script = """
        <script>
        function toggleContent(id) {
            var content = document.getElementById(id);
            var button = document.getElementById('btn-' + id);
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                button.textContent = 'Hide';
            } else {
                content.style.display = 'none';
                button.textContent = 'Show';
            }
        }
        
        function toggleAll(show) {
            var contents = document.getElementsByClassName('collapsed-content');
            var buttons = document.getElementsByClassName('toggle-btn');
            for (var i = 0; i < contents.length; i++) {
                contents[i].style.display = show ? 'block' : 'none';
                buttons[i].textContent = show ? 'Hide' : 'Show';
            }
        }
        
        // Initialize all collapsed content to be hidden
        window.onload = function() {
            var contents = document.getElementsByClassName('collapsed-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].style.display = 'none';
            }
        }
        </script>
        """

        # Build HTML
        html = styles + script + '<div class="traj-container">'

        # Add metadata
        if "usage" in self.current_trajectory:
            usage = self.current_trajectory["usage"]
            time = self.current_trajectory.get("time", "N/A")
            html += f"""
            <div class="metadata">
                <h2>Trajectory Metadata</h2>
                <p><strong>Total Tokens:</strong> {usage["prompt_tokens"] + usage["completion_tokens"]:,} 
                   (Prompt: {usage["prompt_tokens"]:,}, Completion: {usage["completion_tokens"]:,})</p>
                <p><strong>Execution Time:</strong> {time:.2f}s</p>
                <p><strong>Total Messages:</strong> {len(messages)}</p>
                <button class="toggle-btn" onclick="toggleAll(true)">Expand All</button>
                <button class="toggle-btn" onclick="toggleAll(false)">Collapse All</button>
            </div>
            """

        # Add debug info if available
        if (
            self._debug_data
            and hasattr(self, "current_instance_id")
            and self.current_instance_id in self._debug_data
        ):
            debug_info = self._debug_data[self.current_instance_id]
            label = debug_info.get("label", "unknown")
            label_class = "label-success" if label == "success" else "label-failure"

            html += f"""
            <div class="debug-info">
                <h2>Debug Information</h2>
                <p><strong>Instance ID:</strong> {self.current_instance_id}</p>
                <p><strong>Label:</strong> <span class="{label_class}">{label.upper()}</span></p>
                
                <h3>Predictions</h3>
                <div class="predictions-list">
            """

            predictions = debug_info.get("predictions", {})
            for key, items in predictions.items():
                if items:
                    html += f"<p><strong>{key}:</strong></p><ul>"
                    for item in items:
                        html += f"<li><code>{self._escape_html(str(item))}</code></li>"
                    html += "</ul>"
                else:
                    html += f"<p><strong>{key}:</strong> <em>None found</em></p>"

            html += """
                </div>
                
                <h3>Ground Truth</h3>
                <div class="ground-truth-list">
            """

            ground_truth = debug_info.get("ground_truth", {})
            for key, items in ground_truth.items():
                if items:
                    html += f"<p><strong>{key}:</strong></p><ul>"
                    for item in items:
                        html += f"<li><code>{self._escape_html(str(item))}</code></li>"
                    html += "</ul>"
                else:
                    html += f"<p><strong>{key}:</strong> <em>None</em></p>"

            html += """
                </div>
            </div>
            """

        # Process messages
        step_counter = 0
        for i, msg in enumerate(messages):
            role = msg["role"]

            if role == "user":
                step_counter += 1
                html += f'<div class="message-block user-message">'
                html += f'<div class="message-header">👤 User Input <span class="step-number">Step {step_counter}</span></div>'
                content = msg["content"]
                if not show_full_content and len(content) > max_content_length:
                    preview = content[:max_content_length] + "..."
                    html += f'<div class="message-content">{self._escape_html(preview)}</div>'
                    html += f'<div class="collapsible" onclick="toggleContent(\'user-{i}\')">'
                    html += f"<span>Show full content ({len(content)} chars)</span>"
                    html += f'<button id="btn-user-{i}" class="toggle-btn">Show</button></div>'
                    html += f'<div id="user-{i}" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: inherit; font-size: 14px; margin: 0;">{self._escape_html(content)}</pre></div>'
                else:
                    html += f'<div class="message-content">{self._escape_html(content)}</div>'
                html += "</div>"

            elif role == "assistant":
                if msg.get("tool_calls"):
                    html += '<div class="message-block tool-call">'
                    html += f'<div class="message-header">🤖 Assistant → Tool Calls ({len(msg["tool_calls"])} calls)</div>'
                    html += '<div class="message-content">'

                    for j, call in enumerate(msg["tool_calls"]):
                        func_name = call["function"]["name"]
                        func_args = call["function"]["arguments"]
                        call_id = call["id"]

                        html += f'<div class="collapsible" onclick="toggleContent(\'call-{i}-{j}\')">'
                        html += f"<span>📞 <code>{func_name}</code></span>"
                        html += f'<button id="btn-call-{i}-{j}" class="toggle-btn">Show</button></div>'

                        html += f'<div id="call-{i}-{j}" class="collapsed-content">'
                        html += (
                            f"<p><strong>Call ID:</strong> <code>{call_id}</code></p>"
                        )
                        html += f"<p><strong>Arguments:</strong></p>"
                        html += f'<div class="tool-args">{self._format_json(func_args)}</div>'
                        html += "</div>"

                    html += "</div></div>"

                elif msg.get("content"):
                    html += '<div class="message-block assistant-message">'
                    html += '<div class="message-header">🤖 Assistant Response</div>'
                    content = msg["content"]
                    if not show_full_content and len(content) > max_content_length:
                        preview = content[:max_content_length] + "..."
                        html += f'<div class="message-content">{self._escape_html(preview)}</div>'
                        html += f'<div class="collapsible" onclick="toggleContent(\'assistant-{i}\')">'
                        html += (
                            f"<span>Show full response ({len(content)} chars)</span>"
                        )
                        html += f'<button id="btn-assistant-{i}" class="toggle-btn">Show</button></div>'
                        html += f'<div id="assistant-{i}" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: inherit; font-size: 14px; margin: 0;">{self._escape_html(content)}</pre></div>'
                    else:
                        html += f'<div class="message-content">{self._escape_html(content)}</div>'
                    html += "</div>"

            elif role == "tool":
                html += '<div class="message-block tool-response">'
                html += (
                    f'<div class="message-header">🔧 Tool Response: {msg["name"]}</div>'
                )
                html += '<div class="message-content">'

                content = msg["content"]
                # Extract observation preview
                lines = content.split("\n")
                preview_lines = []
                for line in lines[:5]:  # First 5 lines as preview
                    if line.strip():
                        preview_lines.append(line)

                preview = "\n".join(preview_lines[:3])
                if preview:
                    html += f'<div class="observation-preview">{self._escape_html(preview)}...</div>'

                html += (
                    f'<div class="collapsible" onclick="toggleContent(\'tool-{i}\')">'
                )
                html += f"<span>View full output ({len(content)} chars)</span>"
                html += (
                    f'<button id="btn-tool-{i}" class="toggle-btn">Show</button></div>'
                )
                html += f'<div id="tool-{i}" class="collapsed-content"><pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px; margin: 0;">{self._escape_html(content)}</pre></div>'

                html += "</div></div>"

        html += "</div>"

        # Add raw output section
        if hasattr(self, "current_raw_output") and self.current_raw_output:
            html += f"""
            <div class="raw-output-section">
                <h2>Raw LLM Output</h2>
                <div class="collapsible" onclick="toggleContent('raw-output')">
                    <span>Show raw output ({len(self.current_raw_output)} chars)</span>
                    <button id="btn-raw-output" class="toggle-btn">Show</button>
                </div>
                <div id="raw-output" class="collapsed-content">
                    <pre style="white-space: pre-wrap; font-family: monospace; font-size: 13px;">{self._escape_html(self.current_raw_output)}</pre>
                </div>
            </div>
            """

        # Add tools section
        if "tools" in self.current_trajectory:
            tools = self.current_trajectory["tools"]
            html += f"""
            <div class="tools-section">
                <h2>Available Tools ({len(tools)} tools)</h2>
                <div class="collapsible" onclick="toggleContent('tools-list')">
                    <span>Click to view available tools</span>
                    <button id="btn-tools-list" class="toggle-btn">Show</button>
                </div>
                <div id="tools-list" class="collapsed-content">
            """

            for i, tool in enumerate(tools):
                if isinstance(tool, dict) and "function" in tool:
                    func_info = tool["function"]
                    tool_name = func_info.get("name", "Unknown")
                    tool_desc = func_info.get("description", "No description")
                    tool_params = func_info.get("parameters", {})

                    # Clean up description - remove excessive newlines but keep structure
                    tool_desc = tool_desc.strip()

                    html += f"""
                    <div class="tool-definition">
                        <h4>🔧 {self._escape_html(tool_name)}</h4>
                        <div style="margin-left: 20px;">
                            <p><strong>Description:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: inherit; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 13px;">{self._escape_html(tool_desc)}</pre>
                            
                            <p><strong>Parameters:</strong></p>
                            <pre style="white-space: pre-wrap; font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">{self._escape_html(self._format_tool_parameters(tool_params))}</pre>
                        </div>
                    </div>
                    """

                    # Add separator between tools except for the last one
                    if i < len(tools) - 1:
                        html += '<hr style="margin: 20px 0; border: none; border-top: 1px solid #e0e0e0;">'

            html += """
                </div>
            </div>
            """

        html += "</div>"  # Close traj-container

        # Save to file if requested
        if save_to_file:
            # Convert to Path object
            from pathlib import Path

            save_path = Path(save_to_file)

            # If no directory is specified, use DEBUG_DIR
            if not save_path.is_absolute() and save_path.parent == Path("."):
                save_path = DEBUG_DIR / save_path

            # Ensure the parent directory exists
            save_path.parent.mkdir(parents=True, exist_ok=True)

            full_html = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Trajectory Visualization - {self.current_trajectory.get("instance_id", "Unknown")}</title>
            </head>
            <body>
                {html}
            </body>
            </html>
            """
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(full_html)
            print(f"✅ Saved visualization to: {save_path}")

            # Optionally open in browser automatically
            import webbrowser

            webbrowser.open(f"file://{save_path.resolve()}")

    def _escape_html(self, text):
        """Escape HTML special characters."""
        if text is None:
            return ""
        return (
            text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&#39;")
        )

    def _format_json(self, json_str):
        """Format JSON string with proper indentation."""
        try:
            obj = json.loads(json_str)
            return json.dumps(obj, indent=2)
        except:
            return json_str

    def _format_tool_parameters(self, params):
        """Format tool parameters for better readability."""
        if not params:
            return "No parameters"

        try:
            # If it's already a dict, format it directly
            if isinstance(params, dict):
                formatted = json.dumps(params, indent=2)
            else:
                # If it's a string, parse and format
                formatted = self._format_json(params)

            # Add additional line breaks for better readability
            formatted = formatted.replace('",', '",\n')
            return formatted
        except:
            return str(params)

    def get_summary(self):
        """Get a summary of the trajectory."""
        if not self.current_trajectory:
            raise ValueError("No trajectory loaded.")

        messages = self.current_trajectory["messages"]

        # Count different types of interactions
        user_msgs = sum(1 for m in messages if m["role"] == "user")
        tool_calls = sum(
            len(m.get("tool_calls", [])) for m in messages if m["role"] == "assistant"
        )
        tool_responses = sum(1 for m in messages if m["role"] == "tool")

        # Get unique tools used
        tools_used = set()
        for m in messages:
            if m["role"] == "assistant" and m.get("tool_calls"):
                for call in m["tool_calls"]:
                    tools_used.add(call["function"]["name"])

        summary = {
            "total_messages": len(messages),
            "user_messages": user_msgs,
            "tool_calls": tool_calls,
            "tool_responses": tool_responses,
            "unique_tools": list(tools_used),
            "total_tokens": self.current_trajectory["usage"]["prompt_tokens"]
            + self.current_trajectory["usage"]["completion_tokens"],
            "execution_time": self.current_trajectory.get("time", 0),
        }

        return summary


def visualize_trajectory_from_azure(
    instance_id: str,
    trajectory_path: str,
    subscription_id: str,
    resource_group: str,
    workspace: str,
    save_to_file: Optional[str] = None,
    show_full_content: bool = False,
    debug_dataset_path: Optional[str] = None,  # Keep this parameter for local file
    use_cache: bool = True,
):
    """
    Convenience function to visualize a trajectory from Azure ML data asset.

    Args:
        instance_id: ID of the instance to visualize
        trajectory_path: Azure ML data asset reference (azureml:name:version) or URI
        subscription_id: Azure subscription ID
        resource_group: Resource group name
        workspace: Workspace name
        save_to_file: Optional filename to save HTML (will be saved to debug/)
        show_full_content: Whether to show full content or truncate
        debug_dataset_path: Optional path to debug dataset JSON file (local file in debug/)

    Example:
        # Using data asset reference with debug dataset
        visualize_trajectory_from_azure(
            instance_id="django-123",
            trajectory_path="azureml:trajectories:1",
            subscription_id="your-sub-id",
            resource_group="your-rg",
            workspace="your-ws",
            save_to_file="django-123.html",
            debug_dataset_path="debug/swebenchlite_gpt41mini_debug_dataset.json"
        )
    """
    viz = TrajectoryVisualizer.from_azure_ml(
        trajectory_path=trajectory_path,
        subscription_id=subscription_id,
        resource_group=resource_group,
        workspace=workspace,
        use_cache=use_cache,
    )

    # Set debug dataset path after creating the visualizer
    viz.debug_dataset_path = debug_dataset_path

    viz.load_trajectory(instance_id=instance_id)
    viz.visualize(show_full_content=show_full_content, save_to_file=save_to_file)

    return viz
