from typing import List, Optional, Union

from llamafactory.api.protocol import ChatCompletionRequest, ChatMessage, FunctionAvailable
from pydantic import BaseModel
from inference import create_response


class ChatCompletionRequestWithSpeculation(BaseModel):
    model: str
    messages: List[ChatMessage]
    tools: Optional[List[FunctionAvailable]] = None
    do_sample: Optional[bool] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    n: int = 1
    max_tokens: Optional[int] = None
    stop: Optional[Union[str, List[str]]] = None
    stream: bool = False
    speculation: str = None


def run_server():
    from fastapi import FastAPI
    import uvicorn

    app = FastAPI()

    @app.post("/score")
    async def score(request: ChatCompletionRequestWithSpeculation):
        speculation = request.speculation
        request: ChatCompletionRequest = ChatCompletionRequest.model_validate(
            request.model_dump())
        return await create_response(request, speculation)

    print("Starting server...")
    uvicorn.run(app, host="127.0.0.1", port=8000)
