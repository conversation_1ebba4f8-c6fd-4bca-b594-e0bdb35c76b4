"""Direct prediction method using LLMs without codebase analysis."""

import re
import time
from typing import Dict, Any, Optional, List
from ...utils.llm_clients import LLMClientFactory
from .parsing_output import parse_location_prediction
from .prompts import PROMPTS
from ...utils.cost_analysis import num_tokens_from_messages, calc_cost


class DirectPredictionMethod:
    """Direct prediction method that uses LLMs to predict locations from issue descriptions."""

    def __init__(
        self, model_name: str = "gpt-4", config: Optional[Dict[str, Any]] = None
    ):
        """Initialize direct prediction method.

        Args:
            model_name: Name of the LLM model to use
            config: Optional configuration dictionary
        """
        self.model_name = model_name
        self.config = config or {}
        self.method_name = "direct_prediction"

        # Initialize LLM client
        self.llm_client = LLMClientFactory.create(model_name, config)

        # Extract config values
        self.temperature = self.config.get("temperature", 0.1)
        self.max_tokens = self.config.get("max_tokens", 6000)
        self.rate_limit_delay = self.config.get("rate_limit_delay", 0.5)

        # prompt templates
        self.prompt_template = self.config.get("prompt_template", "default")
        if self.prompt_template not in PROMPTS:
            available_templates = ", ".join(PROMPTS.keys())
            raise ValueError(
                f"Invalid prompt template '{self.prompt_template}'. "
                f"Available templates: {available_templates}"
            )

    def predict(self, instance: Dict[str, Any]) -> Dict[str, Any]:
        """Predict locations for a given issue.

        Args:
            instance: Dataset instance containing issue information

        Returns:
            Dictionary with predictions
        """
        # Create prompt from instance
        prompt = self._create_prompt(instance)
        # Track API call time
        start_time = time.time()
        # Get prediction from LLM using the generate method
        response = self.llm_client.generate(
            prompt=prompt, temperature=self.temperature, max_tokens=self.max_tokens
        )

        elapsed_time = time.time() - start_time
        # Add rate limiting to avoid hitting API limits
        time.sleep(self.rate_limit_delay)

        # Calculate token usage and cost
        prompt_tokens = num_tokens_from_messages(prompt, model=self.model_name)
        response_tokens = num_tokens_from_messages(response, model=self.model_name)
        cost = calc_cost(self.model_name, prompt_tokens, response_tokens)

        # Parse the response
        parsed_result = parse_location_prediction(
            response, instance.get("instance_id", "unknown")
        )

        # Add trajectory metadata
        trajectory = {
            "instance_id": instance.get("instance_id", "unknown"),
            "messages": [
                {"role": "user", "content": prompt},
                {"role": "assistant", "content": response},
            ],
            "tooks": [],
            "usage": {
                "prompt_tokens": prompt_tokens,
                "completion_tokens": response_tokens,
                "cost": cost,
            },
            "time": elapsed_time,
        }

        return parsed_result, trajectory

    def _create_prompt(self, instance: Dict[str, Any]) -> str:
        """Create a prompt for location prediction.

        Args:
            instance: Dataset instance containing issue information

        Returns:
            Formatted prompt string
        """
        # Extract relevant information
        repo = instance.get("repo", "repository")
        problem = instance.get("problem_statement", "")

        template = PROMPTS[self.prompt_template]

        return template.format(repo=repo, problem=problem)
