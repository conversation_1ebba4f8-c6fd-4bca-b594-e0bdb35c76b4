import asyncio
import os
import json
import traceback
import pandas as pd
from inference import create_response, init_model, run_example
from aml_score_batch import run
import sys
import tqdm

from llamafactory.api.protocol import ChatCompletionRequest

async def run_all(data_path, extract_system):
    init_model(os.environ)
    files = []
    for fpath in os.listdir(data_path):
        files.append(os.path.join(data_path, fpath))
    inputs = []
    outputs = []
    for raw_data_file in tqdm.tqdm(files, desc="Batch inference"):
        with open(raw_data_file) as f:
            raw_data = f.read()
        inputs.append(raw_data)
        
        # Model is required, even if empty. If it's not provided, use a placeholder.
        data = json.loads(raw_data)
        static_speculation = data.get("speculation", None)
        if "model" not in data:
            data["model"] = ""
        messages = data["messages"]
        if extract_system:
            if messages[0]["role"] == "user":
                content = messages[0]["content"]
                if content.lstrip().startswith("<SYSTEM>"):
                    assert "</SYSTEM>" in content, "No system message ending tag in content: " + content
                    begin_idx = content.index("<SYSTEM>")
                    end_idx = content.index("</SYSTEM>")
                    assert begin_idx != -1, "Could not find system message beginning tag in content: " + content
                    assert end_idx != -1, "Could not find system message ending tag in content: " + content
                    system_content = content[begin_idx + len("<SYSTEM>"):end_idx].strip()
                    user_content = content[end_idx + len("</SYSTEM>"):].strip()
                    messages[0]["content"] = user_content
                    messages.insert(0, {"role": "system", "content": system_content})
        try:
            request = ChatCompletionRequest.model_validate(data)
            response = await create_response(request, static_speculation)
        except Exception as e:
            print("Inference error:", e)
            print(traceback.format_exc())
            response = {"error": str(e)}
        response_json = json.dumps(response)

        outputs.append(response_json)
    return inputs, outputs

if __name__ == "__main__":
    model_path, data_path, output_path, speculative, quantized, template, parallel, static_overlap_tokens, num_speculative_tokens, extract_system = sys.argv[1:]
    os.environ["AZUREML_infer_backend"] = "vllm"
    os.environ["AZUREML_use_v2_block_manager"] = "True"
    if speculative == "True":
        os.environ["AZUREML_speculative_model"] = "[static]"
        os.environ["AZUREML_static_overlap_tokens"] = static_overlap_tokens
        os.environ["AZUREML_num_speculative_tokens"] = num_speculative_tokens
    if quantized == "bitsandbytes":
        os.environ["AZUREML_quantization"] = "bitsandbytes"
        os.environ["AZUREML_load_format"] = "bitsandbytes"
        os.environ["AZUREML_infer_dtype"] = "bfloat16"
    elif quantized != "none":
        os.environ["AZUREML_quantization"] = quantized
        os.environ["AZUREML_infer_dtype"] = "float16"
    if int(parallel) > 0:
        os.environ["AZUREML_tensor_parallel_size"] = str(parallel)
    os.environ["AZUREML_template"] = template
    os.environ["AZUREML_model_name_or_path"] = model_path
    os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2,3,4,5,6,7"
    
    extract_system = extract_system == "True"
    inputs, outputs = asyncio.run(run_all(data_path, extract_system))
    results = pd.DataFrame({"input": inputs, "output": outputs})
    results.to_csv(os.path.join(output_path, "predictions.csv"), sep=" ")
