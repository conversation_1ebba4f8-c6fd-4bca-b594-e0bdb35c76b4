from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import command
from azure.ai.ml import Input, Output
from azure.ai.ml.entities import ResourceConfiguration, Environment, BuildContext
from azure.ai.ml import M<PERSON><PERSON>
from datetime import datetime
from argparse import ArgumentParser
from azure.ai.ml.entities import JupyterLabJobService, VsCodeJobService, TensorBoardJobService, SshJobService
import os
import webbrowser

if __name__=='__main__':
 
    parser = ArgumentParser()
    parser.add_argument('--cluster', type=str, default="A100")
    parser.add_argument('--resource_group', type=str, default="SingularityH100")
    parser.add_argument('--workspace', type=str, default="H100CentralUS")
 
    args = parser.parse_args()
 
    resource_group = args.resource_group
    workspace = args.workspace
 
    try:
        credential = DefaultAzureCredential()
        # Check if given credential can get token successfully.
        credential.get_token("https://management.azure.com/.default")
    except Exception as ex:
        print('Failed to get token with DefaultAzureCredential, fall back to InteractiveBrowserCredential', ex)
        # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work
        credential = InteractiveBrowserCredential()
 
    # get a handle to the workspace
    ml_client = MLClient(
        subscription_id="d0c05057-7972-46ff-9bcf-3c932250155e",
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential,
    )
    env_docker_context = Environment(
    build=BuildContext(
        path="./",
        dockerfile_path="locagent_Dockerfile"  # Add this line
    ),
    name="LlamaFactoryLocAgent",  # Changed name to reflect new purpose
    description="Environment created for LlamaFactory with LocAgent capabilities."
    )
    ml_client.environments.create_or_update(env_docker_context)