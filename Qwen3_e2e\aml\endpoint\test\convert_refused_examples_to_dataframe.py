import json
import pandas as pd
import os

os.makedirs("refused_examples", exist_ok=True)

datas = []
with open("refused_examples.jsonl") as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        data = json.loads(line)
        prompt = data['prompt']
        speculation = data['speculation']
        data = {
            "model": "",
            "messages": [{"role": "user", "content": prompt}],
            "speculation": speculation,
            "temperature": 0.0,
            "top_p": 1.0,
            "max_tokens": 10000,
        }
        with open(f"relected_data/example_{str(i).rjust(len(str(len(lines))), '0')}.json", "w") as of:
            json.dump(data, of)
