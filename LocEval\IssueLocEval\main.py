"""Command-line interface for RepoLocEval."""

import argparse
import json
from pathlib import Path
from typing import Dict, Any, List
from tqdm import tqdm
import pandas as pd

from .datasets.registry import DatasetRegistry
from .methods.registry import MethodRegistry
from .metrics.eval_metrics import LocalizationEvaluator
from .utils.paths import RESULTS_DIR
import pdb


def run_e2e(args):
    """Run evaluation with specified dataset and method."""
    # Load dataset
    print(f"Loading dataset: {args.dataset}")
    dataset = DatasetRegistry.get(args.dataset)
    dataset.load(split=args.split, limit=args.limit)
    data = dataset._data
    if args.filter_out_simple:
        print("Filtering out simple instances...")
        data = dataset.filter_out_simple(data)
    if args.filter_out_old:
        print(f"Filtering out instances created before {args.filter_out_old}...")
        data = dataset.filter_out_old(args.filter_out_old, data)

    # Initialize method
    print(f"Initializing method: {args.method}")
    method_config = {}
    if args.temperature is not None:
        method_config["temperature"] = args.temperature
    if args.max_tokens is not None:
        method_config["max_tokens"] = args.max_tokens
    if args.prompt_template is not None:
        method_config["prompt_template"] = args.prompt_template

    method = MethodRegistry.get(
        args.method,
        model_name=args.model,
        config=method_config
    )
    
    # Run predictions
    results = []
    trajectories = []
    for item in tqdm(data, desc="Processing items"):
        prediction, trajectory = method.predict(item)
        results.append(prediction)
        trajectories.append(trajectory)

    # Save results
    # Create folder name based on experiment parameters
    folder_parts = [
        args.model.replace('/', '-').replace('.', '_'),
        args.dataset.replace('/', '_'),
    ]

    # Add filter indicators
    if args.filter_out_simple:
        folder_parts.append("simple_filtered")
    if args.filter_out_old:
        folder_parts.append(f"after_{args.filter_out_old.replace('-', '')}")

    # Create experiment folder
    experiment_folder = RESULTS_DIR / "experiments" / "_".join(folder_parts)
    experiment_folder.mkdir(parents=True, exist_ok=True)

    # Define file paths
    output_path = experiment_folder / "loc_output.jsonl"
    traj_path = experiment_folder / "loc_trajs.jsonl"

    # Save location predictions
    with open(output_path, 'w', encoding='utf-8') as f:
        for result in results:
            json.dump(result, f, ensure_ascii=False)
            f.write('\n')

    # Save trajectories
    with open(traj_path, 'w', encoding='utf-8') as f:
        for traj in trajectories:
            json.dump(traj, f, ensure_ascii=False)
            f.write('\n')

    print(f"\nExperiment folder: {experiment_folder}")
    print(f"Results saved to: {output_path.name}")
    print(f"Trajectories saved to: {traj_path.name}")
    print(f"Processed {len(results)} items")
    
    # Print summary statistics
    if results:
        avg_files = sum(len(r.get("found_files", [])) for r in results) / len(results)
        avg_entities = sum(len(r.get("found_entities", [])) for r in results) / len(results)
        print(f"Average files found: {avg_files:.2f}")
        print(f"Average entities found: {avg_entities:.2f}")
    
    # Run evaluation if requested
    if args.evaluate:
        print("\nRunning evaluation...")
        evaluator = LocalizationEvaluator()
        # pdb.set_trace()
        # Use preprocessed data for ground truth
        eval_results = evaluator.evaluate(
            prediction_file=str(output_path),
            data=data,
            metrics=args.metrics
        )
        
        print("\nEvaluation Results:")
        print(eval_results)
        
        # Save evaluation results in the experiment folder
        eval_path = experiment_folder / "loc_output.eval.csv"
        eval_results.to_csv(eval_path)
        print(f"\nEvaluation results saved to: {eval_path.name}")
    
    return output_path


def evaluate_predictions(args):
    """Evaluate existing predictions with optional additional filtering."""
    # Determine input path
    pred_path = Path(args.prediction_file)
    
    # Check if it's in an experiment folder
    if pred_path.name == "loc_output.jsonl" and "experiments" in pred_path.parts:
        input_experiment_folder = pred_path.parent
        print(f"Loading predictions from experiment: {input_experiment_folder.name}")
    else:
        input_experiment_folder = None
        print(f"Loading predictions from: {pred_path}")
    
    evaluator = LocalizationEvaluator()
    
    # Determine dataset
    try:
        dataset_name = args.dataset
    except AttributeError:
        print("No dataset specified.")
    
    # Need to load the dataset to get preprocessed data
    dataset = DatasetRegistry.get(dataset_name)
    dataset.load(split=args.split)
    data = dataset._data

    if args.preprocess:
        print("Preprocessing data...")
        data = dataset.preprocess(data)
    # Apply filters based on command line arguments
    if args.filter_out_simple:
        print("Filtering out simple instances...")
        data = dataset.filter_out_simple()
    if args.filter_out_old:
        print(f"Filtering out instances created before {args.filter_out_old}...")
        data = dataset.filter_out_old(args.filter_out_old)

    # Detect filters from input folder name (if from experiment folder)
    input_filters = {
        'simple': False,
        'date': None
    }

    if input_experiment_folder:
        folder_name = input_experiment_folder.name
        if "simple_filtered" in folder_name:
            input_filters['simple'] = True
        
        import re
        date_match = re.search(r"after_(\d{8})", folder_name)
        if date_match:
            date_str = date_match.group(1)
            input_filters['date'] = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

    # Check if evaluation requires more data than what was used for predictions
    if input_filters['simple'] and not args.filter_out_simple:
        raise ValueError("Error: Input predictions were filtered for simple instances, but evaluation is not. "
                        "Cannot evaluate on more data than predictions were made for.")

    if input_filters['date'] and (not args.filter_out_old or args.filter_out_old < input_filters['date']):
        raise ValueError(f"Error: Input predictions were filtered after {input_filters['date']}, "
                        f"but evaluation filter is {'not set' if not args.filter_out_old else f'before that ({args.filter_out_old})'}. "
                        "Cannot evaluate on more data than predictions were made for.")

    # Determine if we need a new output folder
    need_new_folder = False
    if args.filter_out_simple and not input_filters['simple']:
        need_new_folder = True
    if args.filter_out_old and (not input_filters['date'] or args.filter_out_old > input_filters['date']):
        need_new_folder = True

    # Determine output location
    if need_new_folder and input_experiment_folder:
        # Extract base name without filters
        base_name = input_experiment_folder.name
        base_name = re.sub(r"_simple_filtered", "", base_name)
        base_name = re.sub(r"_after_\d{8}", "", base_name).rstrip("_")
        
        # Build new folder name with current filters
        folder_parts = [base_name]
        if args.filter_out_simple:
            folder_parts.append("simple_filtered")
        if args.filter_out_old:
            folder_parts.append(f"after_{args.filter_out_old.replace('-', '')}")
        
        output_experiment_folder = input_experiment_folder.parent / "_".join(folder_parts)
        output_experiment_folder.mkdir(parents=True, exist_ok=True)
        eval_output_path = output_experiment_folder / "loc_output.eval.csv"
        
        print(f"\nCreating new experiment folder due to additional filters: {output_experiment_folder.name}")
    else:
        # Use original location
        if input_experiment_folder:
            eval_output_path = input_experiment_folder / "loc_output.eval.csv"
        else:
            eval_output_path = Path(args.output) if args.output else pred_path.with_suffix('.eval.csv')
    
    # Run evaluation
    print("\nRunning evaluation...")
    eval_results = evaluator.evaluate(
        prediction_file=str(pred_path),
        data=data,
        metrics=args.metrics
    )
    
    print("\nEvaluation Results:")
    print(eval_results)
    
    # Save evaluation results
    eval_results.to_csv(eval_output_path)
    print(f"\nEvaluation results saved to: {eval_output_path}")
    
    # Print filter summary
    print("\nFilter Summary:")
    print(f"  Original filters: simple={input_filters['simple']}, date={input_filters['date']}")
    if need_new_folder:
        new_filter_parts = []
        if args.filter_out_simple:
            new_filter_parts.append("simple_filtered")
        if args.filter_out_old:
            new_filter_parts.append(f"after_{args.filter_out_old.replace('-', '')}")
        print(f"  Additional filters applied: {', '.join(new_filter_parts)}")


def list_datasets(args):
    """List available datasets with their statistics in a table format."""
    datasets = DatasetRegistry.list_available()
    print("\nLoading dataset statistics...")
    
    # Collect stats for all datasets
    all_stats = []
    stat_keys = set()
    
    for hf_path in datasets:
        print(f"  Loading {hf_path}...", end='', flush=True)
        try:
            # Get dataset instance
            dataset = DatasetRegistry.get(hf_path)
            
            # Load a small sample to get stats
            dataset.load(split='test')
            
            # Get stats
            stats = dataset._stats
            stats['dataset'] = hf_path  # Add dataset name as first column
            all_stats.append(stats)
            stat_keys.update(stats.keys())
            print(" ✓")
        except Exception as e:
            print(f" ✗ (Error: {str(e)})")
            # Add entry with just the dataset name and NaN for other fields
            all_stats.append({'dataset': hf_path})
    
    # Remove some keys that might be too verbose for the table
    verbose_keys = {'repo_distribution', 'gt_language_distribution'}
    stat_keys = stat_keys - verbose_keys
    
    # Define the order of metrics (rows) for better readability
    ordered_metrics = [
        'total_instances',
        'unique_repos',
        'avg_instances_per_repo',
        'min_instances_per_repo',
        '25th_percentile_instances_per_repo',
        'median_instances_per_repo',
        '75th_percentile_instances_per_repo',
        'max_instances_per_repo',
        'avg_problem_length_tokens',
        'avg_patch_length_tokens',
        'avg_gt_files_per_instance',
        'avg_gt_modules_per_instance', 
        'avg_gt_functions_per_instance',
        'unique_gt_languages',
        'top_languages',
        'most_recent_instance'
    ]
    
    # Add any remaining keys not in ordered list
    remaining_keys = sorted(stat_keys - set(ordered_metrics))
    final_metrics = [metric for metric in ordered_metrics if metric in stat_keys] + remaining_keys
    
    # Create pivoted DataFrame - datasets as columns, metrics as rows
    pivot_data = {}
    
    # Initialize with metric names as index
    for stats in all_stats:
        dataset_name = stats.get('dataset', 'Unknown')
        # Shorten dataset names for display
        short_name = dataset_name.split('/')[-1] if '/' in dataset_name else dataset_name
        
        pivot_data[short_name] = {}
        for metric in final_metrics:
            if metric in stats:
                value = stats[metric]
                # Format numbers for better display
                if isinstance(value, float):
                    pivot_data[short_name][metric] = f"{value:.2f}"
                else:
                    pivot_data[short_name][metric] = str(value)
            else:
                pivot_data[short_name][metric] = "NaN"
    
    # Create DataFrame with metrics as index and datasets as columns
    df_pivot = pd.DataFrame(pivot_data, index=final_metrics)
    
    # Create display names for metrics (rows)
    metric_display_names = {
        'total_instances': 'Total Instances',
        'unique_repos': 'Unique Repos',
        'avg_instances_per_repo': 'Avg Instances/Repo',
        'avg_problem_length_tokens': 'Avg Problem Length (tokens)',
        'avg_patch_length_tokens': 'Avg Patch Length (tokens)',
        'avg_gt_files_per_instance': 'Avg GT Files/Instance',
        'avg_gt_modules_per_instance': 'Avg GT Modules/Instance',
        'avg_gt_functions_per_instance': 'Avg GT Functions/Instance',
        'unique_gt_languages': 'Unique Languages',
        'max_instances_per_repo': 'Max Instances/Repo',
        'min_instances_per_repo': 'Min Instances/Repo',
        '25th_percentile_instances_per_repo': '25th Percentile/Repo',
        'median_instances_per_repo': 'Median/Repo',
        '75th_percentile_instances_per_repo': '75th Percentile/Repo',
        'top_languages': 'Top Languages',
        'most_recent_instance': 'Most Recent Instance',
    }
    
    # Rename index for display
    df_pivot.index = [metric_display_names.get(metric, metric) for metric in df_pivot.index]
    
    print("\n" + "="*120)
    print("Dataset Statistics (Pivoted: Metrics in Rows, Datasets in Columns)")
    print("="*120)
    
    # Display the table
    if df_pivot.empty:
        print("No datasets found.")
    else:
        # Use pandas string representation with some formatting
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 30)
        pd.set_option('display.max_rows', None)
        print(df_pivot.to_string())
    
    # Reset pandas options
    pd.reset_option('display.max_columns')
    pd.reset_option('display.width') 
    pd.reset_option('display.max_colwidth')
    pd.reset_option('display.max_rows')
    
    # Optionally save to CSV
    if hasattr(args, 'output') and args.output:
        output_path = RESULTS_DIR / args.output
        df_pivot.to_csv(output_path)
        print(f"\nDataset statistics saved to: {output_path}")


def list_methods(args):
    """List available methods."""
    methods = MethodRegistry.list_available()
    print("\nAvailable methods:")
    for name, info in methods.items():
        print(f"  - {name}: {info['description']}")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="RepoLocEval - Repository Localization Evaluation Framework"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Run evaluation command: predict and evaluate
    run_parser = subparsers.add_parser('run', help='Run evaluation')
    run_parser.add_argument('--dataset', type=str, required=True,
                           help='HuggingFace dataset path (e.g., princeton-nlp/SWE-bench_Lite)')
    run_parser.add_argument('--method', type=str, required=True,
                           help='Method name (e.g., direct_prediction)')
    run_parser.add_argument('--model', type=str, default='gpt-4',
                           help='Model name for LLM-based methods')
    run_parser.add_argument('--split', type=str, default='test',
                           help='Dataset split to use')
    run_parser.add_argument('--limit', type=int, default=None,
                           help='Limit number of instances to process')
    run_parser.add_argument('--temperature', type=float, default=None,
                           help='Temperature for LLM generation')
    run_parser.add_argument('--max-tokens', type=int, default=None,
                           help='Max tokens for LLM generation')
    run_parser.add_argument('--preprocess', action='store_true',
                           help='Preprocess dataset before evaluation')
    run_parser.add_argument('--evaluate', action='store_true',
                           help='Run evaluation after predictions')
    run_parser.add_argument('--metrics', nargs='+', default=['acc', 'recall', 'precision'],
                       choices=['acc', 'ndcg', 'precision', 'recall', 'map'],
                       help='Metrics to calculate')
    run_parser.add_argument('--prompt_template', type=str, default='default',
                        choices=['default', 'concise', 'cot'],  # Add your template names here
                       help='Prompt template to use for direct prediction')
    run_parser.add_argument('--filter-out-simple', action='store_true',
                           help='Filter out simple instances during evaluation')
    run_parser.add_argument('--filter-out-old', type=str, default=None,
                       help='Filter out instances created before this date (e.g., 2023-01-01)')
    run_parser.set_defaults(func=run_e2e)
    
    # Evaluate command: evaluate existing predictions
    eval_parser = subparsers.add_parser('evaluate', help='Evaluate predictions')
    eval_parser.add_argument('--prediction_file', type=str,
                           help='Path to prediction JSONL file')
    eval_parser.add_argument('--dataset', type=str, default=None,
                           help='HuggingFace dataset path (e.g.: princeton-nlp/SWE-bench_Lite_bm25_13K)')
    eval_parser.add_argument('--split', type=str, default='test',
                           help='Dataset split')
    eval_parser.add_argument('--metrics', nargs='+', default=['acc', 'recall', 'precision'],
                        choices=['acc', 'ndcg', 'precision', 'recall', 'map'],
                        help='Metrics to calculate')
    eval_parser.add_argument('--output', type=str, default=None,
                           help='Output file for evaluation results')
    eval_parser.add_argument('--preprocess', type=bool, default=False,
                           help='Preprocess dataset before evaluation')
    eval_parser.add_argument('--filter-out-simple', action='store_true',
                           help='Filter out simple instances during evaluation')
    eval_parser.add_argument('--filter-out-old', type=str, default=None,
                        help='Filter out instances created before this date (e.g., 2023-01-01)')
    eval_parser.set_defaults(func=evaluate_predictions)
    
    # List datasets command
    list_ds_parser = subparsers.add_parser('list-datasets', 
                                          help='List available datasets with statistics')
    list_ds_parser.add_argument('--output', type=str, default=str('registered_datasets_stats.csv'),
                               help='Output CSV file for dataset statistics')
    list_ds_parser.set_defaults(func=list_datasets)
    
    # List methods command
    list_methods_parser = subparsers.add_parser('list-methods',
                                               help='List available methods')
    list_methods_parser.set_defaults(func=list_methods)
    
    args = parser.parse_args()
    
    if hasattr(args, 'func'):
        args.func(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()