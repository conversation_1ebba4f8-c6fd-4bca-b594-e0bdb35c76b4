$schema: https://azuremlschemas.azureedge.net/latest/commandComponent.schema.json
type: command

name: fastapply_inference
display_name: "FastApply Inference"

inputs:
  model:
    type: uri_folder
  data:
    type: uri_folder
  static_speculation:
    type: boolean
    default: false
  quantization:
    type: string
    default: 'none'
    enum: ['none', 'bitsandbytes', 'autoawq', "awq", "awq_marlin", "gptq", "qqq"]
  template:
    type: string
    default: "llama3"
  parallel:
    type: integer
  static_overlap_tokens:
    type: integer
  num_speculative_tokens:
    type: integer
  extract_system_content:
    type: boolean
    default: false

outputs:
  output:
    type: uri_folder

code: ./src

environment: azureml:LlamaFactoryOptimized:8

command: >-
  python aml_component.py ${{inputs.model}} ${{inputs.data}} ${{outputs.output}} ${{inputs.static_speculation}} ${{inputs.quantization}} ${{inputs.template}} ${{inputs.parallel}} ${{inputs.static_overlap_tokens}} ${{inputs.num_speculative_tokens}} ${{inputs.extract_system_content}}
