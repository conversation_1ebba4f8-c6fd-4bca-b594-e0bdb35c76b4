$schema: https://azuremlschemas.azureedge.net/latest/commandComponent.schema.json
type: command

name: fastapply_quantize
display_name: "FastApply Quantization"

inputs:
  model:
    type: uri_folder

outputs:
  quantized_model:
    type: uri_folder

code: ./src

environment: azureml:LlamaFactoryOptimized:8

command: >-
  pip install autoawq && python quantize.py ${{inputs.model}} ${{outputs.quantized_model}}
