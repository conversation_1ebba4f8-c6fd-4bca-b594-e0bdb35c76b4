# Azure ML LlamaFactory FineTuning Script Modifications
On file submit_singularity.py, I made several modifications to the original script to ensure compatibility with the Qwen3 model and to address environment setup issues. Below are the key changes and their purposes.

## 1. Environment Setup Workaround

### Challenge
The base environment contains outdated PEFT and Transformers packages. The Transformers version doesn't recognize Qwen3 models, and standard requirements file installation doesn't overwrite existing packages.

### Solution
Force install newer versions explicitly in the command string.

#### Implementation
Insert the following lines within `command_str = """ """` (near line 54):

```python
export DISABLE_VERSION_CHECK=1
pip install --upgrade -r requirements.txt
pip install --upgrade peft==0.15.1

echo "Current transformers version:"
python -c "import transformers; print(transformers.__version__)"
# Force reinstall transformers to ensure we get 4.51.0
pip install --force-reinstall transformers==4.51.0
echo "After update:"
python -c "import transformers; print(transformers.__version__)"
python -c "from transformers import AutoConfig; print('Qwen3 test:', 'qwen3' in AutoConfig)"
echo "Rank $RANK: Starting training..."
```
With `command_str`, you might need to add an argument flag after src/train.py:
```--trust_remote_code```

## 2. Training Job Configuration
Within ```training_job =  command()```
- Update ```code``` path and outputs ```path```
- ```environment_variables```: you might want to use your own HuggingFace API token
- ```my_ssh```: you might want to update to your own. I think it's supposed to allow you to debug interactively with your own private ssh key. But I haven't tried.
- ```nodes='all'```: Comment out all the ```nodes='all'``` lines. (This makes interactive services only feasible on node 0. Not commenting them out throws an error for me.)
```python
services={
        "My_jupyterlab": JupyterLabJobService(
            # nodes="all" # For distributed jobs, use the `nodes` property to pick which node you want to enable interactive services on. If `nodes` are not selected, by default, interactive applications are only enabled on the head node. Values are "all", or compute node index (for ex. "0", "1" etc.)
        ),
        "My_vscode": VsCodeJobService(
            # nodes="all"
        ),
        "My_tensorboard": TensorBoardJobService(
            # nodes="all",
            log_dir="output/tblogs"  # relative path of Tensorboard logs (same as in your training script)         
        ),
        "My_ssh": SshJobService(
            ssh_public_keys="xxxxxx",
            # nodes="all"  
        ),
}
```

## 3. Supported Datasets
It seems that the script only supports certain types of data (check dataset_info.json and README).
- Types that worked for me
  - OPENAI format; e.g.: upgrade_sft
  - Alpaca SFT
- Types that failed for me
  - Alpaca format preference set; e.g. dpo_en_demo


# Azure ML LlamaFactory Inference Script Modifications
On file submit_infer.py, I made similar changes to ensure compatibility with the Qwen3 model and to address environment setup issues. Below are the key changes.

## 1. Environment Setup Workaround
Under the `command_str` variable, I added the same environment setup commands as in the fine-tuning script to ensure the correct versions of PEFT and Transformers are installed.
```python
command_str = f"""
export DISABLE_VERSION_CHECK=1
pip install --upgrade -r requirements.txt
pip install --upgrade peft==0.15.1
pip install --force-reinstall transformers==4.51.0
pip install --upgrade "vllm>=0.8.4"
python3 scripts/vllm_infer.py \
--model_name_or_path ${{inputs.datastore_dir}} \
--dataset test_apply_2step \
--template qwen \
--save_name ${{outputs.datastore_dir}}/generated_predictions.jsonl
"""
```

## 2. Inference Job Configuration
Within `training_job = command()`, I made the following changes:
- Comment out the `nodes='all'` lines to ensure interactive services are only enabled on the head node.
- Comment out the `distribution={}` line to avoid issues with distributed inference.