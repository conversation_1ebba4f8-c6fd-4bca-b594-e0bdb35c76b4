import asyncio
import time

import json
import traceback
from llamafactory.api.chat import create_chat_completion_response
from llamafactory.api.protocol import ChatCompletionRequest, ChatCompletionResponse
from llamafactory.chat import ChatModel
from vllm.engine.async_llm_engine import AsyncEngineDeadError
from config import set_configuration_if_present, str2bool


chat_model = None
require_speculation = False


def init_model(config):
    global chat_model
    if chat_model is None:
        args = {}
        args["template"] = config.get("AZUREML_template", None)
        set_configuration_if_present(config, args, "model_name_or_path")
        set_configuration_if_present(config, args, "adapter_name_or_path")
        set_configuration_if_present(config, args, "infer_backend")
        set_configuration_if_present(config, args, "vllm_maxlen", int, 13552)
        set_configuration_if_present(config, args, "infer_dtype")
        args["vllm_config"] = vllm_config = {}
        set_configuration_if_present(config, vllm_config, "speculative_model")
        set_configuration_if_present(config, vllm_config, "static_overlap_tokens", int)
        set_configuration_if_present(config, vllm_config, "num_speculative_tokens", int)
        set_configuration_if_present(config, vllm_config, "use_v2_block_manager", str2bool)
        set_configuration_if_present(config, vllm_config, "ngram_prompt_lookup_max", int)
        set_configuration_if_present(config, vllm_config, "use_async_output_proc", str2bool)
        set_configuration_if_present(config, vllm_config, "quantization")
        set_configuration_if_present(config, vllm_config, "load_format")
        set_configuration_if_present(config, vllm_config, "tensor_parallel_size", int)
        set_configuration_if_present(config, vllm_config, "distributed_executor_backend")
        print("Config:", args)
        chat_model = ChatModel(args)
        global require_speculation
        if vllm_config.get("speculative_model", None) == "[static]":
            require_speculation = True
    else:
        print("Model already initialized.")


async def create_response(request: ChatCompletionRequest, static_speculation=None):
    global chat_model
    global require_speculation
    total_begin = time.time()
    print("Request:", request, "static_speculation:", static_speculation)
    if static_speculation is not None:
        system = None
        for msg in request.messages:
            if msg.role == "system":
                system = msg.content
                break
        messages = [dict(msg) for msg in request.messages if msg.role != "system"] + [{"role": "assistant", "content": static_speculation}]
        _, tokenized_static_speculation = chat_model.engine.template.encode_oneturn(chat_model.engine.tokenizer, messages, system=system)
        print("Tokenizing static speculation:",
              static_speculation, tokenized_static_speculation)
    elif require_speculation:
        raise ValueError("Static speculation is required for this model.")
    begin = time.time()
    if static_speculation is not None:
        response: ChatCompletionResponse = await create_chat_completion_response(request, chat_model, static_speculation=tokenized_static_speculation)
    else:
        response: ChatCompletionResponse = await create_chat_completion_response(request, chat_model)
    end = time.time()
    elapsed = end - begin
    print("Response:", response, "time:", elapsed)
    response = response.model_dump()
    total_end = time.time()
    total_elapsed = total_end - total_begin
    response["elapsed"] = elapsed
    response["total_elapsed"] = total_elapsed
    return response


def run_example(raw_data, retry_fn=None) -> str:
    # Model is required, even if empty. If it's not provided, use a placeholder.
    data = json.loads(raw_data)
    static_speculation = data.get("speculation", None)
    if "model" not in data:
        data["model"] = ""
    try:
        try:
            request = ChatCompletionRequest.model_validate(data)
            response = asyncio.run(create_response(request, static_speculation))
        except AsyncEngineDeadError as e:
            if retry_fn is None:
                raise
            print("Reinitializing engine and retrying request due to error:", e)
            print(traceback.format_exc())
            retry_fn(raw_data)
    except Exception as e:
        print("Inference error:", e)
        print(traceback.format_exc())
        response = {"error": str(e)}
    response_json = json.dumps(response)
    return response_json
