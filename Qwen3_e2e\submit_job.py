from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import command
from azure.ai.ml import Input, Output
from azure.ai.ml.entities import ResourceConfiguration
from azure.ai.ml import MLClient
from datetime import datetime
from argparse import ArgumentParser
from azure.ai.ml.entities import JupyterLabJobService, VsCodeJobService, TensorBoardJobService, SshJobService
import os
import webbrowser

if __name__=='__main__':
 
    parser = ArgumentParser()
    parser.add_argument('--workspace', type=str, default="AML-NorwayEast-2")
 
    args = parser.parse_args()
    workspace = args.workspace
    if workspace == "Instant_apply_training_new":
        cluster = "A100"
        resource_group = "zijianjin-rg"
        subscription_id="7b4a614f-3f5e-4896-af31-d1d20477a79c"
    
    if workspace == "AML-NorwayEast-2":
        cluster = "A100VCNorwayEast-2"
        resource_group = "AML-NorwayEast"
        subscription_id="d0c05057-7972-46ff-9bcf-3c932250155e"
 
    try:
        credential = DefaultAzureCredential()
        # Check if given credential can get token successfully.
        credential.get_token("https://management.azure.com/.default")
    except Exception as ex:
        print('Failed to get token with DefaultAzureCredential, fall back to InteractiveBrowserCredential', ex)
        # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work
        credential = InteractiveBrowserCredential()
 
    # get a handle to the workspace
    ml_client = MLClient(
        subscription_id=subscription_id,
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential,
    )
    cpu_cluster = None
    gpu_cluster = cluster
 
    # Get the current date and time
    now = datetime.now()
    timestamp = now.strftime("%Y-%m-%d_%H-%M-%S")
    command_str = """
    FORCE_TORCHRUN=1 llamafactory-cli train examples/train_full/llama3_full_sft_ds3.yaml
    """
   
    disply_name = "llama_factory"
    training_job = command(
        # local path where the code is stored
        code="./",
        # describe the command to run the python script, with all its parameters
        # use the syntax below to inject parameter values from code
        command=command_str,
        inputs={},
        outputs={
            "datastore_dir": Output(
                type="uri_folder",
                path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/",
                # path="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/AML-NorwayEast/workspaces/AML-NorwayEast/datastores/workspaceblobstore/paths/ryangabriel/",
                # path="azureml://datastores/workspaceblobstore/paths/tutorial-datasets/places2/train/",
                mode="rw_mount",
            ),
        },
        environment="azureml://registries/zijian/environments/LlamaFactoryV7/versions/1",
        environment_variables= {
            "HF_TOKEN":"*************************************",
        },
        compute=gpu_cluster,
        services={
        "My_jupyterlab": JupyterLabJobService(
            nodes="all" # For distributed jobs, use the `nodes` property to pick which node you want to enable interactive services on. If `nodes` are not selected, by default, interactive applications are only enabled on the head node. Values are "all", or compute node index (for ex. "0", "1" etc.)
        ),
        "My_vscode": VsCodeJobService(
            nodes="all"
        ),
        "My_tensorboard": TensorBoardJobService(
            nodes="all",
            log_dir="output/tblogs"  # relative path of Tensorboard logs (same as in your training script)         
        ),
        "My_ssh": SshJobService(
            ssh_public_keys="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCn99rZ6I36RZ4bqauynJVhTPMPN5lbuhPqErGW3JWr4MByJL11zdDqWW/scIOKEX3gUWuQUeoetGNmjp1kfEybTSkXQeC1NqoSz1d6z4fwd66Sm/G2YCDeFod5YRv/BDWVP/JO+Qrk1yANVGJ5YICZsgNTJJzHfzk+H7u1DLZ8YcAKgsvfctkLLZMi4jWwVwDjCzskV3v9ZAwq4+pWV58lVPZaGAkK/qYHxMsj0G8LMxfswMu1Zz4E71bHOeiAzggFnuTJUFtuV4+jbWgxszEs/GCVddJl31Fu8cO2Peudajj+FNl/ZGYYaT0ApNyqfOXUBt1+AY05n6p91tlATkBX4bjZy9ZjXveTMOGl6jbOQFdj0Im9sIviO9dvMj47XW3V6a7YlXckUynbhSus8E0aRxRIBIGtyw0BvFKhN/oQqvZnzv92mRL+ESZix8WBg2nN+Jy5jNSadz/MusqhhAJXuymIy1FsbIVOHkKBK3hktvxo70OcLejaagn49m7vHcs= azureuser@zijianjin1",
            nodes="all"  
        ),
        },
        # compute="gpu-compute2",
        distribution={
        "type": "PyTorch",
        # set process count to the number of gpus on the node
        # NC6 has only 1, A100 has 8
        "process_count_per_instance": 8,
    },
    # set instance count to the number of nodes you want to use
    instance_count=1,
    display_name=f'{disply_name}_{timestamp}',
    description="llama factory finetuning",
)
 
    # submit the job
    returned_job = ml_client.jobs.create_or_update(
        training_job,
        # Project's name
        experiment_name="llama_factory_test",
    )
 
    # get a URL for the status of the job
    print("The url to see your live job running is returned by the sdk:")
    print(returned_job.studio_url)
    # open the browser with this url
    # webbrowser.open(returned_job.studio_url)
 
    # print the pipeline run id
    print(
        f"The pipeline details can be access programmatically using identifier: {returned_job.name}"
    )
    # saving it for later in this notebook
    small_scale_run_id = returned_job.name