"""Utility functions for loading files from local paths or Azure ML data assets."""

import os
import json
import tempfile
from typing import Optional, Dict, Any, List
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential
from azure.ai.ml.entities import Data
from azure.ai.ml.constants import AssetTypes


def download_from_data_asset(ml_client: MLClient, data_asset_name: str, version: str = None) -> str:
    """Download file from Azure ML data asset and return local path.
    
    Args:
        ml_client: Azure ML client instance
        data_asset_name: Name of the data asset
        version: Optional version of the data asset
        
    Returns:
        Path to the downloaded file
    """
    # Get the data asset
    if version:
        data_asset = ml_client.data.get(name=data_asset_name, version=version)
    else:
        data_asset = ml_client.data.get(name=data_asset_name, label="latest")
    
    # Download to a temporary directory
    download_path = data_asset.download(target_path=tempfile.mkdtemp())
    
    # Find the jsonl file in the downloaded directory
    for root, dirs, files in os.walk(download_path):
        for file in files:
            if file.endswith('.jsonl'):
                return os.path.join(root, file)
    
    raise FileNotFoundError(f"No .jsonl file found in data asset {data_asset_name}")


def download_from_uri(ml_client: MLClient, uri: str) -> str:
    """Download file from Azure ML URI and return local path.
    
    Args:
        ml_client: Azure ML client instance
        uri: Full Azure ML URI path
        
    Returns:
        Path to the downloaded file
    """
    from azure.storage.blob import BlobServiceClient
    import re
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    
    # Parse the URI
    # Format: azureml://subscriptions/{sub}/resourcegroups/{rg}/workspaces/{ws}/datastores/{ds}/paths/{path}
    pattern = r"azureml://subscriptions/([^/]+)/resourcegroups/([^/]+)/workspaces/([^/]+)/datastores/([^/]+)/paths/(.+)"
    match = re.match(pattern, uri)
    
    if not match:
        raise ValueError(f"Invalid Azure ML URI format: {uri}")
    
    subscription_id, resource_group, workspace_name, datastore_name, blob_path = match.groups()
    
    # Get the datastore
    datastore = ml_client.datastores.get(datastore_name)
    
    # Get account name and container name from datastore
    account_name = datastore.account_name
    container_name = datastore.container_name
    
    # Extract filename from path
    filename = os.path.basename(blob_path)
    local_path = os.path.join(temp_dir, filename)
    
    # Create blob service client using account key or managed identity
    if hasattr(datastore.credentials, 'account_key'):
        # Use account key if available
        account_key = datastore.credentials.account_key
        blob_service_client = BlobServiceClient(
            account_url=f"https://{account_name}.blob.core.windows.net",
            credential=account_key
        )
    else:
        # Use managed identity or default credentials
        blob_service_client = BlobServiceClient(
            account_url=f"https://{account_name}.blob.core.windows.net",
            credential=ml_client._credential
        )
    
    # Download the blob
    blob_client = blob_service_client.get_blob_client(
        container=container_name,
        blob=blob_path
    )
    
    with open(local_path, "wb") as download_file:
        download_file.write(blob_client.download_blob().readall())
    
    return local_path


def load_file_or_data_asset(file_path: str, ml_client: MLClient = None) -> str:
    """Load file from local path, Azure ML data asset, or Azure ML URI.
    
    Args:
        file_path: Local file path, Azure ML data asset reference (format: azureml:name:version),
                   or full Azure ML URI (format: azureml://subscriptions/.../paths/...)
        ml_client: Optional MLClient instance for Azure ML operations
        
    Returns:
        Path to the file (local path or downloaded temporary path)
    """
    if file_path.startswith("azureml://"):
        # Full Azure ML URI
        if not ml_client:
            raise ValueError("MLClient required for loading from Azure ML URI")
        
        return download_from_uri(ml_client, file_path)
    
    elif file_path.startswith("azureml:"):
        # Data asset reference (format: azureml:data_asset_name:version)
        parts = file_path.split(":")
        data_asset_name = parts[1]
        version = parts[2] if len(parts) > 2 else None
        
        if not ml_client:
            raise ValueError("MLClient required for loading data assets")
        
        return download_from_data_asset(ml_client, data_asset_name, version)
    else:
        # Local file path
        return file_path


def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from a JSONL file.
    
    Args:
        file_path: Path to the JSONL file
        
    Returns:
        List of dictionaries loaded from the file
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                data.append(json.loads(line))
    return data


def load_jsonl_as_dict(file_path: str, key_field: str = 'instance_id') -> Dict[str, Dict[str, Any]]:
    """Load JSONL file and create a dictionary mapping by a specified key field.
    
    Args:
        file_path: Path to the JSONL file
        key_field: Field to use as dictionary key (default: 'instance_id')
        
    Returns:
        Dictionary mapping key_field values to the full records
    """
    data = load_jsonl(file_path)
    result = {}
    for item in data:
        key = item.get(key_field)
        if key:
            result[key] = item
    return result


def load_predictions(file_path: str, ml_client: MLClient = None) -> Dict[str, Dict[str, Any]]:
    """Load prediction data from local file or Azure ML data asset.
    
    Args:
        file_path: Path to predictions file or Azure ML data asset reference
        ml_client: Optional MLClient instance for Azure ML operations
        
    Returns:
        Dictionary mapping instance_id to prediction data
    """
    # Resolve file path (download from Azure ML if needed)
    resolved_path = load_file_or_data_asset(file_path, ml_client)
    
    # Load predictions as dictionary
    return load_jsonl_as_dict(resolved_path, key_field='instance_id')


def load_trajectory_data(file_path: str, ml_client: MLClient = None) -> Dict[str, Dict[str, Any]]:
    """Load trajectory data from local file or Azure ML data asset.
    
    Args:
        file_path: Path to trajectory file or Azure ML data asset reference
        ml_client: Optional MLClient instance for Azure ML operations
        
    Returns:
        Dictionary mapping instance_id to trajectory data
    """
    # Resolve file path (download from Azure ML if needed)
    resolved_path = load_file_or_data_asset(file_path, ml_client)
    
    # Load trajectories as dictionary
    return load_jsonl_as_dict(resolved_path, key_field='instance_id')


def get_ml_client(subscription_id: str, resource_group: str, workspace: str) -> MLClient:
    """Create and return an MLClient instance.
    
    Args:
        subscription_id: Azure subscription ID
        resource_group: Resource group name
        workspace: Workspace name
        
    Returns:
        MLClient instance
    """
    credential = DefaultAzureCredential()
    return MLClient(
        subscription_id=subscription_id,
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential
    )