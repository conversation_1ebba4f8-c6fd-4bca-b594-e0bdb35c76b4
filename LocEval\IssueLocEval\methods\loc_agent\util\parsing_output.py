import re
import json
import logging
import collections
from collections import Counter
from typing import List, Tu<PERSON>, Dict
from pathlib import Path
import pickle
import os

# Import the graph-related modules
from dependency_graph import RepoEntitySearcher
from dependency_graph.build_graph import (
    NODE_TYPE_FILE,
    NODE_TYPE_FUNCTION,
    NODE_TYPE_CLASS,
)

## NOTE: This file assumes dependency on other modules of Loc Agent. Will need to combine a clean loc agent codebase here.

# Add this line to read from environment variable
GRAPH_INDEX_DIR = os.environ.get(
    "GRAPH_INDEX_DIR", "index_data/SWE-bench_Lite/graph_index_v2.3"
)


def extract_python_file_path(line: str) -> str:
    """
    Extracts the Python file path from a given line of text.
    """
    # Pattern to match file paths ending with .py
    pattern = r"[\w\./\\-]+\.py"

    # Search for the pattern in the line
    match = re.search(pattern, line)

    if match:
        matched_fp = match.group(0)
        # Normalize to forward slashes
        matched_fp = matched_fp.replace("\\", "/")
        return matched_fp
    return None


def parse_raw_loc_output(raw_output: str) -> Tuple[List[str], List[str], List[str]]:
    """
    Parse raw LLM output to extract files, modules, and entities.
    Honors the order in the raw output without graph searching.
    """
    file_list = []
    module_list = []
    entity_list = []

    # Remove triple backticks and surrounding whitespace
    raw_output = raw_output.strip("` \n")

    current_file = None
    current_module = None

    # Split the input data into lines
    lines = raw_output.strip().split("\n")

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Check if line contains a Python file
        if ".py" in line and not line.startswith(
            ("function:", "class:", "method:", "line:", "lines:", "variable:")
        ):
            file_path = extract_python_file_path(line)
            if file_path:
                current_file = file_path
                if current_file not in file_list:
                    file_list.append(current_file)

                # Check if the line has a module specification like "file.py:ModuleName"
                if ":" in line and line.index(":") > line.index(".py"):
                    # Extract module name after the file path
                    parts = line.split(":", 1)
                    if len(parts) > 1 and parts[1].strip():
                        module_name = parts[1].strip()
                        # Remove any trailing content after module name
                        module_name = module_name.split()[0]
                        current_module = f"{current_file}:{module_name}"
                        if current_module not in module_list:
                            module_list.append(current_module)

        # Handle class definitions
        elif line.startswith("class:") and current_file:
            class_name = line[6:].strip().split()[0]
            module_id = f"{current_file}:{class_name}"
            current_module = module_id
            if module_id not in module_list:
                module_list.append(module_id)

        # Handle function/method definitions
        elif line.startswith(("function:", "method:")) and current_file:
            func_line = line.split(":", 1)[1].strip()
            func_name = func_line.split("(")[0].split()[0]

            # Check if it's a method (has a dot)
            if "." in func_name:
                # It's a method like Class.method
                entity_id = f"{current_file}:{func_name}"
            elif current_module and ":" in current_module:
                # It's a method of the current class
                class_name = current_module.split(":")[1]
                entity_id = f"{current_file}:{class_name}.{func_name}"
            else:
                # It's a standalone function
                entity_id = f"{current_file}:{func_name}"

            if entity_id not in entity_list:
                entity_list.append(entity_id)

    return file_list, module_list, entity_list


def enhance_with_graph_search(
    found_files: List[str],
    found_modules: List[str],
    found_entities: List[str],
    searcher: RepoEntitySearcher,
) -> Tuple[List[str], List[str], List[str]]:
    """
    Enhance the found locations with graph-based search, similar to process_output.py.
    This adds related modules and entities based on the graph structure.
    """
    enhanced_modules = list(found_modules)  # Start with existing modules
    enhanced_entities = list(found_entities)  # Start with existing entities

    # Process entities to find parent modules (similar to get_edit_entities_from_raw_locs)
    for entity in found_entities:
        if searcher.has_node(entity):
            entity_data = searcher.get_node_data([entity])[0]

            # Extract parent module/class for methods
            if "." in entity.split(":")[-1]:
                parent_module = ".".join(entity.split(".")[:-1])
                if (
                    searcher.has_node(parent_module)
                    and parent_module not in enhanced_modules
                ):
                    enhanced_modules.append(parent_module)

    # Look for additional entities in found files
    for file_path in found_files:
        if not searcher.has_node(file_path):
            continue

        # Get all functions/classes in this file
        file_nodes = []
        for nid in searcher.G.nodes():
            if nid.startswith(file_path + ":"):
                node = searcher.G.nodes[nid]
                if node["type"] in [NODE_TYPE_FUNCTION, NODE_TYPE_CLASS]:
                    file_nodes.append(nid)

        # Add highly connected nodes from this file
        for node_id in file_nodes:
            # Check if this node has many connections (indicating it's important)
            if node_id in searcher.G:
                degree = searcher.G.degree(node_id)
                # Add nodes with high connectivity that aren't already in our lists
                if degree > 3:  # Threshold for "important" nodes
                    node = searcher.G.nodes[node_id]
                    if (
                        node["type"] == NODE_TYPE_CLASS
                        and node_id not in enhanced_modules
                    ):
                        enhanced_modules.append(node_id)
                    elif (
                        node["type"] == NODE_TYPE_FUNCTION
                        and node_id not in enhanced_entities
                    ):
                        enhanced_entities.append(node_id)

    return found_files, enhanced_modules, enhanced_entities


def get_loc_results_from_raw_output(
    instance_id: str, raw_output: str, use_graph_enhancement: bool = True
) -> Tuple[List[str], List[str], List[str]]:
    """
    Process a single raw output string and return found files, modules, and entities.

    Args:
        instance_id: The instance ID for loading the graph
        raw_output: The raw LLM output
        use_graph_enhancement: Whether to enhance results with graph search
    """
    if not raw_output:
        return [], [], []

    # First, parse the raw output directly
    found_files, found_modules, found_entities = parse_raw_loc_output(raw_output)

    # Then enhance with graph search if requested
    if use_graph_enhancement:
        try:
            # Load the graph for this instance
            graph_path = Path(GRAPH_INDEX_DIR) / f"{instance_id}.pkl"
            if graph_path.exists():
                with open(graph_path, "rb") as f:
                    G = pickle.load(f)
                searcher = RepoEntitySearcher(G)

                # Enhance the results with graph search
                found_files, found_modules, found_entities = enhance_with_graph_search(
                    found_files, found_modules, found_entities, searcher
                )
            else:
                logging.warning(
                    f"Graph not found for instance {instance_id}: {graph_path}"
                )
        except Exception as e:
            logging.error(f"Error loading graph for {instance_id}: {e}")

    return found_files, found_modules, found_entities


def process_loc_trajs_file(
    input_file: str, output_file: str, use_graph_enhancement: bool = True
):
    """
    Process a loc_trajs.jsonl file and create a new loc_outputs.jsonl file.

    Args:
        input_file: Path to input loc_trajs.jsonl
        output_file: Path to output loc_outputs.jsonl
        use_graph_enhancement: Whether to use graph-based enhancement
    """
    results = []

    with open(input_file, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
                instance_id = data.get("instance_id", "")

                # The structure seems to have raw_output_loc at the top level
                raw_output_loc = data.get("raw_output_loc", None)

                if raw_output_loc:
                    # Process the raw output
                    if isinstance(raw_output_loc, list):
                        # If it's a list, take the first element
                        raw_output_str = raw_output_loc[0] if raw_output_loc else ""
                    else:
                        raw_output_str = raw_output_loc

                    found_files, found_modules, found_entities = (
                        get_loc_results_from_raw_output(
                            instance_id, raw_output_str, use_graph_enhancement
                        )
                    )

                    # Create result entry matching the original format
                    result = {
                        "instance_id": instance_id,
                        "found_files": [
                            found_files
                        ],  # Wrap in list to match original format
                        "found_modules": [found_modules],
                        "found_entities": [found_entities],
                        "raw_output_loc": [raw_output_str],
                        "meta_data": data.get("meta_data", {}),
                    }
                    results.append(result)
                else:
                    logging.warning(
                        f"No raw_output_loc found for instance {instance_id}"
                    )

            except json.JSONDecodeError as e:
                logging.error(f"Error parsing JSON: {e}")
                continue
            except Exception as e:
                logging.error(f"Error processing instance {instance_id}: {e}")
                continue

    # Write results to output file
    with open(output_file, "w", encoding="utf-8") as f:
        for result in results:
            json.dump(result, f)
            f.write("\n")

    print(f"Processed {len(results)} instances")
    print(f"Output written to: {output_file}")


if __name__ == "__main__":
    # Example usage
    input_file = "../results/czSWEBenchLite/GPT4-1-mini/loc_trajs.jsonl"

    # Generate two outputs: one without graph enhancement, one with
    output_file_simple = "../results/czSWEBenchLite/GPT4-1-mini/loc_outputs_alt.jsonl"
    output_file_enhanced = (
        "../results/czSWEBenchLite/GPT4-1-mini/loc_outputs_enhanced.jsonl"
    )

    print("Processing without graph enhancement...")
    process_loc_trajs_file(input_file, output_file_simple, use_graph_enhancement=False)

    print("\nProcessing with graph enhancement...")
    process_loc_trajs_file(input_file, output_file_enhanced, use_graph_enhancement=True)
