Instructions for Running a Completion Evaluation Pipeline
Introduction
This document provides step-by-step instructions for running a completion evaluation pipeline.The document includes setup, configuration, model/secret handling, pipeline execution, and troubleshooting.
Setup
1. Clone the repository containing the pipeline code.
2. Ensure you have the necessary dependencies installed. Refer to the repository's README for installation instructions.
3. Set up your development environment with the required tools and libraries.
Configuration
1. Update the pipeline configuration file with the appropriate settings.
2. Set the model name, secret name, and OpenAI key in the pipeline variables.
   - Model name: `xtab_274_gpt41mini_model10`
   - Secret name: `copilot-ppe-centralus-xtab`
   - OpenAI key: Retrieve the key from the keyvault and place it in `openai_key_ado`.
Model/Secret Handling
1. Ensure the model endpoint is correctly set in the pipeline configuration.
   - Endpoint: `https://copilot-ppe-centralus-xtab.openai.azure.com/openai/deployments/xtab_274_gpt41mini_model10/chat/completions?api-version=2025-01-01-preview`
2. Retrieve the secret value from the keyvault and set it in the pipeline variables.
3. Verify that the model and secret names are correctly configured in the pipeline.
Pipeline Execution
1. Run the pipeline using the configured settings.
2. Monitor the pipeline execution and check for any errors or issues.
3. Once the pipeline execution is complete, review the generated artifacts and reports.
4. If running locally, use the same values set as default in the pipeline variables for the model and secret name.
Troubleshooting
1. If you encounter authentication errors, ensure that the OpenAI key is correctly set.
2. Verify that the model name and secret name are correctly configured in the pipeline variables.
3. Check the pipeline logs for any error messages and resolve the issues accordingly.
4. If the default model value set in the pipeline variables is no longer available, update it with the correct model name and secret name.
