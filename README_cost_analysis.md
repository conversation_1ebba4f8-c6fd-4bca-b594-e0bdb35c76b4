# Cost and Latency Analysis Tool

This tool extracts cost and latency information from `loc_trajs.jsonl` files and provides detailed analysis.

## Prerequisites

**Python Installation Required**: This tool requires Python 3.6 or higher.

### Installing Python (if not already installed):
1. Go to https://www.python.org/downloads/
2. Download the latest Python version
3. **Important**: During installation, check the box "Add Python to PATH"
4. Complete the installation

## Files Included

- `extract_cost_latency.py` - Main analysis script
- `run_analysis.bat` - Windows batch file for easy execution
- `README_cost_analysis.md` - This documentation

## Usage

### Option 1: Using the Batch File (Windows - Recommended)
```cmd
run_analysis.bat "path\to\your\loc_trajs.jsonl"
```

Example:
```cmd
run_analysis.bat "LocEval\example_results\experiment1\gpt-4.1-mini-czlll-swebenchlite\loc_trajs.jsonl"
```

### Option 2: Direct Python Command
```cmd
python extract_cost_latency.py "path\to\your\loc_trajs.jsonl"
```

## Command Line Options

### Basic Analysis (Summary Only)
```cmd
python extract_cost_latency.py loc_trajs.jsonl
```

### Detailed Analysis (Show per-instance data)
```cmd
python extract_cost_latency.py loc_trajs.jsonl --detailed
```

### Limited Detailed Analysis (Show first N entries)
```cmd
python extract_cost_latency.py loc_trajs.jsonl --detailed 10
```

### Export to CSV
```cmd
python extract_cost_latency.py loc_trajs.jsonl --export metrics.csv
```

### Combined Options
```cmd
python extract_cost_latency.py loc_trajs.jsonl --detailed 5 --export results.csv
```

## Output Information

The tool provides:

### Summary Statistics
- **Cost Statistics**: Total cost, average, median, min/max costs
- **Latency Statistics**: Total time, average, median, min/max latency
- **Token Statistics**: Total tokens, average per request, prompt vs completion tokens

### Detailed Report (Optional)
- Per-instance breakdown showing:
  - Instance ID
  - Cost per request
  - Latency per request  
  - Token count per request

### CSV Export (Optional)
- Exports all data to a CSV file for further analysis in Excel or other tools
- Includes: instance_id, cost, latency, prompt_tokens, completion_tokens, total_tokens

## Example Output

```
============================================================
COST AND LATENCY ANALYSIS SUMMARY
============================================================
Total entries processed: 11

COST STATISTICS:
  Entries with cost data: 11
  Total cost: $0.035891
  Average cost: $0.003263
  Median cost: $0.003295
  Min cost: $0.001658
  Max cost: $0.004761

LATENCY STATISTICS:
  Entries with latency data: 11
  Total latency: 12.345 seconds
  Average latency: 1.122 seconds
  Median latency: 0.963 seconds
  Min latency: 0.665 seconds
  Max latency: 2.718 seconds

TOKEN STATISTICS:
  Entries with token data: 11
  Total tokens: 7,234
  Average tokens per request: 657.6
  Median tokens per request: 657.0
  Min tokens: 330
  Max tokens: 950
  Average prompt tokens: 534.5
  Average completion tokens: 123.1
```

## Troubleshooting

### "Python not found" Error
- Make sure Python is installed
- Verify Python was added to PATH during installation
- Try running `python --version` in Command Prompt to test

### File Not Found Error
- Check the file path is correct
- Use quotes around the file path if it contains spaces
- Make sure you're in the correct directory

### Permission Errors
- Make sure you have read access to the input file
- For CSV export, ensure you have write access to the output directory

## File Format Expected

The tool expects JSONL files where each line contains a JSON object with this structure:
```json
{
  "instance_id": "example-id",
  "usage": {
    "prompt_tokens": 534,
    "completion_tokens": 113,
    "cost": 0.00268695
  },
  "time": 2.7180941104888916
}
```
