"""Method registry for managing available methods."""

from typing import Dict, Type, Optional, Any
from .direct_prediction.direct_prediction import DirectPredictionMethod


class MethodRegistry:
    """Registry for managing available localization methods."""

    _methods: Dict[str, Type] = {
        "direct_prediction": DirectPredictionMethod,
    }

    @classmethod
    def register(cls, name: str, method_class: Type) -> None:
        """Register a new method.

        Args:
            name: Name to register the method under
            method_class: Method class to register
        """
        cls._methods[name] = method_class

    @classmethod
    def get(
        cls,
        name: str,
        model_name: str = "gpt-4",
        config: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """Get a method instance by name.

        Args:
            name: Name of the method
            model_name: Model name for LLM-based methods
            config: Optional configuration

        Returns:
            Method instance

        Raises:
            ValueError: If method name is not registered
        """
        if name not in cls._methods:
            available = list(cls._methods.keys())
            raise ValueError(
                f"Method '{name}' not found. Available methods: {available}"
            )

        method_class = cls._methods[name]
        return method_class(model_name=model_name, config=config)

    @classmethod
    def list_available(cls) -> Dict[str, Dict[str, Any]]:
        """List all available methods with their information.

        Returns:
            Dictionary mapping method names to their information
        """
        info = {}

        for name, method_class in cls._methods.items():
            info[name] = {
                "class": method_class.__name__,
                "module": method_class.__module__,
                "description": method_class.__doc__.strip()
                if method_class.__doc__
                else "No description available",
            }

        return info
