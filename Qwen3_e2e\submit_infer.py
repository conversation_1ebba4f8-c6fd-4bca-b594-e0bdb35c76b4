from azure.identity import DefaultAzureCredential, InteractiveBrowserCredential
from azure.ai.ml import command
from azure.ai.ml import Input, Output
from azure.ai.ml.entities import ResourceConfiguration
from azure.ai.ml import MLClient
from datetime import datetime
from argparse import ArgumentParser
from azure.ai.ml.entities import JupyterLabJobService, VsCodeJobService, TensorBoardJobService, SshJobService
import os
import webbrowser
from aml.endpoint.manage import download_data_asset

from azure.ai.ml import MLClient, command, Input
from azure.ai.ml.constants import AssetTypes, InputOutputModes

import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
print(sys.path)
os.environ["VLLM_LOGGING_LEVEL"] = "DEBUG"
# Optionally disable NCCL P2P
os.environ["NCCL_DEBUG"] = "TRACE"
os.environ["VLLM_TRACE_FUNCTION"] = "1"

if __name__=='__main__':
 
    parser = ArgumentParser()
    parser.add_argument('--cluster', type=str, default="h100centralusvc")
    parser.add_argument('--resource_group', type=str, default="SingularityH100")
    parser.add_argument('--workspace', type=str, default="H100CentralUS")
    parser.add_argument('--subscription_id', type=str, default="d0c05057-7972-46ff-9bcf-3c932250155e")
    parser.add_argument('--model_name', type=str, default="qwen7b_ep1")
    parser.add_argument('--data_asset_version', type=str, default="1")
    parser.add_argument('--data_asset_path', type=str, default="azureml://datastores/workspaceblobstore/paths/alex/qwen25_coder_7b_ep3/")
    parser.add_argument('--data_asset_local_path', type=str, default="./data_asset_download/")
    
    args = parser.parse_args()
 
    resource_group = args.resource_group
    workspace = args.workspace
    subscription_id = args.subscription_id
    cluster_name = args.cluster
 
    try:
        credential = DefaultAzureCredential()
        # Check if given credential can get token successfully.
        credential.get_token("https://management.azure.com/.default")
    except Exception as ex:
        print('Failed to get token with DefaultAzureCredential, fall back to InteractiveBrowserCredential', ex)
        # Fall back to InteractiveBrowserCredential in case DefaultAzureCredential not work
        credential = InteractiveBrowserCredential()
 
    # get a handle to the workspace
    ml_client = MLClient(
        subscription_id=subscription_id,
        resource_group_name=resource_group,
        workspace_name=workspace,
        credential=credential,
    )
    
    cpu_cluster = None
    gpu_cluster = args.cluster
    vc_cluster = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.MachineLearningServices/virtualclusters/{cluster_name}"
 
    # Get the current date and time
    now = datetime.now()
    timestamp = now.strftime("%Y-%m-%d_%H-%M-%S")
    command_str = """
    export DISABLE_VERSION_CHECK=1
    pip install --upgrade peft==0.15.1
    pip install --force-reinstall transformers==4.51.0
    pip install --upgrade "vllm>=0.8.4"

    python3 scripts/vllm_infer.py \
    --model_name_or_path ${{inputs.datastore_dir}} \
    --dataset test_apply_2step \
    --template qwen \
    --save_name ${{outputs.datastore_dir}}/generated_predictions.jsonl
    echo "Inference completed. Check the generated predictions at ${{outputs.datastore_dir}}/generated_predictions.jsonl"
    exit 0
    """
    # command_str = """
    # sleep infinity
    # """
   
    disply_name = "llama_factory_xh"
    training_job = command(
        # local path where the code is stored
        code="./",
        # describe the command to run the python script, with all its parameters
        # use the syntax below to inject parameter values from code
        command=command_str,
        inputs={
            "datastore_dir": Input(
                type="uri_folder",
                path=f"azureml:azureml_willing_coconut_rjvc0bwyjz_output_data_datastore_dir:1",
                # path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/alex/llama33_nostop_8b/",
                # path="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/AML-NorwayEast/workspaces/AML-NorwayEast/datastores/workspaceblobstore/paths/ryangabriel/",
                # path="azureml://datastores/workspaceblobstore/paths/tutorial-datasets/places2/train/",
                mode="rw_mount",
            ),
        },
        outputs={
            "datastore_dir": Output(
                type="uri_folder",
                path=f"azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/{resource_group}/workspaces/{workspace}/datastores/workspaceblobstore/paths/xhou/upgrade_sft_inference_{timestamp}/",
                # path="azureml://subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourcegroups/AML-NorwayEast/workspaces/AML-NorwayEast/datastores/workspaceblobstore/paths/ryangabriel/",
                # path="azureml://datastores/workspaceblobstore/paths/tutorial-datasets/places2/train/",
                mode="rw_mount",
            ),
        },
        environment="azureml://registries/zijian/environments/LlamaFactoryOptimized/versions/8",
        environment_variables= {
            "HF_TOKEN":"*************************************",
            "_AZUREML_SINGULARITY_JOB_UAI":"/subscriptions/d0c05057-7972-46ff-9bcf-3c932250155e/resourceGroups/SingularityH100/providers/Microsoft.ManagedIdentity/userAssignedIdentities/singularityh100",
            "CUDA_VISIBLE_DEVICES": "0,1,2,3,4,5,6,7",  # All 8 GPUs
            "VLLM_TENSOR_PARALLEL_SIZE": "8",  # Use all 8 GPUs
        },
        compute=vc_cluster,
        resources={
            "instance_count": 1,
            "instance_type": "Singularity.ND96_H100_v5",
            "properties": {
                "singularity": {
                    "interactive": True,
                    "imageVersion": '',
                    "slaTier": "Premium",
                    "priority": "high",
                    "tensorboardLogDirectory": "/scratch/tensorboard_logs",
                    "enableAzmlInt": False,
                }
            },
        },
        services={
        "My_jupyterlab": JupyterLabJobService(
            # nodes="all" # For distributed jobs, use the `nodes` property to pick which node you want to enable interactive services on. If `nodes` are not selected, by default, interactive applications are only enabled on the head node. Values are "all", or compute node index (for ex. "0", "1" etc.)
        ),
        "My_vscode": VsCodeJobService(
            # nodes="all"
        ),
        "My_tensorboard": TensorBoardJobService(
            # nodes="all",
            log_dir="output/tblogs"  # relative path of Tensorboard logs (same as in your training script)         
        ),
        "My_ssh": SshJobService(
            ssh_public_keys="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDE0nI+zXB9QPBVaeNztf+v7e7b+bjhbAHw3RHh5aD+yZo8f8CWnnjajkpsDPeOOolwFJGhUX+Zi02Zn1KIL2jZTSGI3/WHGAko66LbRVPn4VGROYjv90SwPBA0AUcCvBEuP5V1dYEe+LRPgrM9WoaANnqGIpVZbXXKPbVS6ZyblPkTwIcN5jRGjauHMnx4NPzjJzWJV2V1yNJZ1MiWuuQWMv8WygjZmO5bV6R2QCxSsOupiwHee3T7qBtMrr/U4134trGHHGtHipWcg4xgxNGoJ9T/emGrt4FnSDZueH1qmC/gqubBm/VSM9d9He7QYqcQPepIZxrlayR6mHVtiuDyWzm0oI2w35GG3oA8y2dmPH9FTvuuSCN/eYxMPnvwHl/XwtamFrZz63pC6hbSmynFEFRX2jQVci+0kxpDhW1EjuwrskXq3LSbhLIhd1U1G1+q3wlxPxijqVJxkqS7MKvtzduvr+dX36oNVrSUyHfe9RJiQhArY1KMWgq45GPHaHU0t/izMZbCuwW9A4THZOSLFH0bTVJDEuXm5YKmz9OiKT/AOEUzBEeOABTtJdjUBGAIdQT0iLqohHuT9pJ+HrE15M7dlo6cSzb0ZIuVmFtKKcUBPLdbE0rA2u8Cgbbg/zw2Zqkov3lyYDZDFVP5cMix0yeSqV6Z18HgJiKRFReviQ== <EMAIL>",
            # nodes="all"  
        ),
        },
        # compute="gpu-compute2", # comment out for efficiency in inference
        distribution={
        "type": "PyTorch",
        # set process count to the number of gpus on the node
        # NC6 has only 1, A100 has 8
        "process_count_per_instance": 1,
    },
    # set instance count to the number of nodes you want to use
    instance_count=1,
    display_name=f'{disply_name}_{timestamp}',
    description="llama factory finetuning",
)
 
    # submit the job
    returned_job = ml_client.jobs.create_or_update(
        training_job,
        # Project's name
        experiment_name="llama_factory_test",
    )
 
    # get a URL for the status of the job
    print("The url to see your live job running is returned by the sdk:")
    print(returned_job.studio_url)
    # open the browser with this url
    # webbrowser.open(returned_job.studio_url)
 
    # print the pipeline run id
    print(
        f"The pipeline details can be access programmatically using identifier: {returned_job.name}"
    )
    # saving it for later in this notebook
    small_scale_run_id = returned_job.name