import json
import pandas as pd
import os

os.makedirs("data", exist_ok=True)

datas = []
with open("maas_data_1000.jsonl") as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        messages = json.loads(line)["messages"]
        prompt_msg = messages[0]
        data = {
            "model": "",
            "messages": [prompt_msg],
            "temperature": 0.0,
            "top_p": 1.0,
            "max_tokens": 10000,
        }
        with open(f"data/example_{str(i).rjust(len(str(len(lines))), '0')}.json", "w") as of:
            json.dump(data, of)
